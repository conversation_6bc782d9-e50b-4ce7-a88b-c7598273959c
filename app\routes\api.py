from flask import Blueprint, request, jsonify, current_app
from flask_login import current_user
import os
import logging
import json
import requests
from datetime import datetime
from werkzeug.utils import secure_filename
from app.services.query_service import query_category
from app.services.embedding_service import embed_file, scrape_url
from app.utils import helpers as utils
from app.utils import database as db_utils
from app.services import geo_service as geo_utils
from app.services import geo_analytics as geoip_analytics
from app.services.vector_db import get_vector_db, OLLAMA_BASE_URL, _chroma_cache
from app.utils.helpers import list_categories, delete_file, check_duplicate_pdf
from app.utils.rag_performance import get_rag_monitor
from app.services.cache_service import cache_service
import ollama
from scripts.setup.create_temp_dirs import create_temp_directories
from langchain_ollama.embeddings import OllamaEmbeddings
from app.utils.embedding_db import embed_file_db_first, scrape_url_db_first
from app.routes.auth import admin_required, function_permission_required
from app.utils.config import save_default_models, update_env_file
from flask_wtf import CSRFProtect

api_bp = Blueprint('api_bp', __name__, url_prefix='/api')

logger = logging.getLogger(__name__)
csrf = CSRFProtect()


@api_bp.route('/check_duplicate', methods=['POST'])
@csrf.exempt
@function_permission_required('upload_files')
def check_duplicate():
    logger.info(f"Check duplicate API called - Content-Type: {request.content_type}, User: {current_user}")
    try:
        # Get file and category from form data or JSON
        if request.content_type == 'application/json':
            data = request.get_json()
            filename = data.get('filename')
            category = data.get('category')
            file_obj = None
            logger.info(f"JSON request - Filename: {filename}, Category: {category}")
            if not filename:
                return jsonify({'error': 'Filename is required.'}), 400
        else:  # Handle multipart/form-data
            file_obj = request.files.get('file')
            filename = None
            if file_obj and file_obj.filename:
                filename = secure_filename(file_obj.filename)
                # Reset file pointer to beginning in case it was read already
                file_obj.seek(0)
            category = request.form.get('category')
            logger.info(f"Form data request - Filename: {filename}, Category: {category}")

        if not filename:
            logger.warning("No filename provided in check_duplicate request")
            return jsonify({'error': 'No file provided or invalid filename'}), 400
        if not category:
            logger.warning("No category provided in check_duplicate request")
            return jsonify({'error': 'Category is required.'}), 400

        logger.info(f"Checking for duplicate - Filename: {filename}, Category: {category}")

        # If we have a file object, use it, otherwise just check by filename
        if file_obj:
            logger.info(f"Using file object for duplicate check")
            is_duplicate, duplicate_info = check_duplicate_pdf(file_obj, category)
        else:
            # If no file object, just check if a file with this name exists in the category
            logger.info(f"No file object provided, checking filesystem only")
            from app.utils.helpers import TEMP_FOLDER
            file_path = os.path.join(TEMP_FOLDER, category, filename)
            is_duplicate = os.path.exists(file_path)
            duplicate_info = None
            logger.info(f"Filesystem check - Path: {file_path}, Exists: {is_duplicate}")

        response_data = {'is_duplicate': is_duplicate}
        
        if is_duplicate and duplicate_info:
            response_data['duplicate_info'] = duplicate_info
            if duplicate_info.get('type') == 'filename_match':
                response_data['message'] = f"A file with the same name already exists in category '{category}'"
            elif duplicate_info.get('type') == 'content_match':
                response_data['message'] = f"A file with identical content already exists in category '{category}'"
            else:
                response_data['message'] = f"A duplicate file was found in category '{category}'"
        elif is_duplicate:
            response_data['message'] = f"A file with the same name already exists in category '{category}'"

        logger.info(f"Duplicate check result - Is Duplicate: {is_duplicate}")
        return jsonify(response_data)
        
    except Exception as e:
        logger.error(f"Error in check_duplicate: {str(e)}", exc_info=True)
        return jsonify({'error': f'Error checking for duplicate: {str(e)}'}), 500

@api_bp.route('/upload', methods=['POST'])
@function_permission_required('upload_files')
def upload_file():
    if 'file' not in request.files and 'url' not in request.form:
        return jsonify({'error': 'No file or URL provided'}), 400

    category = request.form.get('category', 'default')
    if not category.strip():
        return jsonify({'error': 'Category cannot be empty'}), 400

    if 'file' in request.files and request.files['file'].filename != '':
        file = request.files['file']
        filename = secure_filename(file.filename)
        if not filename.lower().endswith('.pdf'):
            return jsonify({'error': 'Only PDF files are allowed'}), 400

        try:
            embed_file_db_first(file, category, filename)
            return jsonify({'message': f'File "{filename}" uploaded and embedded successfully in category "{category}"'}), 200
        except Exception as e:
            logger.error(f"Error embedding file: {e}")
            return jsonify({'error': str(e)}), 500

    if 'url' in request.form and request.form['url'].strip() != '':
        url = request.form['url']
        try:
            scrape_url_db_first(url, category)
            return jsonify({'message': f'URL "{url}" scraped and embedded successfully in category "{category}"'}), 200
        except Exception as e:
            logger.error(f"Error scraping URL: {e}")
            return jsonify({'error': str(e)}), 500

    return jsonify({'error': 'No file or URL provided'}), 400

@api_bp.route('/categories', methods=['GET'])
@function_permission_required('view_categories')
def list_categories_route():
    try:
        return jsonify(utils.list_categories_with_details())
    except Exception as e:
        logger.error(f"Error listing categories: {e}")
        return jsonify({'error': str(e)}), 500

@api_bp.route('/categories', methods=['POST'])
@function_permission_required('manage_categories')
def create_category():
    data = request.get_json()
    category_name = data.get('category_name')
    if not category_name:
        return jsonify({'error': 'Category name is required'}), 400
    try:
        from app.utils.helpers import create_category
        success = create_category(category_name)
        if success:
            return jsonify({'message': f'Category "{category_name}" created successfully'}), 201
        else:
            return jsonify({'error': f'Failed to create category "{category_name}"'}), 500
    except Exception as e:
        logger.error(f"Error creating category: {e}")
        return jsonify({'error': str(e)}), 500

@api_bp.route('/categories/<old_category>', methods=['PUT'])
@function_permission_required('manage_categories')
def update_category(old_category):
    data = request.get_json()
    new_category = data.get('new_category')
    if not new_category:
        return jsonify({'error': 'New category name is required'}), 400
    try:
        utils.update_category_name(old_category, new_category)
        return jsonify({'message': f'Category "{old_category}" updated to "{new_category}" successfully'}), 200
    except Exception as e:
        logger.error(f"Error updating category: {e}")
        return jsonify({'error': str(e)}), 500

@api_bp.route('/categories/<category>', methods=['DELETE'])
@function_permission_required('manage_categories')
def delete_category(category):
    try:
        utils.delete_category_and_files(category)
        return jsonify({'message': f'Category "{category}" deleted successfully'}), 200
    except Exception as e:
        logger.error(f"Error deleting category: {e}")
        return jsonify({'error': str(e)}), 500

@api_bp.route('/auth/status', methods=['GET'])
def auth_status():
    """Check authentication status and permissions"""
    try:
        status = {
            'authenticated': current_user.is_authenticated,
            'user_id': getattr(current_user, 'user_id', None),
            'username': getattr(current_user, 'username', None),
            'role': getattr(current_user, 'role', None),
            'permissions': {}
        }
        
        if current_user.is_authenticated:
            # Check common permissions
            permissions_to_check = ['view_files', 'upload_files', 'manage_files', 'view_categories']
            for permission in permissions_to_check:
                status['permissions'][permission] = current_user.has_dashboard_permission(permission)
        
        return jsonify(status), 200
    except Exception as e:
        logger.error(f"Error checking auth status: {e}")
        return jsonify({'error': str(e)}), 500

@api_bp.route('/files', methods=['GET'])
@function_permission_required('view_files')
def list_files():
    logger.info(f"Files API called - Category: {request.args.get('category')}, User: {current_user}")
    category = request.args.get('category')
    if not category:
        return jsonify({'error': 'Category is required'}), 400
    try:
        files = utils.get_files_in_category(category)
        logger.info(f"Successfully retrieved {len(files)} files for category {category}")
        return jsonify(files)
    except Exception as e:
        logger.error(f"Error listing files: {e}")
        return jsonify({'error': str(e)}), 500

@api_bp.route('/files/delete', methods=['POST'])
@function_permission_required('manage_files')
def delete_file_route():
    data = request.get_json()
    filename = data.get('filename')
    category = data.get('category')
    if not filename or not category:
        return jsonify({'error': 'Filename and category are required'}), 400
    try:
        delete_file(category, filename)
        return jsonify({'message': f'File "{filename}" in category "{category}" deleted successfully'}), 200
    except Exception as e:
        logger.error(f"Error deleting file: {e}")
        return jsonify({'error': str(e)}), 500

@api_bp.route('/files/batch-delete', methods=['POST'])
@function_permission_required('manage_files')
def batch_delete_files():
    """
    Delete multiple files in a batch operation.

    Expected JSON payload:
    {
        "files": {
            "category1": [
                {"filename": "file1.pdf", "filetype": "pdf"},
                {"filename": "file2.pdf", "filetype": "pdf"}
            ],
            "category2": [
                {"filename": "file3.pdf", "filetype": "pdf"}
            ]
        }
    }

    Returns:
    {
        "success": true,
        "successful_count": 2,
        "failed_count": 1,
        "results": [...],
        "errors": [...]
    }
    """
    try:
        data = request.get_json()
        if not data or 'files' not in data:
            return jsonify({'error': 'Files data is required'}), 400

        files_by_category = data['files']
        if not isinstance(files_by_category, dict):
            return jsonify({'error': 'Files must be organized by category'}), 400

        # Validate the structure
        total_files = 0
        for category, files in files_by_category.items():
            if not isinstance(files, list):
                return jsonify({'error': f'Files for category "{category}" must be a list'}), 400
            for file_info in files:
                if not isinstance(file_info, dict) or 'filename' not in file_info:
                    return jsonify({'error': 'Each file must have a filename'}), 400
                total_files += 1

        if total_files == 0:
            return jsonify({'error': 'No files specified for deletion'}), 400

        if total_files > 100:  # Safety limit
            return jsonify({'error': 'Cannot delete more than 100 files at once'}), 400

        logger.info(f"Starting batch delete operation for {total_files} files across {len(files_by_category)} categories")

        # Process deletions
        successful_deletions = []
        failed_deletions = []
        errors = []

        for category, files in files_by_category.items():
            logger.info(f"Processing {len(files)} files in category '{category}'")

            for file_info in files:
                filename = file_info['filename']
                filetype = file_info.get('filetype', 'unknown')

                try:
                    # Delete from ChromaDB
                    utils.delete_vector_embeddings(category, filename)

                    # Delete from SQLite
                    db_utils.delete_pdf_document_records(category, filename)

                    # Use the existing delete_file function
                    success, message = delete_file(category, filename)

                    if success:
                        successful_deletions.append({
                            'category': category,
                            'filename': filename,
                            'filetype': filetype,
                            'message': message
                        })
                        logger.info(f"Successfully deleted {category}/{filename}")
                    else:
                        failed_deletions.append({
                            'category': category,
                            'filename': filename,
                            'filetype': filetype,
                            'error': message
                        })
                        errors.append(f"{category}/{filename}: {message}")
                        logger.warning(f"Failed to delete {category}/{filename}: {message}")

                except Exception as e:
                    error_msg = str(e)
                    failed_deletions.append({
                        'category': category,
                        'filename': filename,
                        'filetype': filetype,
                        'error': error_msg
                    })
                    errors.append(f"{category}/{filename}: {error_msg}")
                    logger.error(f"Exception deleting {category}/{filename}: {error_msg}")

        successful_count = len(successful_deletions)
        failed_count = len(failed_deletions)

        logger.info(f"Batch delete completed: {successful_count} successful, {failed_count} failed")

        # Determine overall success
        overall_success = successful_count > 0

        response_data = {
            'success': overall_success,
            'successful_count': successful_count,
            'failed_count': failed_count,
            'total_count': total_files,
            'results': {
                'successful': successful_deletions,
                'failed': failed_deletions
            }
        }

        # Include errors in response if any
        if errors:
            response_data['errors'] = errors

        # Return appropriate status code
        if failed_count == 0:
            return jsonify(response_data), 200  # All successful
        elif successful_count == 0:
            return jsonify(response_data), 400  # All failed
        else:
            return jsonify(response_data), 207  # Partial success (Multi-Status)

    except Exception as e:
        logger.error(f"Error in batch delete operation: {str(e)}", exc_info=True)
        return jsonify({
            'success': False,
            'error': f'Batch delete operation failed: {str(e)}',
            'successful_count': 0,
            'failed_count': 0,
            'total_count': 0
        }), 500

@api_bp.route('/files/content', methods=['GET', 'POST'])
@function_permission_required('view_files')
def handle_file_content():
    if request.method == 'GET':
        category = request.args.get('category')
        filename = request.args.get('filename')
        if not category or not filename:
            return jsonify({'error': 'Category and filename are required'}), 400
        try:
            content = utils.get_file_content(category, filename)
            return jsonify({'content': content})
        except Exception as e:
            return jsonify({'error': str(e)}), 500
    elif request.method == 'POST':
        data = request.get_json()
        category = data.get('category')
        filename = data.get('filename')
        content = data.get('content')
        if not category or not filename or content is None:
            return jsonify({'error': 'Category, filename, and content are required'}), 400
        try:
            utils.update_file_content(category, filename, content)
            return jsonify({'message': 'File content updated successfully'}), 200
        except Exception as e:
            return jsonify({'error': str(e)}), 500

@api_bp.route('/query', methods=['POST'])
@function_permission_required('perform_query')
def query():
    data = request.get_json()
    if not data or 'query' not in data or 'category' not in data:
        return jsonify({'error': 'Query and category are required'}), 400

    query_text = data['query']
    category = data['category']
    user_id = 1 # Replace with actual user ID from session

    try:
        result = query_category(query_text, category, user_id)
        return jsonify(result)
    except Exception as e:
        logger.error(f"Error processing query: {e}")
        return jsonify({'error': str(e)}), 500

@api_bp.route('/models', methods=['GET'])
@admin_required
def get_ollama_models():
    from app.utils.config import get_models_data
    try:
        models_data = get_models_data()
        return jsonify(models_data)
    except requests.exceptions.RequestException as e:
        logger.error(f"Error connecting to Ollama: {e}")
        return jsonify({'error': 'Could not connect to Ollama. Please ensure Ollama is running and accessible.'}), 500
    except Exception as e:
        logger.error(f"Error getting models: {e}")
        return jsonify({'error': str(e)}), 500

@api_bp.route('/settings/models', methods=['GET', 'POST'])
@admin_required
def handle_default_models():
    from app.utils.config import get_models_data
    from app import save_default_models, update_env_file
    if request.method == 'GET':
        try:
            return jsonify(get_models_data())
        except Exception as e:
            return jsonify({'error': str(e)}), 500
    elif request.method == 'POST':
        data = request.get_json()
        try:
            save_default_models(llm_model=data.get('llm_model'), 
                                embedding_model=data.get('embedding_model'),
                                vision_model=data.get('vision_model'))
            update_env_file(llm_model=data.get('llm_model'), 
                            embedding_model=data.get('embedding_model'),
                            vision_model=data.get('vision_model'))
            return jsonify({'message': 'Default models saved successfully'}), 200
        except Exception as e:
            return jsonify({'error': str(e)}), 500

@api_bp.route('/settings/vision_embedding', methods=['GET'])
def handle_vision_embedding_settings():
    from app.utils.config import get_embedding_config_data
    from app import save_default_models
    if request.method == 'GET':
        try:
            return jsonify(get_embedding_config_data())
        except Exception as e:
            return jsonify({'error': str(e)}), 500
    elif request.method == 'POST':
        data = request.get_json()
        try:
            save_default_models(use_vision=data.get('use_vision_model'),
                                filter_pdf_images=data.get('filter_pdf_images'),
                                filter_sensitivity=data.get('filter_sensitivity'),
                                max_pdf_images=data.get('max_pdf_images'))
            return jsonify({'message': 'Vision and embedding settings saved successfully'}), 200
        except Exception as e:
            return jsonify({'error': str(e)}), 500

@api_bp.route('/settings/vision_embedding_status', methods=['GET'])
def get_vision_embedding_status():
    """Get the global vision model during embedding setting."""
    try:
        # Get the current setting from environment variable
        use_vision_during_embedding = os.getenv('USE_VISION_MODEL_DURING_EMBEDDING', 'true').lower() == 'true'

        # Return the setting as JSON
        return jsonify({
            "enabled": use_vision_during_embedding,
            "message": "Vision model during embedding is " + ("enabled" if use_vision_during_embedding else "disabled")
        }), 200
    except Exception as e:
        logger.error(f"Error getting vision embedding setting: {str(e)}")
        return jsonify({"error": str(e)}), 500

@api_bp.route('/settings/query_config', methods=['GET', 'POST'])
@admin_required
def handle_query_config():
    from app.utils.config import get_query_config_data
    from app import app, logger, save_default_models, clear_cache
    if request.method == 'GET':
        try:
            return jsonify(get_query_config_data())
        except Exception as e:
            return jsonify({'error': str(e)}), 500
    elif request.method == 'POST':
        try:
            data = request.get_json()
            if not data:
                return jsonify({"error": "No data provided"}), 400

            # Extract data from request
            preamble = data.get('preamble')
            anti_hallucination_mode = data.get('anti_hallucination_mode')
            anti_hallucination_custom_instructions = data.get('anti_hallucination_custom_instructions')
            prompt_templates = data.get('prompt_templates')
            insufficient_info_phrases = data.get('insufficient_info_phrases')
            followup_question_templates = data.get('followup_question_templates')
            use_vision = data.get('use_vision')

            # --- Extract hallucination detection thresholds (support both flat and nested) ---
            hallucination_detection = data.get('hallucination_detection', {})
            hallucination_threshold_strict = data.get('hallucination_threshold_strict', hallucination_detection.get('threshold_strict'))
            hallucination_threshold_balanced = data.get('hallucination_threshold_balanced', hallucination_detection.get('threshold_balanced'))
            hallucination_threshold_default = data.get('hallucination_threshold_default', hallucination_detection.get('threshold_default'))
            min_statement_length = data.get('min_statement_length', hallucination_detection.get('min_statement_length'))

            # Extract RAG optimization parameters
            enable_query_caching = data.get('enable_query_caching', True)
            query_cache_ttl = data.get('query_cache_ttl', 1800)
            use_redis_cache = data.get('use_redis_cache', True)
            redis_url = data.get('redis_url', 'redis://localhost:6379/1')
            enable_parallel_scoring = data.get('enable_parallel_scoring', True)
            parallel_threshold = data.get('parallel_threshold', 4)
            max_parallel_workers = data.get('max_parallel_workers', 4)
            enable_adaptive_retrieval = data.get('enable_adaptive_retrieval', True)
            simple_k_ratio = data.get('simple_k_ratio', 0.67)
            complex_k_ratio = data.get('complex_k_ratio', 1.33)
            min_adaptive_k = data.get('min_adaptive_k', 6)
            max_adaptive_k = data.get('max_adaptive_k', 20)
            enable_performance_monitoring = data.get('enable_performance_monitoring', True)
            log_cache_performance = data.get('log_cache_performance', True)
            log_adaptive_retrieval = data.get('log_adaptive_retrieval', True)
            log_parallel_processing = data.get('log_parallel_processing', True)
            auto_invalidate_cache = data.get('auto_invalidate_cache', True)

            # Save the query configuration
            success = save_default_models(
                preamble=preamble,
                anti_hallucination_mode=anti_hallucination_mode,
                anti_hallucination_custom_instructions=anti_hallucination_custom_instructions,
                prompt_templates=prompt_templates,
                insufficient_info_phrases=insufficient_info_phrases,
                followup_question_templates=followup_question_templates,
                use_vision=use_vision,
                hallucination_threshold_strict=hallucination_threshold_strict,
                hallucination_threshold_balanced=hallucination_threshold_balanced,
                hallucination_threshold_default=hallucination_threshold_default,
                min_statement_length=min_statement_length
            )

            if success:
                # Update environment variables
                if preamble is not None:
                    os.environ['QUERY_PREAMBLE'] = preamble

                if anti_hallucination_mode is not None:
                    os.environ['ANTI_HALLUCINATION_MODE'] = anti_hallucination_mode

                if anti_hallucination_custom_instructions is not None:
                    os.environ['ANTI_HALLUCINATION_CUSTOM_INSTRUCTIONS'] = anti_hallucination_custom_instructions

                if prompt_templates is not None:
                    os.environ['PROMPT_TEMPLATES'] = json.dumps(prompt_templates)

                if insufficient_info_phrases is not None:
                    os.environ['INSUFFICIENT_INFO_PHRASES'] = json.dumps(insufficient_info_phrases)

                if followup_question_templates is not None:
                    os.environ['FOLLOWUP_QUESTION_TEMPLATES'] = json.dumps(followup_question_templates)

                if use_vision is not None:
                    use_vision_str = 'true' if use_vision else 'false'
                    os.environ['USE_VISION_MODEL'] = use_vision_str
                    app.config['USE_VISION_MODEL'] = use_vision_str.lower() == 'true'
                    logger.info(f"Updated vision model for chat setting: {app.config['USE_VISION_MODEL']}")

                # Update RAG optimization environment variables
                os.environ['RAG_ENABLE_QUERY_CACHING'] = 'true' if enable_query_caching else 'false'
                os.environ['RAG_QUERY_CACHE_TTL'] = str(query_cache_ttl)
                os.environ['RAG_USE_REDIS_CACHE'] = 'true' if use_redis_cache else 'false'
                os.environ['RAG_REDIS_URL'] = redis_url
                os.environ['RAG_ENABLE_PARALLEL_SCORING'] = 'true' if enable_parallel_scoring else 'false'
                os.environ['RAG_PARALLEL_THRESHOLD'] = str(parallel_threshold)
                os.environ['RAG_MAX_PARALLEL_WORKERS'] = str(max_parallel_workers)
                os.environ['RAG_ENABLE_ADAPTIVE_RETRIEVAL'] = 'true' if enable_adaptive_retrieval else 'false'
                os.environ['RAG_SIMPLE_QUERY_K_RATIO'] = str(simple_k_ratio)
                os.environ['RAG_COMPLEX_QUERY_K_RATIO'] = str(complex_k_ratio)
                os.environ['RAG_MIN_ADAPTIVE_K'] = str(min_adaptive_k)
                os.environ['RAG_MAX_ADAPTIVE_K'] = str(max_adaptive_k)
                os.environ['RAG_ENABLE_PERFORMANCE_MONITORING'] = 'true' if enable_performance_monitoring else 'false'
                os.environ['RAG_LOG_CACHE_PERFORMANCE'] = 'true' if log_cache_performance else 'false'
                os.environ['RAG_LOG_ADAPTIVE_RETRIEVAL'] = 'true' if log_adaptive_retrieval else 'false'
                os.environ['RAG_LOG_PARALLEL_PROCESSING'] = 'true' if log_parallel_processing else 'false'
                os.environ['RAG_AUTO_INVALIDATE_ON_NEW_DOCS'] = 'true' if auto_invalidate_cache else 'false'

                # Reload RAG configuration to pick up new settings
                from config.rag_optimizations import reload_rag_config
                reload_rag_config()
                logger.info("RAG optimization configuration updated")

                return jsonify({"message": "Query configuration saved successfully"}), 200
            else:
                return jsonify({"error": "Failed to save query configuration"}), 500

        except Exception as e:
            logger.error(f"Error saving query configuration: {str(e)}")
            return jsonify({"error": f"Failed to save query configuration: {str(e)}"}), 500

@api_bp.route('/settings/embedding_config', methods=['GET', 'POST'])
@admin_required
def handle_embedding_config():
    from app.utils.config import get_embedding_config_data
    from app import save_default_models
    if request.method == 'GET':
        try:
            return jsonify(get_embedding_config_data())
        except Exception as e:
            return jsonify({'error': str(e)}), 500
    elif request.method == 'POST':
        try:
            data = request.get_json()
            if not data:
                return jsonify({"error": "No data provided"}), 400

            # Extract data from request
            chunk_size = data.get('chunk_size')
            chunk_overlap = data.get('chunk_overlap')
            extract_tables = data.get('extract_tables')
            extract_images = data.get('extract_images')
            use_vision_model = data.get('use_vision_model')
            filter_sensitivity = data.get('filter_sensitivity')
            max_images = data.get('max_images')
            embedding_model = data.get('embedding_model')
            batch_size = data.get('batch_size')
            processing_threads = data.get('processing_threads')

            # Extract RAG optimization parameters for embedding
            enable_embedding_caching = data.get('enable_embedding_caching', True)
            embedding_cache_ttl = data.get('embedding_cache_ttl', 86400)
            prefer_semantic_chunking = data.get('prefer_semantic_chunking', True)
            semantic_threshold_length = data.get('semantic_threshold_length', 5000)
            semantic_buffer_size = data.get('semantic_buffer_size', 1)
            semantic_breakpoint_threshold = data.get('semantic_breakpoint_threshold', 95)
            fallback_to_sentence = data.get('fallback_to_sentence', True)

            # Save the embedding configuration
            success = save_default_models(
                embedding_model=embedding_model,
                use_vision_during_embedding=use_vision_model,
                filter_sensitivity=filter_sensitivity,
                chunk_size=chunk_size,
                chunk_overlap=chunk_overlap,
                extract_tables=extract_tables,
                extract_images=extract_images,
                max_images=max_images,
                batch_size=batch_size,
                processing_threads=processing_threads
            )

            if success:
                # Update environment variables
                if embedding_model is not None:
                    os.environ['TEXT_EMBEDDING_MODEL'] = embedding_model
                    _chroma_cache.clear()

                if use_vision_model is not None:
                    os.environ['USE_VISION_MODEL_DURING_EMBEDDING'] = 'true' if use_vision_model else 'false'

                if filter_sensitivity is not None:
                    os.environ['PDF_IMAGE_FILTER_SENSITIVITY'] = filter_sensitivity

                if max_images is not None:
                    os.environ['MAX_PDF_IMAGES_TO_ANALYZE'] = str(max_images)

                if chunk_size is not None:
                    os.environ['EMBEDDING_CHUNK_SIZE'] = str(chunk_size)

                if chunk_overlap is not None:
                    os.environ['EMBEDDING_CHUNK_OVERLAP'] = str(chunk_overlap)

                if batch_size is not None:
                    os.environ['EMBEDDING_BATCH_SIZE'] = str(batch_size)

                if processing_threads is not None:
                    os.environ['EMBEDDING_PROCESSING_THREADS'] = str(processing_threads)

                # Update RAG optimization environment variables for embedding
                os.environ['RAG_ENABLE_EMBEDDING_CACHING'] = 'true' if enable_embedding_caching else 'false'
                os.environ['RAG_EMBEDDING_CACHE_TTL'] = str(embedding_cache_ttl)
                os.environ['RAG_PREFER_SEMANTIC_CHUNKING'] = 'true' if prefer_semantic_chunking else 'false'
                os.environ['RAG_SEMANTIC_THRESHOLD_LENGTH'] = str(semantic_threshold_length)
                os.environ['RAG_SEMANTIC_BUFFER_SIZE'] = str(semantic_buffer_size)
                os.environ['RAG_SEMANTIC_BREAKPOINT_THRESHOLD'] = str(semantic_breakpoint_threshold)
                os.environ['RAG_FALLBACK_TO_SENTENCE'] = 'true' if fallback_to_sentence else 'false'

                # Reload RAG configuration to pick up new settings
                from config.rag_optimizations import reload_rag_config
                reload_rag_config()
                logger.info("RAG optimization configuration for embedding updated")

                return jsonify({"message": "Embedding configuration saved successfully"}), 200
            else:
                return jsonify({"error": "Failed to save embedding configuration"}), 500

        except Exception as e:
            logger.error(f"Error saving embedding configuration: {str(e)}")
            return jsonify({"error": f"Failed to save embedding configuration: {str(e)}"}), 500

@api_bp.route('/settings/unified_config', methods=['POST'])
@admin_required
def save_unified_config():
    from app import save_default_models
    data = request.get_json()
    try:
        save_default_models(**data)
        return jsonify({'message': 'Configuration saved successfully'}), 200
    except Exception as e:
        return jsonify({'error': str(e)}), 500

@api_bp.route('/models', methods=['GET'])
@admin_required
def get_llm_models():
    from app.utils.config import get_models_data
    try:
        models_data = get_models_data()
        return jsonify(models_data['models'])
    except Exception as e:
        logger.error(f"Error fetching LLM models: {str(e)}")
        return jsonify({'error': 'Failed to fetch models'}), 500

@api_bp.route('/embeddings', methods=['GET'])
@admin_required
def get_embedding_models():
    from app import get_models_data
    try:
        models_data = get_models_data()
        return jsonify(models_data['embeddings'])
    except Exception as e:
        logger.error(f"Error fetching embedding models: {str(e)}")
        return jsonify({'error': 'Failed to fetch models'}), 500

@api_bp.route('/performance/rag', methods=['GET'])
@admin_required
def get_rag_performance():
    """Get RAG pipeline performance metrics"""
    try:
        rag_monitor = get_rag_monitor()
        stats = rag_monitor.get_comprehensive_stats()

        # Add cache service stats
        cache_stats = cache_service.get_stats()
        stats['cache_service'] = cache_stats

        return jsonify({
            'success': True,
            'data': stats,
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        logger.error(f"Error getting RAG performance metrics: {str(e)}")
        return jsonify({'error': 'Failed to get performance metrics'}), 500

@api_bp.route('/performance/rag/reset', methods=['POST'])
@admin_required
def reset_rag_performance():
    """Reset RAG pipeline performance metrics"""
    try:
        rag_monitor = get_rag_monitor()
        rag_monitor.reset_stats()

        return jsonify({
            'success': True,
            'message': 'RAG performance metrics reset successfully',
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        logger.error(f"Error resetting RAG performance metrics: {str(e)}")
        return jsonify({'error': 'Failed to reset performance metrics'}), 500

@api_bp.route('/vision_models', methods=['GET'])
@admin_required
def get_vision_models():
    from app.utils.config import get_models_data
    try:
        models_data = get_models_data()
        return jsonify(models_data['vision_models'])
    except Exception as e:
        logger.error(f"Error fetching vision models: {str(e)}")
        return jsonify({'error': 'Failed to fetch models'}), 500

@api_bp.route('/multimodal/status', methods=['GET'])
@admin_required
def get_multimodal_status():
    """Get comprehensive multimodal pipeline status"""
    try:
        from app.services.multimodal_status_service import multimodal_status_service

        force_refresh = request.args.get('refresh', 'false').lower() == 'true'
        status = multimodal_status_service.get_status(force_refresh=force_refresh)

        return jsonify({
            'success': True,
            'status': status
        })
    except Exception as e:
        logger.error(f"Error getting multimodal status: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@api_bp.route('/multimodal/health', methods=['GET'])
@admin_required
def get_multimodal_health():
    """Get multimodal pipeline health summary"""
    try:
        from app.services.multimodal_status_service import multimodal_status_service

        health = multimodal_status_service.get_health_summary()

        return jsonify({
            'success': True,
            'health': health
        })
    except Exception as e:
        logger.error(f"Error getting multimodal health: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@api_bp.route('/multimodal/metrics', methods=['GET'])
@admin_required
def get_multimodal_metrics():
    """Get multimodal processing metrics"""
    try:
        from app.services.multimodal_status_service import multimodal_status_service

        metrics = multimodal_status_service.get_metrics()

        return jsonify({
            'success': True,
            'metrics': metrics
        })
    except Exception as e:
        logger.error(f"Error getting multimodal metrics: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@api_bp.route('/multimodal/metrics/reset', methods=['POST'])
@admin_required
def reset_multimodal_metrics():
    """Reset multimodal processing metrics"""
    try:
        from app.services.multimodal_status_service import multimodal_status_service

        multimodal_status_service.reset_metrics()

        return jsonify({
            'success': True,
            'message': 'Metrics reset successfully'
        })
    except Exception as e:
        logger.error(f"Error resetting multimodal metrics: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@api_bp.route('/multimodal/test', methods=['POST'])
@admin_required
def test_multimodal_pipeline():
    """Test multimodal pipeline with sample or uploaded document"""
    try:
        import tempfile
        import os
        from datetime import datetime

        # Check if file was uploaded
        if 'file' not in request.files:
            return jsonify({
                'success': False,
                'error': 'No file uploaded'
            }), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({
                'success': False,
                'error': 'No file selected'
            }), 400

        # Save uploaded file temporarily
        with tempfile.NamedTemporaryFile(delete=False, suffix=os.path.splitext(file.filename)[1]) as temp_file:
            file.save(temp_file.name)
            temp_path = temp_file.name

        try:
            # Test multimodal processing
            start_time = datetime.now()

            # TODO: Implement actual multimodal processing test
            # For now, return a mock result
            processing_time = (datetime.now() - start_time).total_seconds()

            result = {
                'success': True,
                'filename': file.filename,
                'processing_time': processing_time,
                'results': {
                    'images_extracted': 0,  # TODO: Implement actual extraction
                    'tables_extracted': 0,  # TODO: Implement actual extraction
                    'text_chunks': 0,       # TODO: Implement actual chunking
                    'vision_captions': [],  # TODO: Implement actual vision processing
                    'table_data': []        # TODO: Implement actual table extraction
                },
                'status': 'Test completed - multimodal processing pipeline is accessible',
                'timestamp': datetime.now().isoformat()
            }

            # Record metrics
            from app.services.multimodal_status_service import multimodal_status_service
            multimodal_status_service.record_processing_complete(
                file.filename, processing_time, 0, 0, True, text_chunks=0
            )

            return jsonify(result)

        finally:
            # Clean up temporary file
            try:
                os.unlink(temp_path)
            except:
                pass

    except Exception as e:
        logger.error(f"Error testing multimodal pipeline: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@api_bp.route('/multimodal/logs', methods=['GET'])
@admin_required
def get_multimodal_logs():
    """Get multimodal processing logs"""
    try:
        from app.services.multimodal_logger import multimodal_logger

        lines = int(request.args.get('lines', 100))
        logs = multimodal_logger.get_processing_logs(lines)

        return jsonify({
            'success': True,
            'logs': logs
        })
    except Exception as e:
        logger.error(f"Error getting multimodal logs: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@api_bp.route('/multimodal/sessions', methods=['GET'])
@admin_required
def get_multimodal_sessions():
    """Get recent multimodal processing sessions"""
    try:
        from app.services.multimodal_logger import multimodal_logger

        limit = int(request.args.get('limit', 10))
        sessions = multimodal_logger.get_recent_sessions(limit)

        # Convert to dict for JSON serialization
        sessions_data = []
        for session in sessions:
            session_dict = {
                'session_id': session.session_id,
                'document_name': session.document_name,
                'start_time': session.start_time,
                'end_time': session.end_time,
                'status': session.status,
                'results': session.results,
                'event_count': len(session.events)
            }
            sessions_data.append(session_dict)

        return jsonify({
            'success': True,
            'sessions': sessions_data
        })
    except Exception as e:
        logger.error(f"Error getting multimodal sessions: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@api_bp.route('/multimodal/sessions/<session_id>/events', methods=['GET'])
@admin_required
def get_session_events(session_id):
    """Get events for a specific processing session"""
    try:
        from app.services.multimodal_logger import multimodal_logger

        events = multimodal_logger.get_session_events(session_id)

        # Convert to dict for JSON serialization
        events_data = []
        for event in events:
            event_dict = {
                'timestamp': event.timestamp,
                'event_type': event.event_type,
                'document_name': event.document_name,
                'message': event.message,
                'details': event.details
            }
            events_data.append(event_dict)

        return jsonify({
            'success': True,
            'events': events_data
        })
    except Exception as e:
        logger.error(f"Error getting session events: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e)
        }), 500

@api_bp.route('/extract_locations', methods=['POST'])
@admin_required
@function_permission_required('extract_locations')
def extract_locations_from_text():
    from app import logger
    try:
        data = request.get_json()
        text = data.get('text', '')
        if not text:
            return jsonify({'success': False, 'message': 'No text provided'}), 400

        locations = geo_utils.extract_locations_from_text(text)
        geocoded_locations = []

        for location in locations:
            geocoding_result = geo_utils.geocode_location(location['name'])
            if geocoding_result and geocoding_result.get('latitude') and geocoding_result.get('longitude'):
                location.update({
                    'latitude': geocoding_result['latitude'],
                    'longitude': geocoding_result['longitude'],
                    'country': geocoding_result['country'],
                    'state': geocoding_result.get('state'),
                    'city': geocoding_result['city'],
                    'municipality': geocoding_result.get('municipality'),
                    'barangay': geocoding_result.get('barangay')
                })

            # Only include locations with valid coordinates
            if location.get('latitude') and location.get('longitude'):
                geocoded_locations.append(location)

        return jsonify({
            'success': True,
            'locations': geocoded_locations,
            'count': len(geocoded_locations)
        })

    except Exception as e:
        logger.error(f"Error extracting locations from text: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Error extracting locations: {str(e)}'
        }), 500


@api_bp.route('/generate-html', methods=['POST'])
@admin_required
def generate_html_route():
    from app import html_generator
    data = request.get_json()
    if not data or 'content' not in data:
        return jsonify({'error': 'No content provided'}), 400
    
@api_bp.route('/locations')
@function_permission_required('ai_analytics')
def api_locations():
    """API endpoint to get location data."""
    try:
        category = request.args.get('category')

        if category:
            from app.utils.database import get_locations_by_category
            locations = get_locations_by_category(category)
        else:
            from app.utils.database import get_all_extracted_locations
            locations = get_all_extracted_locations(include_sources=True)

        return jsonify({
            'success': True,
            'locations': locations,
            'count': len(locations)
        })
    except Exception as e:
        logger.error(f"Error retrieving locations via API: {str(e)}")
        return jsonify({'error': 'Failed to retrieve locations'}), 500

@api_bp.route('/location_statistics')
@function_permission_required('ai_analytics')
def api_location_statistics():
    """API endpoint to get location statistics."""
    try:
        from app.utils.database import get_location_statistics
        statistics = get_location_statistics()
        return jsonify({
            'success': True,
            'statistics': statistics
        })
    except Exception as e:
        logger.error(f"Error retrieving location statistics: {str(e)}")
        return jsonify({'error': 'Failed to retrieve statistics'}), 500

@api_bp.route('/locations/<int:location_id>', methods=['DELETE'])
@function_permission_required('ai_analytics')
def api_delete_location(location_id):
    """API endpoint to delete a single location."""
    try:
        from app.utils.database import delete_location_by_id

        success = delete_location_by_id(location_id)

        if success:
            return jsonify({
                'success': True,
                'message': 'Location deleted successfully'
            })
        else:
            return jsonify({
                'success': False,
                'message': 'Failed to delete location'
            }), 500

    except Exception as e:
        logger.error(f"Error deleting location {location_id}: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Error deleting location: {str(e)}'
        }), 500

@api_bp.route('/locations/bulk-delete', methods=['POST'])
@function_permission_required('ai_analytics')
def api_bulk_delete_locations():
    """API endpoint to delete multiple locations."""
    try:
        from app.utils.database import delete_location_by_id

        data = request.get_json()
        location_ids = data.get('location_ids', [])

        if not location_ids:
            return jsonify({
                'success': False,
                'message': 'No location IDs provided'
            }), 400

        deleted_count = 0
        failed_count = 0

        for location_id in location_ids:
            try:
                if delete_location_by_id(int(location_id)):
                    deleted_count += 1
                else:
                    failed_count += 1
            except Exception as e:
                logger.error(f"Error deleting location {location_id}: {str(e)}")
                failed_count += 1

        return jsonify({
            'success': True,
            'deleted_count': deleted_count,
            'failed_count': failed_count,
            'message': f'Deleted {deleted_count} location(s)' + (f', {failed_count} failed' if failed_count > 0 else '')
        })

    except Exception as e:
        logger.error(f"Error in bulk delete: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Error deleting locations: {str(e)}'
        }), 500

# NOTE: This route originally conflicted with another '/extract_locations' route.
# It uses the more detailed LocationExtractor.
@api_bp.route('/extract-locations-detailed', methods=['POST'])
def api_extract_locations():
    """API endpoint to extract locations from text using the detailed LocationExtractor."""
    try:
        data = request.get_json()
        text = data.get('text', '')
        sources = data.get('sources', [])
        filter_philippine = data.get('filter_philippine', True)
        admin_levels_only = data.get('admin_levels_only', True)

        if not text:
            return jsonify({
                'success': False,
                'message': 'No text provided'
            }), 400

        # Import location extractor
        from app.services.location_extractor import LocationExtractor

        # Initialize extractor
        extractor = LocationExtractor()

        # Extract locations from text
        locations = extractor.extract_locations_from_text(text)

        # Filter for Philippine administrative divisions if requested
        if filter_philippine and admin_levels_only:
            locations = [
                loc for loc in locations
                if loc.get('location_type') in ['municipality', 'city', 'barangay'] or
                   (loc.get('administrative_level') in ['municipality', 'city', 'barangay'])
            ]

        # Geocode locations that don't have coordinates
        geocoded_locations = []
        for location in locations:
            if not location.get('latitude') or not location.get('longitude'):
                geocoding_result = extractor.geocode_location(location['location_text'])
                if geocoding_result and geocoding_result.get('status') == 'success':
                    location.update({
                        'latitude': geocoding_result['latitude'],
                        'longitude': geocoding_result['longitude'],
                        'geocoded_address': geocoding_result['formatted_address'],
                        'country': geocoding_result['country'],
                        'region': geocoding_result['region'],
                        'city': geocoding_result['city'],
                        'municipality': geocoding_result.get('municipality'),
                        'barangay': geocoding_result.get('barangay')
                    })

            # Only include locations with valid coordinates
            if location.get('latitude') and location.get('longitude'):
                geocoded_locations.append(location)

        return jsonify({
            'success': True,
            'locations': geocoded_locations,
            'count': len(geocoded_locations)
        })

    except Exception as e:
        logger.error(f"Error extracting locations from text: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'Error extracting locations: {str(e)}'
        }), 500


"""
Multimodal Image Processing Service
Enhanced image extraction and processing with vision model integration
"""

import os
import logging
from typing import Dict, Any, List, Optional, Tuple
import fitz  # PyMuPDF
from pathlib import Path

from config.multimodal_config import get_multimodal_config
from app.services.multimodal_storage import get_multimodal_storage
from app.services.vision_processor import analyze_image, detect_image_content
from app.utils.performance_monitor import performance_monitor
from scripts.setup.create_temp_dirs import create_pdf_directory_structure

logger = logging.getLogger(__name__)

class MultimodalImageProcessor:
    """Enhanced image processor for multimodal RAG pipeline"""
    
    def __init__(self, config=None):
        self.config = config or get_multimodal_config()
        self.storage = get_multimodal_storage()
        
    @performance_monitor(track_memory=True, track_cpu=True, log_parameters=True, log_result_size=True)
    def extract_images_from_pdf(self, pdf_path: str, document_id: str, 
                               category: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Extract images from PDF with vision model analysis and multimodal storage
        
        Args:
            pdf_path: Path to the PDF file
            document_id: Unique identifier for the document
            category: Category for organizing content
            
        Returns:
            List of processed image information
        """
        if not self.config.image_processing.enable_image_extraction:
            logger.info("Image extraction is disabled")
            return []
        
        images = []
        try:
            doc = fitz.open(pdf_path)
            logger.info(f"Extracting images from PDF: {pdf_path} ({len(doc)} pages)")
            
            image_count = 0
            for page_num, page in enumerate(doc):
                if image_count >= self.config.image_processing.max_images_per_document:
                    logger.info(f"Reached maximum image limit: {self.config.image_processing.max_images_per_document}")
                    break
                
                page_images = self._extract_images_from_page(
                    page, page_num + 1, document_id, pdf_path, category
                )
                images.extend(page_images)
                image_count += len(page_images)
            
            doc.close()
            logger.info(f"Extracted {len(images)} images from PDF")
            return images
            
        except Exception as e:
            logger.error(f"Error extracting images from PDF {pdf_path}: {e}")
            return []
    
    def _extract_images_from_page(self, page: fitz.Page, page_num: int, 
                                 document_id: str, pdf_path: str, 
                                 category: Optional[str]) -> List[Dict[str, Any]]:
        """Extract and process images from a single PDF page"""
        page_images = []
        
        try:
            image_list = page.get_images(full=True)
            logger.debug(f"Found {len(image_list)} images on page {page_num}")
            
            for img_index, img in enumerate(image_list):
                try:
                    image_info = self._process_single_image(
                        page, img, img_index, page_num, document_id, pdf_path, category
                    )
                    if image_info:
                        page_images.append(image_info)
                        
                except Exception as e:
                    logger.error(f"Error processing image {img_index} on page {page_num}: {e}")
                    continue
            
            return page_images
            
        except Exception as e:
            logger.error(f"Error extracting images from page {page_num}: {e}")
            return []
    
    def _process_single_image(self, page: fitz.Page, img: tuple, img_index: int,
                             page_num: int, document_id: str, pdf_path: str,
                             category: Optional[str]) -> Optional[Dict[str, Any]]:
        """Process a single image from PDF"""
        try:
            # Extract image data
            xref = img[0]
            doc = page.parent
            base_image = doc.extract_image(xref)
            image_data = base_image["image"]
            
            # Check minimum size
            if len(image_data) < self.config.image_processing.min_image_size:
                logger.debug(f"Image too small: {len(image_data)} bytes")
                return None
            
            # Check format
            image_format = base_image["ext"].lower()
            if image_format not in self.config.image_processing.supported_formats:
                logger.debug(f"Unsupported image format: {image_format}")
                return None
            
            # Get image dimensions and position
            image_rect = page.get_image_bbox(img)
            
            # Prepare basic metadata
            metadata = {
                "format": image_format,
                "width": base_image["width"],
                "height": base_image["height"],
                "colorspace": base_image["colorspace"],
                "bpc": base_image["bpc"],
                "page_num": page_num,
                "image_index": img_index,
                "bbox": {
                    "x0": image_rect.x0,
                    "y0": image_rect.y0,
                    "x1": image_rect.x1,
                    "y1": image_rect.y1
                },
                "source_pdf": os.path.basename(pdf_path)
            }
            
            # Filter logos and irrelevant images if enabled
            if self.config.image_processing.filter_logos:
                if self._should_filter_image(image_data, metadata):
                    logger.debug(f"Filtered image {img_index} on page {page_num}")
                    return None
            
            # Generate vision model caption if enabled
            caption = None
            vision_metadata = {}
            if self.config.image_processing.enable_vision_captions:
                caption, vision_metadata = self._generate_image_caption(
                    image_data, metadata, document_id, page_num
                )
            
            # Store image in multimodal storage
            storage_metadata = {
                **metadata,
                "caption": caption,
                "vision_analysis": vision_metadata
            }
            
            content_hash = self.storage.store_image(
                image_data, storage_metadata, document_id, page_num
            )
            
            if not content_hash:
                logger.error(f"Failed to store image {img_index} on page {page_num}")
                return None
            
            # Save image file if traditional storage is also needed
            image_file_path = None
            if category:
                image_file_path = self._save_image_file(
                    image_data, content_hash, image_format, 
                    document_id, page_num, img_index, category
                )
            
            # Return processed image information
            return {
                "content_hash": content_hash,
                "content_type": "image",
                "page_num": page_num,
                "image_index": img_index,
                "format": image_format,
                "size_bytes": len(image_data),
                "dimensions": {
                    "width": base_image["width"],
                    "height": base_image["height"]
                },
                "bbox": metadata["bbox"],
                "caption": caption,
                "vision_analysis": vision_metadata,
                "file_path": image_file_path,
                "document_id": document_id
            }
            
        except Exception as e:
            logger.error(f"Error processing image {img_index} on page {page_num}: {e}")
            return None
    
    def _should_filter_image(self, image_data: bytes, metadata: Dict[str, Any]) -> bool:
        """Determine if image should be filtered out"""
        try:
            # Basic size filtering
            if len(image_data) < self.config.image_processing.min_image_size:
                return True
            
            # Dimension filtering (very small images are likely logos/icons)
            width = metadata.get("width", 0)
            height = metadata.get("height", 0)
            if width < 50 or height < 50:
                return True
            
            # Use vision model for content-based filtering if available
            if self.config.image_processing.enable_vision_captions:
                try:
                    # Save image temporarily for vision analysis
                    import tempfile
                    with tempfile.NamedTemporaryFile(suffix=f".{metadata['format']}", delete=False) as tmp_file:
                        tmp_file.write(image_data)
                        tmp_path = tmp_file.name
                    
                    # Analyze image content
                    content_analysis = detect_image_content(tmp_path)
                    
                    # Clean up temporary file
                    os.unlink(tmp_path)
                    
                    # Filter based on content analysis
                    if content_analysis.get("is_logo", False):
                        return True
                    
                    # Calculate relevance score
                    relevance = self._calculate_image_relevance(content_analysis)
                    return relevance < self.config.image_processing.filter_sensitivity
                    
                except Exception as e:
                    logger.debug(f"Vision-based filtering failed: {e}")
                    return False
            
            return False
            
        except Exception as e:
            logger.error(f"Error in image filtering: {e}")
            return False
    
    def _calculate_image_relevance(self, content_analysis: Dict[str, Any]) -> float:
        """Calculate relevance score for image content"""
        try:
            score = 0.5  # Base score
            
            # Boost score for certain categories
            category = content_analysis.get("category", "").lower()
            if category in ["diagram", "chart", "graph", "table"]:
                score += 0.3
            elif category in ["photograph", "illustration"]:
                score += 0.2
            elif category in ["logo", "icon"]:
                score -= 0.4
            
            # Boost score if text is detected
            if content_analysis.get("text"):
                score += 0.2
            
            # Boost score for multiple objects
            objects = content_analysis.get("objects", [])
            if len(objects) > 1:
                score += 0.1
            
            return max(0.0, min(1.0, score))
            
        except Exception as e:
            logger.error(f"Error calculating image relevance: {e}")
            return 0.5
    
    def _generate_image_caption(self, image_data: bytes, metadata: Dict[str, Any],
                               document_id: str, page_num: int) -> Tuple[Optional[str], Dict[str, Any]]:
        """Generate caption for image using vision model"""
        try:
            # Save image temporarily for vision analysis
            import tempfile
            with tempfile.NamedTemporaryFile(suffix=f".{metadata['format']}", delete=False) as tmp_file:
                tmp_file.write(image_data)
                tmp_path = tmp_file.name
            
            # Generate caption using vision model
            prompt = self.config.image_processing.vision_prompt_template
            analysis_result = analyze_image(tmp_path, prompt)
            
            # Clean up temporary file
            os.unlink(tmp_path)
            
            if "error" in analysis_result:
                logger.warning(f"Vision analysis failed: {analysis_result['error']}")
                return None, {}
            
            caption = analysis_result.get("description", "")
            vision_metadata = {
                "model": analysis_result.get("model", ""),
                "processing_time": analysis_result.get("processing_time", 0),
                "analysis_timestamp": analysis_result.get("timestamp", "")
            }
            
            return caption, vision_metadata
            
        except Exception as e:
            logger.error(f"Error generating image caption: {e}")
            return None, {}
    
    def _save_image_file(self, image_data: bytes, content_hash: str, 
                        image_format: str, document_id: str, page_num: int,
                        img_index: int, category: str) -> Optional[str]:
        """Save image file to traditional storage location"""
        try:
            # Create directory structure
            pdf_name = f"{document_id}.pdf"  # Reconstruct PDF name
            dir_structure = create_pdf_directory_structure(category, pdf_name)
            if not dir_structure:
                logger.error(f"Failed to create directory structure for {pdf_name}")
                return None
            
            # Generate filename
            filename = f"page_{page_num}_img_{img_index}_{content_hash[:8]}.{image_format}"
            image_path = os.path.join(dir_structure["pdf_images_dir"], filename)
            
            # Save image
            with open(image_path, 'wb') as f:
                f.write(image_data)
            
            logger.debug(f"Saved image file: {image_path}")
            return image_path
            
        except Exception as e:
            logger.error(f"Error saving image file: {e}")
            return None

# Global processor instance
_multimodal_image_processor = None

def get_multimodal_image_processor() -> MultimodalImageProcessor:
    """Get the global multimodal image processor instance"""
    global _multimodal_image_processor
    if _multimodal_image_processor is None:
        _multimodal_image_processor = MultimodalImageProcessor()
    return _multimodal_image_processor

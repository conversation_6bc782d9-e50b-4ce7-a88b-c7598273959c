"""
LangChain Multimodal RAG Example

This example demonstrates how to use the LangChain integration components
for multimodal PDF processing with PyMuPDF, including text, images, and tables.
"""

import os
import sys
import logging
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.services.langchain_integration.multimodal_document_loader import MultimodalDocumentLoader
from app.services.langchain_integration.ollama_embeddings_wrapper import get_default_langchain_embeddings
from app.services.langchain_integration.multimodal_text_splitter import get_multimodal_text_splitter
from app.services.langchain_integration.chroma_vectorstore_wrapper import get_chroma_vectorstore
from app.services.langchain_integration.adaptive_retriever import get_adaptive_retriever

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def main():
    """Main example function."""
    print("🦜 LangChain Multimodal RAG Example")
    print("=" * 50)
    
    # Example PDF path (replace with your own)
    pdf_path = "data/temp/RESEARCH/sample_document.pdf"
    category = "RESEARCH"
    
    if not os.path.exists(pdf_path):
        print(f"❌ PDF file not found: {pdf_path}")
        print("Please place a PDF file at the specified path or update the path.")
        return
    
    try:
        # Step 1: Load multimodal document
        print("\n📄 Step 1: Loading multimodal document...")
        loader = MultimodalDocumentLoader(
            file_path=pdf_path,
            category=category,
            include_images=True,
            include_tables=True,
            include_multimodal_chunks=True
        )
        
        documents = loader.load()
        print(f"✅ Loaded {len(documents)} documents from PDF")
        
        # Display document types
        content_types = {}
        for doc in documents:
            content_type = doc.metadata.get("content_type", "unknown")
            content_types[content_type] = content_types.get(content_type, 0) + 1
        
        print("📊 Content types found:")
        for content_type, count in content_types.items():
            print(f"   - {content_type}: {count} documents")
        
        # Step 2: Initialize embeddings
        print("\n🔤 Step 2: Initializing Ollama embeddings...")
        embeddings = get_default_langchain_embeddings()
        print(f"✅ Initialized embeddings: {embeddings.get_model_info()}")
        
        # Test embeddings
        test_embedding = embeddings.embed_query("test query")
        print(f"📏 Embedding dimension: {len(test_embedding)}")
        
        # Step 3: Split documents
        print("\n✂️ Step 3: Splitting documents with multimodal text splitter...")
        text_splitter = get_multimodal_text_splitter(
            chunk_size=800,
            chunk_overlap=200,
            chunking_strategy="adaptive",
            preserve_multimodal_relationships=True
        )
        
        split_documents = text_splitter.split_documents(documents)
        print(f"✅ Split into {len(split_documents)} chunks")
        
        # Display splitter info
        splitter_info = text_splitter.get_splitter_info()
        print(f"🔧 Splitter config: {splitter_info}")
        
        # Step 4: Create vector store
        print("\n🗄️ Step 4: Creating ChromaDB vector store...")
        vectorstore = get_chroma_vectorstore(
            category=category,
            embeddings=embeddings
        )
        
        # Add documents to vector store
        doc_ids = vectorstore.add_documents(split_documents, category=category)
        print(f"✅ Added {len(doc_ids)} documents to vector store")
        
        # Get collection stats
        stats = vectorstore.get_collection_stats(category)
        print(f"📈 Collection stats: {stats}")
        
        # Step 5: Create adaptive retriever
        print("\n🔍 Step 5: Creating adaptive retriever...")
        retriever = get_adaptive_retriever(
            vectorstore=vectorstore,
            base_k=12,
            category=category,
            enable_multimodal=True
        )
        
        retriever_info = retriever.get_retriever_info()
        print(f"🎯 Retriever config: {retriever_info}")
        
        # Step 6: Test queries with different complexities
        print("\n❓ Step 6: Testing adaptive retrieval...")
        
        test_queries = [
            "What is this?",  # Simple query (k=8)
            "What are the main findings in this research?",  # Medium query (k=12)
            "Can you provide a detailed analysis of the methodology, results, and implications discussed in this document?",  # Complex query (k=16)
            "Show me any tables or charts",  # Multimodal query
            "What images are included in this document?"  # Image-focused query
        ]
        
        for i, query in enumerate(test_queries, 1):
            print(f"\n🔍 Query {i}: {query}")
            
            # Retrieve documents
            retrieved_docs = retriever.get_relevant_documents(query)
            print(f"📋 Retrieved {len(retrieved_docs)} documents")
            
            # Display first few results
            for j, doc in enumerate(retrieved_docs[:3]):
                content_preview = doc.page_content[:100] + "..." if len(doc.page_content) > 100 else doc.page_content
                content_type = doc.metadata.get("content_type", "unknown")
                retrieval_k = doc.metadata.get("retrieval_k", "unknown")
                
                print(f"   📄 Doc {j+1} ({content_type}, k={retrieval_k}): {content_preview}")
        
        # Step 7: Test similarity search with scores
        print("\n📊 Step 7: Testing similarity search with scores...")
        query = "research methodology"
        results_with_scores = vectorstore.similarity_search_with_score(query, k=5, category=category)
        
        print(f"🔍 Query: {query}")
        for i, (doc, score) in enumerate(results_with_scores):
            content_preview = doc.page_content[:80] + "..." if len(doc.page_content) > 80 else doc.page_content
            content_type = doc.metadata.get("content_type", "unknown")
            print(f"   📄 Result {i+1} (score: {score:.3f}, type: {content_type}): {content_preview}")
        
        # Step 8: Test Maximum Marginal Relevance
        print("\n🎯 Step 8: Testing Maximum Marginal Relevance...")
        mmr_results = vectorstore.max_marginal_relevance_search(
            query="research findings",
            k=5,
            fetch_k=20,
            lambda_mult=0.5,
            category=category
        )
        
        print(f"🔍 MMR Query: research findings")
        for i, doc in enumerate(mmr_results):
            content_preview = doc.page_content[:80] + "..." if len(doc.page_content) > 80 else doc.page_content
            content_type = doc.metadata.get("content_type", "unknown")
            print(f"   📄 MMR Result {i+1} ({content_type}): {content_preview}")
        
        # Step 9: Display cache statistics
        print("\n📊 Step 9: Cache performance statistics...")
        cache_stats = embeddings.get_cache_stats()
        print(f"💾 Embedding cache stats: {cache_stats}")
        
        print("\n✅ LangChain multimodal RAG example completed successfully!")
        print("\n🎉 Key Features Demonstrated:")
        print("   - Multimodal document loading (text, images, tables)")
        print("   - Cached Ollama embeddings with LangChain compatibility")
        print("   - Adaptive text splitting with multimodal awareness")
        print("   - Unified ChromaDB vector store with category filtering")
        print("   - Adaptive retrieval with query complexity analysis")
        print("   - Multimodal-aware retrieval prioritization")
        print("   - Performance monitoring and caching")
        
    except Exception as e:
        logger.error(f"Error in LangChain example: {e}")
        print(f"❌ Error: {e}")
        raise


if __name__ == "__main__":
    main()

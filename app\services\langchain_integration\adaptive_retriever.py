"""
LangChain AdaptiveRetriever

LangChain-compatible retriever that implements adaptive k values based on
query complexity, integrating with existing performance monitoring and
multimodal content awareness.
"""

import logging
from typing import List, Dict, Any, Optional

from langchain_core.retrievers import BaseRetriever
from langchain_core.documents import Document
from langchain_core.callbacks import CallbackManagerForRetrieverRun

from app.services.langchain_integration.chroma_vectorstore_wrapper import ChromaVectorStoreWrapper
from app.services.query_service import get_adaptive_k
from app.utils.performance_monitor import performance_monitor
from config.rag_optimizations import get_rag_config

logger = logging.getLogger(__name__)


class AdaptiveRetriever(BaseRetriever):
    """
    LangChain-compatible retriever with adaptive k values.
    
    This retriever automatically adjusts the number of documents retrieved
    based on query complexity:
    - Simple queries (≤3 words): k=8 (base_k * 0.67)
    - Medium queries (4-10 words): k=12 (base_k)
    - Complex queries (>10 words): k=16 (base_k * 1.33)
    """
    
    def __init__(
        self,
        vectorstore: ChromaVectorStoreWrapper,
        base_k: int = 12,
        category: Optional[str] = None,
        enable_adaptive_k: bool = True,
        min_k: int = 6,
        max_k: int = 20,
        enable_mmr: bool = False,
        mmr_lambda: float = 0.5,
        mmr_fetch_k: int = 20,
        **kwargs
    ):
        """
        Initialize the adaptive retriever.
        
        Args:
            vectorstore: ChromaDB vector store wrapper
            base_k: Base number of documents to retrieve
            category: Default category for retrieval
            enable_adaptive_k: Whether to use adaptive k values
            min_k: Minimum k value
            max_k: Maximum k value
            enable_mmr: Whether to use Maximum Marginal Relevance
            mmr_lambda: Lambda parameter for MMR (diversity vs relevance)
            mmr_fetch_k: Number of documents to fetch for MMR
            **kwargs: Additional arguments
        """
        super().__init__(**kwargs)
        
        self.vectorstore = vectorstore
        self.base_k = base_k
        self.category = category
        self.enable_adaptive_k = enable_adaptive_k
        self.min_k = min_k
        self.max_k = max_k
        self.enable_mmr = enable_mmr
        self.mmr_lambda = mmr_lambda
        self.mmr_fetch_k = mmr_fetch_k
        
        # Load RAG configuration
        self.rag_config = get_rag_config()
        
        logger.info(f"Initialized AdaptiveRetriever with base_k={base_k}, category={category}")
    
    @performance_monitor(track_memory=True, track_cpu=True, log_parameters=True, log_result_size=True)
    def _get_relevant_documents(
        self,
        query: str,
        *,
        run_manager: CallbackManagerForRetrieverRun,
        **kwargs
    ) -> List[Document]:
        """
        Retrieve relevant documents with adaptive k values.
        
        Args:
            query: Search query
            run_manager: Callback manager for the retriever run
            **kwargs: Additional search parameters
            
        Returns:
            List of relevant documents
        """
        try:
            # Determine k value based on query complexity
            if self.enable_adaptive_k:
                k = self._calculate_adaptive_k(query)
            else:
                k = self.base_k
            
            # Override k if provided in kwargs
            k = kwargs.pop('k', k)
            
            # Get category from kwargs or use default
            category = kwargs.pop('category', self.category)
            
            logger.debug(f"Retrieving documents with k={k} for query: {query[:50]}...")
            
            # Perform retrieval
            if self.enable_mmr:
                documents = self._retrieve_with_mmr(query, k, category, **kwargs)
            else:
                documents = self._retrieve_with_similarity(query, k, category, **kwargs)
            
            # Log retrieval results
            logger.info(f"Retrieved {len(documents)} documents for query complexity analysis")
            
            # Add retrieval metadata to documents
            for doc in documents:
                doc.metadata.update({
                    "retrieval_k": k,
                    "retrieval_method": "mmr" if self.enable_mmr else "similarity",
                    "query_complexity": self._analyze_query_complexity(query),
                    "adaptive_retrieval": self.enable_adaptive_k
                })
            
            return documents
            
        except Exception as e:
            logger.error(f"Error in adaptive retrieval: {e}")
            # Fallback to simple similarity search
            return self._fallback_retrieval(query, **kwargs)
    
    def _calculate_adaptive_k(self, query: str) -> int:
        """Calculate adaptive k value based on query complexity."""
        try:
            # Use existing adaptive k logic
            adaptive_k = get_adaptive_k(query, self.base_k)
            
            # Apply min/max constraints
            adaptive_k = max(self.min_k, min(self.max_k, adaptive_k))
            
            logger.debug(f"Adaptive k calculation: query='{query[:30]}...', k={adaptive_k}")
            return adaptive_k
            
        except Exception as e:
            logger.error(f"Error calculating adaptive k: {e}")
            return self.base_k
    
    def _analyze_query_complexity(self, query: str) -> str:
        """Analyze query complexity for metadata."""
        word_count = len(query.split())
        
        if word_count <= 3:
            return "simple"
        elif word_count <= 10:
            return "medium"
        else:
            return "complex"
    
    def _retrieve_with_similarity(
        self,
        query: str,
        k: int,
        category: Optional[str],
        **kwargs
    ) -> List[Document]:
        """Retrieve documents using similarity search."""
        try:
            return self.vectorstore.similarity_search(
                query=query,
                k=k,
                category=category,
                **kwargs
            )
        except Exception as e:
            logger.error(f"Error in similarity search: {e}")
            raise
    
    def _retrieve_with_mmr(
        self,
        query: str,
        k: int,
        category: Optional[str],
        **kwargs
    ) -> List[Document]:
        """Retrieve documents using Maximum Marginal Relevance."""
        try:
            return self.vectorstore.max_marginal_relevance_search(
                query=query,
                k=k,
                fetch_k=self.mmr_fetch_k,
                lambda_mult=self.mmr_lambda,
                category=category,
                **kwargs
            )
        except Exception as e:
            logger.error(f"Error in MMR search: {e}")
            # Fallback to similarity search
            return self._retrieve_with_similarity(query, k, category, **kwargs)
    
    def _fallback_retrieval(self, query: str, **kwargs) -> List[Document]:
        """Fallback retrieval method."""
        try:
            logger.warning("Using fallback retrieval method")
            return self.vectorstore.similarity_search(
                query=query,
                k=self.base_k,
                category=self.category,
                **kwargs
            )
        except Exception as e:
            logger.error(f"Fallback retrieval failed: {e}")
            return []
    
    def get_retriever_info(self) -> Dict[str, Any]:
        """Get information about the retriever configuration."""
        return {
            "retriever_type": "adaptive",
            "base_k": self.base_k,
            "category": self.category,
            "enable_adaptive_k": self.enable_adaptive_k,
            "min_k": self.min_k,
            "max_k": self.max_k,
            "enable_mmr": self.enable_mmr,
            "mmr_lambda": self.mmr_lambda,
            "mmr_fetch_k": self.mmr_fetch_k,
            "vectorstore_type": type(self.vectorstore).__name__
        }
    
    def update_config(
        self,
        base_k: Optional[int] = None,
        category: Optional[str] = None,
        enable_adaptive_k: Optional[bool] = None,
        enable_mmr: Optional[bool] = None,
        **kwargs
    ):
        """Update retriever configuration."""
        if base_k is not None:
            self.base_k = base_k
        if category is not None:
            self.category = category
        if enable_adaptive_k is not None:
            self.enable_adaptive_k = enable_adaptive_k
        if enable_mmr is not None:
            self.enable_mmr = enable_mmr
        
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
        
        logger.info(f"Updated retriever config: base_k={self.base_k}, category={self.category}")
    
    async def _aget_relevant_documents(
        self,
        query: str,
        *,
        run_manager: CallbackManagerForRetrieverRun,
        **kwargs
    ) -> List[Document]:
        """
        Async version of _get_relevant_documents.
        
        Note: Currently delegates to sync version as the underlying
        vector store doesn't support async operations.
        """
        return self._get_relevant_documents(query, run_manager=run_manager, **kwargs)


class MultimodalAdaptiveRetriever(AdaptiveRetriever):
    """
    Extended adaptive retriever with multimodal content awareness.
    
    This retriever can handle queries that might reference images or tables
    and adjust retrieval strategy accordingly.
    """
    
    def __init__(
        self,
        vectorstore: ChromaVectorStoreWrapper,
        enable_multimodal_filtering: bool = True,
        **kwargs
    ):
        """
        Initialize the multimodal adaptive retriever.
        
        Args:
            vectorstore: ChromaDB vector store wrapper
            enable_multimodal_filtering: Whether to filter by content type
            **kwargs: Additional arguments passed to AdaptiveRetriever
        """
        super().__init__(vectorstore, **kwargs)
        self.enable_multimodal_filtering = enable_multimodal_filtering
        
        logger.info("Initialized MultimodalAdaptiveRetriever")
    
    def _get_relevant_documents(
        self,
        query: str,
        *,
        run_manager: CallbackManagerForRetrieverRun,
        **kwargs
    ) -> List[Document]:
        """
        Retrieve relevant documents with multimodal awareness.
        """
        try:
            # Analyze query for multimodal content indicators
            multimodal_hints = self._analyze_multimodal_query(query)
            
            # Adjust retrieval strategy based on multimodal hints
            if multimodal_hints and self.enable_multimodal_filtering:
                kwargs = self._adjust_for_multimodal_content(kwargs, multimodal_hints)
            
            # Use parent class retrieval
            documents = super()._get_relevant_documents(query, run_manager=run_manager, **kwargs)
            
            # Post-process for multimodal content
            if multimodal_hints:
                documents = self._post_process_multimodal_results(documents, multimodal_hints)
            
            return documents
            
        except Exception as e:
            logger.error(f"Error in multimodal adaptive retrieval: {e}")
            return super()._get_relevant_documents(query, run_manager=run_manager, **kwargs)
    
    def _analyze_multimodal_query(self, query: str) -> Dict[str, bool]:
        """Analyze query for multimodal content indicators."""
        query_lower = query.lower()
        
        return {
            "has_image_references": any(word in query_lower for word in [
                "image", "picture", "figure", "photo", "diagram", "chart", "graph"
            ]),
            "has_table_references": any(word in query_lower for word in [
                "table", "data", "statistics", "numbers", "values", "rows", "columns"
            ]),
            "has_visual_references": any(word in query_lower for word in [
                "show", "display", "visual", "see", "look", "view"
            ])
        }
    
    def _adjust_for_multimodal_content(
        self,
        kwargs: Dict[str, Any],
        multimodal_hints: Dict[str, bool]
    ) -> Dict[str, Any]:
        """Adjust retrieval parameters for multimodal content."""
        # Increase k for multimodal queries to get more diverse content
        if any(multimodal_hints.values()):
            current_k = kwargs.get('k', self.base_k)
            kwargs['k'] = min(self.max_k, int(current_k * 1.2))
            logger.debug(f"Increased k for multimodal query: {kwargs['k']}")
        
        return kwargs
    
    def _post_process_multimodal_results(
        self,
        documents: List[Document],
        multimodal_hints: Dict[str, bool]
    ) -> List[Document]:
        """Post-process results to prioritize multimodal content."""
        try:
            # Separate documents by content type
            text_docs = []
            image_docs = []
            table_docs = []
            multimodal_docs = []
            
            for doc in documents:
                content_type = doc.metadata.get("content_type", "text")
                
                if content_type in ["image_caption", "image_analysis"]:
                    image_docs.append(doc)
                elif content_type in ["table_markdown", "table_context"]:
                    table_docs.append(doc)
                elif content_type == "multimodal_chunk":
                    multimodal_docs.append(doc)
                else:
                    text_docs.append(doc)
            
            # Reorder based on query hints
            reordered_docs = []
            
            # Prioritize multimodal chunks
            reordered_docs.extend(multimodal_docs)
            
            # Add specific content types based on hints
            if multimodal_hints.get("has_image_references"):
                reordered_docs.extend(image_docs)
            
            if multimodal_hints.get("has_table_references"):
                reordered_docs.extend(table_docs)
            
            # Add remaining text documents
            reordered_docs.extend(text_docs)
            
            # Add remaining image/table docs if not already included
            for doc in image_docs + table_docs:
                if doc not in reordered_docs:
                    reordered_docs.append(doc)
            
            return reordered_docs[:len(documents)]  # Maintain original count
            
        except Exception as e:
            logger.error(f"Error post-processing multimodal results: {e}")
            return documents


def get_adaptive_retriever(
    vectorstore: ChromaVectorStoreWrapper,
    base_k: int = 12,
    category: Optional[str] = None,
    enable_multimodal: bool = False,
    **kwargs
) -> AdaptiveRetriever:
    """
    Factory function to create an adaptive retriever.
    
    Args:
        vectorstore: ChromaDB vector store wrapper
        base_k: Base number of documents to retrieve
        category: Default category for retrieval
        enable_multimodal: Whether to use multimodal-aware retriever
        **kwargs: Additional arguments
        
    Returns:
        AdaptiveRetriever or MultimodalAdaptiveRetriever instance
    """
    if enable_multimodal:
        return MultimodalAdaptiveRetriever(
            vectorstore=vectorstore,
            base_k=base_k,
            category=category,
            **kwargs
        )
    else:
        return AdaptiveRetriever(
            vectorstore=vectorstore,
            base_k=base_k,
            category=category,
            **kwargs
        )

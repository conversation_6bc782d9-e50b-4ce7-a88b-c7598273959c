"""
Unified RAG Example

This example demonstrates the unified RAG service that can work with both
LlamaIndex and LangChain frameworks, sharing cached embeddings and vector storage.
"""

import os
import sys
import logging
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.services.unified_rag_service import get_unified_rag_service, RAGFramework

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def main():
    """Main example function."""
    print("🔄 Unified RAG Service Example")
    print("=" * 50)
    
    # Example PDF path (replace with your own)
    pdf_path = "data/temp/RESEARCH/sample_document.pdf"
    category = "RESEARCH"
    
    if not os.path.exists(pdf_path):
        print(f"❌ PDF file not found: {pdf_path}")
        print("Please place a PDF file at the specified path or update the path.")
        return
    
    try:
        # Step 1: Initialize unified RAG service
        print("\n🚀 Step 1: Initializing unified RAG service...")
        
        # Initialize with both frameworks
        unified_service = get_unified_rag_service(
            default_framework=RAGFramework.BOTH,
            category=category
        )
        
        service_info = unified_service.get_service_info()
        print(f"✅ Service initialized: {service_info}")
        
        # Step 2: Process document with both frameworks
        print("\n📄 Step 2: Processing document with both frameworks...")
        
        processing_result = unified_service.process_document(
            document_path=pdf_path,
            category=category,
            framework=RAGFramework.BOTH
        )
        
        print(f"✅ Document processed with framework: {processing_result['framework']}")
        
        # Display multimodal content summary
        multimodal_content = processing_result.get("multimodal_content", {})
        if multimodal_content:
            print("\n📊 Multimodal content extracted:")
            print(f"   - Text content: {len(multimodal_content.get('text_content', []))} items")
            print(f"   - Images: {len(multimodal_content.get('images', []))} items")
            print(f"   - Tables: {len(multimodal_content.get('tables', []))} items")
            print(f"   - Multimodal chunks: {len(multimodal_content.get('multimodal_chunks', []))} items")
        
        # Display LangChain processing results
        langchain_result = processing_result.get("langchain_processing")
        if langchain_result and "error" not in langchain_result:
            print("\n🦜 LangChain processing results:")
            print(f"   - Documents loaded: {langchain_result.get('documents_loaded', 0)}")
            print(f"   - Chunks created: {langchain_result.get('chunks_created', 0)}")
            print(f"   - Document IDs: {len(langchain_result.get('document_ids', []))}")
        
        # Display LlamaIndex processing results
        llamaindex_result = processing_result.get("llamaindex_processing")
        if llamaindex_result and "error" not in llamaindex_result:
            print("\n🦙 LlamaIndex processing results:")
            print(f"   - Processing completed: {llamaindex_result.get('framework')}")
        
        # Step 3: Test queries with different frameworks
        print("\n❓ Step 3: Testing queries with different frameworks...")
        
        test_queries = [
            "What is the main topic of this document?",
            "Are there any tables or charts in this document?",
            "What methodology was used in this research?",
            "Can you summarize the key findings?"
        ]
        
        for framework in [RAGFramework.LANGCHAIN, RAGFramework.LLAMAINDEX, RAGFramework.BOTH]:
            print(f"\n🔍 Testing with framework: {framework.value}")
            
            for i, query in enumerate(test_queries[:2], 1):  # Test first 2 queries
                print(f"\n   Query {i}: {query}")
                
                try:
                    query_result = unified_service.query(
                        question=query,
                        category=category,
                        framework=framework,
                        k=8
                    )
                    
                    # Display LangChain results
                    langchain_query_result = query_result.get("langchain_result")
                    if langchain_query_result and "error" not in langchain_query_result:
                        docs_count = langchain_query_result.get("documents_retrieved", 0)
                        print(f"      🦜 LangChain: {docs_count} documents retrieved")
                        
                        # Show first document
                        documents = langchain_query_result.get("documents", [])
                        if documents:
                            first_doc = documents[0]
                            content_preview = first_doc.get("content", "")[:100] + "..."
                            content_type = first_doc.get("metadata", {}).get("content_type", "unknown")
                            print(f"         📄 First result ({content_type}): {content_preview}")
                    
                    # Display LlamaIndex results
                    llamaindex_query_result = query_result.get("llamaindex_result")
                    if llamaindex_query_result and "error" not in llamaindex_query_result:
                        print(f"      🦙 LlamaIndex: Processing completed")
                        
                        # Show answer if available
                        answer_result = llamaindex_query_result.get("answer_result", {})
                        if isinstance(answer_result, dict) and "answer" in answer_result:
                            answer_preview = answer_result["answer"][:100] + "..."
                            print(f"         💬 Answer: {answer_preview}")
                
                except Exception as e:
                    print(f"      ❌ Error in query: {e}")
        
        # Step 4: Compare framework performance
        print("\n📊 Step 4: Framework performance comparison...")
        
        # Test same query with both frameworks
        test_query = "What are the main findings?"
        
        print(f"\n🔍 Comparing frameworks for query: {test_query}")
        
        # LangChain only
        langchain_result = unified_service.query(
            question=test_query,
            category=category,
            framework=RAGFramework.LANGCHAIN,
            k=10
        )
        
        # LlamaIndex only
        llamaindex_result = unified_service.query(
            question=test_query,
            category=category,
            framework=RAGFramework.LLAMAINDEX,
            k=10
        )
        
        # Display comparison
        print("\n📈 Performance comparison:")
        
        lc_result = langchain_result.get("langchain_result", {})
        if "error" not in lc_result:
            lc_docs = lc_result.get("documents_retrieved", 0)
            lc_retriever_info = lc_result.get("retriever_info", {})
            print(f"   🦜 LangChain: {lc_docs} docs, adaptive_k: {lc_retriever_info.get('enable_adaptive_k', False)}")
        
        li_result = llamaindex_result.get("llamaindex_result", {})
        if "error" not in li_result:
            print(f"   🦙 LlamaIndex: Processing completed")
        
        # Step 5: Test cache sharing
        print("\n💾 Step 5: Testing cache sharing between frameworks...")
        
        cache_stats = unified_service.get_cache_stats()
        print(f"📊 Cache statistics: {cache_stats}")
        
        # Test embedding cache sharing
        print("\n🔤 Testing embedding cache sharing...")
        
        # Query with LangChain first
        lc_query_result = unified_service.query(
            question="cache test query",
            category=category,
            framework=RAGFramework.LANGCHAIN,
            k=5
        )
        
        # Then query with LlamaIndex (should use cached embeddings)
        li_query_result = unified_service.query(
            question="cache test query",
            category=category,
            framework=RAGFramework.LLAMAINDEX,
            k=5
        )
        
        print("✅ Cache sharing test completed")
        
        # Step 6: Framework-specific features
        print("\n🎯 Step 6: Testing framework-specific features...")
        
        # Test LangChain-specific features
        print("\n🦜 LangChain-specific features:")
        if unified_service.langchain_retriever:
            retriever_info = unified_service.langchain_retriever.get_retriever_info()
            print(f"   - Adaptive retrieval: {retriever_info.get('enable_adaptive_k', False)}")
            print(f"   - MMR support: {retriever_info.get('enable_mmr', False)}")
            print(f"   - Multimodal awareness: {retriever_info.get('retriever_type') == 'adaptive'}")
        
        # Test LlamaIndex-specific features
        print("\n🦙 LlamaIndex-specific features:")
        if unified_service.llamaindex_rag_service:
            print("   - Enhanced RAG service: Available")
            print("   - Semantic chunking: Available")
            print("   - Multimodal processing: Available")
        
        print("\n✅ Unified RAG example completed successfully!")
        print("\n🎉 Key Features Demonstrated:")
        print("   - Unified service supporting both LlamaIndex and LangChain")
        print("   - Shared multimodal document processing")
        print("   - Cached embeddings shared between frameworks")
        print("   - Unified vector storage with category filtering")
        print("   - Framework-specific optimizations maintained")
        print("   - Performance monitoring across both frameworks")
        print("   - Adaptive retrieval with query complexity analysis")
        print("   - Seamless switching between frameworks")
        
    except Exception as e:
        logger.error(f"Error in unified RAG example: {e}")
        print(f"❌ Error: {e}")
        raise


if __name__ == "__main__":
    main()

"""
Multimodal Table Processing Service
Enhanced table extraction and processing with structure preservation
"""

import os
import logging
import json
import re
from typing import Dict, Any, List, Optional, Tuple
import fitz  # PyMuPDF
import pandas as pd
from pathlib import Path

from config.multimodal_config import get_multimodal_config
from app.services.multimodal_storage import get_multimodal_storage
from app.utils.performance_monitor import performance_monitor

# Import table extraction libraries
try:
    import camelot
    HAS_CAMELOT = True
except ImportError:
    HAS_CAMELOT = False

try:
    import tabula
    HAS_TABULA = True
except ImportError:
    HAS_TABULA = False

logger = logging.getLogger(__name__)

class MultimodalTableProcessor:
    """Enhanced table processor for multimodal RAG pipeline"""
    
    def __init__(self, config=None):
        self.config = config or get_multimodal_config()
        self.storage = get_multimodal_storage()
        
    @performance_monitor(track_memory=True, track_cpu=True, log_parameters=True, log_result_size=True)
    def extract_tables_from_pdf(self, pdf_path: str, document_id: str,
                               category: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Extract tables from PDF with structure preservation and multimodal storage
        
        Args:
            pdf_path: Path to the PDF file
            document_id: Unique identifier for the document
            category: Category for organizing content
            
        Returns:
            List of processed table information
        """
        if not self.config.table_processing.enable_table_extraction:
            logger.info("Table extraction is disabled")
            return []
        
        tables = []
        
        try:
            logger.info(f"Extracting tables from PDF: {pdf_path}")
            
            # Try PyMuPDF RAG first (built-in table detection)
            pymupdf_tables = self._extract_tables_with_pymupdf(pdf_path, document_id)
            tables.extend(pymupdf_tables)
            
            # Try Camelot for more accurate table extraction
            if self.config.table_processing.use_camelot and HAS_CAMELOT:
                camelot_tables = self._extract_tables_with_camelot(pdf_path, document_id)
                tables.extend(camelot_tables)
            
            # Try Tabula as fallback
            if self.config.table_processing.use_tabula and HAS_TABULA and not tables:
                tabula_tables = self._extract_tables_with_tabula(pdf_path, document_id)
                tables.extend(tabula_tables)
            
            # Remove duplicates and filter by quality
            tables = self._deduplicate_and_filter_tables(tables)
            
            # Limit number of tables
            if len(tables) > self.config.table_processing.max_tables_per_document:
                tables = tables[:self.config.table_processing.max_tables_per_document]
                logger.info(f"Limited tables to {self.config.table_processing.max_tables_per_document}")
            
            logger.info(f"Extracted {len(tables)} tables from PDF")
            return tables
            
        except Exception as e:
            logger.error(f"Error extracting tables from PDF {pdf_path}: {e}")
            return []
    
    def _extract_tables_with_pymupdf(self, pdf_path: str, document_id: str) -> List[Dict[str, Any]]:
        """Extract tables using PyMuPDF's built-in table detection"""
        tables = []
        
        try:
            doc = fitz.open(pdf_path)
            
            for page_num, page in enumerate(doc):
                try:
                    # Find tables on the page
                    page_tables = page.find_tables()
                    
                    for table_index, table in enumerate(page_tables):
                        table_data = self._process_pymupdf_table(
                            table, page_num + 1, table_index, document_id, pdf_path
                        )
                        if table_data:
                            tables.append(table_data)
                            
                except Exception as e:
                    logger.error(f"Error extracting tables from page {page_num + 1}: {e}")
                    continue
            
            doc.close()
            return tables
            
        except Exception as e:
            logger.error(f"Error in PyMuPDF table extraction: {e}")
            return []
    
    def _process_pymupdf_table(self, table, page_num: int, table_index: int,
                              document_id: str, pdf_path: str) -> Optional[Dict[str, Any]]:
        """Process a single table from PyMuPDF"""
        try:
            # Extract table data
            table_data = table.extract()
            
            if not table_data or len(table_data) < self.config.table_processing.min_table_rows:
                return None
            
            # Check minimum columns
            if len(table_data[0]) < self.config.table_processing.min_table_cols:
                return None
            
            # Convert to structured format
            structured_data = {
                "rows": table_data,
                "num_rows": len(table_data),
                "num_cols": len(table_data[0]) if table_data else 0
            }
            
            # Convert to markdown if enabled
            markdown_table = None
            if self.config.table_processing.table_to_markdown:
                markdown_table = self._convert_table_to_markdown(table_data)
            
            # Get table bounding box
            bbox = table.bbox
            
            # Prepare metadata
            metadata = {
                "extraction_method": "pymupdf",
                "page_num": page_num,
                "table_index": table_index,
                "bbox": {
                    "x0": bbox.x0,
                    "y0": bbox.y0,
                    "x1": bbox.x1,
                    "y1": bbox.y1
                },
                "num_rows": structured_data["num_rows"],
                "num_cols": structured_data["num_cols"],
                "source_pdf": os.path.basename(pdf_path)
            }
            
            # Get surrounding context if enabled
            context = None
            if self.config.table_processing.include_table_context:
                context = self._extract_table_context(pdf_path, page_num, bbox)
            
            # Prepare table data for storage
            table_storage_data = {
                "structured_data": structured_data,
                "markdown": markdown_table,
                "context": context
            }
            
            # Store table in multimodal storage
            storage_metadata = {
                **metadata,
                "context": context
            }
            
            content_hash = self.storage.store_table(
                table_storage_data, storage_metadata, document_id, page_num
            )
            
            if not content_hash:
                logger.error(f"Failed to store table {table_index} on page {page_num}")
                return None
            
            return {
                "content_hash": content_hash,
                "content_type": "table",
                "page_num": page_num,
                "table_index": table_index,
                "extraction_method": "pymupdf",
                "num_rows": structured_data["num_rows"],
                "num_cols": structured_data["num_cols"],
                "bbox": metadata["bbox"],
                "markdown": markdown_table,
                "context": context,
                "document_id": document_id
            }
            
        except Exception as e:
            logger.error(f"Error processing PyMuPDF table: {e}")
            return None
    
    def _extract_tables_with_camelot(self, pdf_path: str, document_id: str) -> List[Dict[str, Any]]:
        """Extract tables using Camelot"""
        tables = []
        
        try:
            # Extract with lattice mode (for tables with borders)
            lattice_tables = camelot.read_pdf(pdf_path, pages='all', flavor='lattice')
            
            for i, table in enumerate(lattice_tables):
                if table.df.empty:
                    continue
                
                table_data = self._process_camelot_table(
                    table, i, document_id, pdf_path, "lattice"
                )
                if table_data:
                    tables.append(table_data)
            
            # Extract with stream mode (for tables without clear borders)
            stream_tables = camelot.read_pdf(pdf_path, pages='all', flavor='stream')
            
            for i, table in enumerate(stream_tables):
                if table.df.empty:
                    continue
                
                table_data = self._process_camelot_table(
                    table, i + len(lattice_tables), document_id, pdf_path, "stream"
                )
                if table_data:
                    tables.append(table_data)
            
            return tables
            
        except Exception as e:
            logger.error(f"Error in Camelot table extraction: {e}")
            return []
    
    def _process_camelot_table(self, table, table_index: int, document_id: str,
                              pdf_path: str, extraction_flavor: str) -> Optional[Dict[str, Any]]:
        """Process a single table from Camelot"""
        try:
            # Convert DataFrame to list of lists
            table_data = table.df.values.tolist()
            
            # Add headers if they exist
            if not table.df.columns.empty:
                headers = table.df.columns.tolist()
                table_data.insert(0, headers)
            
            if len(table_data) < self.config.table_processing.min_table_rows:
                return None
            
            if len(table_data[0]) < self.config.table_processing.min_table_cols:
                return None
            
            # Get table metadata
            page_num = table.page
            
            # Convert to structured format
            structured_data = {
                "rows": table_data,
                "num_rows": len(table_data),
                "num_cols": len(table_data[0]) if table_data else 0,
                "accuracy": table.accuracy if hasattr(table, 'accuracy') else None
            }
            
            # Convert to markdown
            markdown_table = None
            if self.config.table_processing.table_to_markdown:
                markdown_table = self._convert_table_to_markdown(table_data)
            
            # Prepare metadata
            metadata = {
                "extraction_method": f"camelot_{extraction_flavor}",
                "page_num": page_num,
                "table_index": table_index,
                "accuracy": structured_data["accuracy"],
                "num_rows": structured_data["num_rows"],
                "num_cols": structured_data["num_cols"],
                "source_pdf": os.path.basename(pdf_path)
            }
            
            # Prepare table data for storage
            table_storage_data = {
                "structured_data": structured_data,
                "markdown": markdown_table,
                "dataframe_info": {
                    "shape": table.df.shape,
                    "columns": table.df.columns.tolist()
                }
            }
            
            # Store table
            content_hash = self.storage.store_table(
                table_storage_data, metadata, document_id, page_num
            )
            
            if not content_hash:
                return None
            
            return {
                "content_hash": content_hash,
                "content_type": "table",
                "page_num": page_num,
                "table_index": table_index,
                "extraction_method": f"camelot_{extraction_flavor}",
                "num_rows": structured_data["num_rows"],
                "num_cols": structured_data["num_cols"],
                "accuracy": structured_data["accuracy"],
                "markdown": markdown_table,
                "document_id": document_id
            }
            
        except Exception as e:
            logger.error(f"Error processing Camelot table: {e}")
            return None
    
    def _extract_tables_with_tabula(self, pdf_path: str, document_id: str) -> List[Dict[str, Any]]:
        """Extract tables using Tabula (fallback method)"""
        tables = []

        try:
            # Extract all tables from all pages
            dfs = tabula.read_pdf(pdf_path, pages='all', multiple_tables=True)

            for i, df in enumerate(dfs):
                if df.empty:
                    continue

                table_data = self._process_tabula_table(df, i, document_id, pdf_path)
                if table_data:
                    tables.append(table_data)

            return tables

        except Exception as e:
            logger.error(f"Error in Tabula table extraction: {e}")
            return []
    
    def _process_tabula_table(self, df: pd.DataFrame, table_index: int,
                             document_id: str, pdf_path: str) -> Optional[Dict[str, Any]]:
        """Process a single table from Tabula"""
        try:
            # Convert DataFrame to list of lists
            table_data = df.values.tolist()
            
            # Add headers
            if not df.columns.empty:
                headers = df.columns.tolist()
                table_data.insert(0, headers)
            
            if len(table_data) < self.config.table_processing.min_table_rows:
                return None
            
            if len(table_data[0]) < self.config.table_processing.min_table_cols:
                return None
            
            # Convert to structured format
            structured_data = {
                "rows": table_data,
                "num_rows": len(table_data),
                "num_cols": len(table_data[0]) if table_data else 0
            }
            
            # Convert to markdown
            markdown_table = None
            if self.config.table_processing.table_to_markdown:
                markdown_table = self._convert_table_to_markdown(table_data)
            
            # Prepare metadata (page number not available from Tabula)
            metadata = {
                "extraction_method": "tabula",
                "table_index": table_index,
                "num_rows": structured_data["num_rows"],
                "num_cols": structured_data["num_cols"],
                "source_pdf": os.path.basename(pdf_path)
            }
            
            # Prepare table data for storage
            table_storage_data = {
                "structured_data": structured_data,
                "markdown": markdown_table,
                "dataframe_info": {
                    "shape": df.shape,
                    "columns": df.columns.tolist()
                }
            }
            
            # Store table
            content_hash = self.storage.store_table(
                table_storage_data, metadata, document_id
            )
            
            if not content_hash:
                return None
            
            return {
                "content_hash": content_hash,
                "content_type": "table",
                "table_index": table_index,
                "extraction_method": "tabula",
                "num_rows": structured_data["num_rows"],
                "num_cols": structured_data["num_cols"],
                "markdown": markdown_table,
                "document_id": document_id
            }
            
        except Exception as e:
            logger.error(f"Error processing Tabula table: {e}")
            return None
    
    def _convert_table_to_markdown(self, table_data: List[List[str]]) -> str:
        """Convert table data to markdown format"""
        try:
            if not table_data:
                return ""
            
            markdown_lines = []
            
            # Add header row
            if table_data:
                header_row = "| " + " | ".join(str(cell) for cell in table_data[0]) + " |"
                markdown_lines.append(header_row)
                
                # Add separator
                separator = "| " + " | ".join("---" for _ in table_data[0]) + " |"
                markdown_lines.append(separator)
                
                # Add data rows
                for row in table_data[1:]:
                    data_row = "| " + " | ".join(str(cell) for cell in row) + " |"
                    markdown_lines.append(data_row)
            
            return "\n".join(markdown_lines)
            
        except Exception as e:
            logger.error(f"Error converting table to markdown: {e}")
            return ""
    
    def _extract_table_context(self, pdf_path: str, page_num: int, bbox) -> Optional[str]:
        """Extract text context around a table"""
        try:
            doc = fitz.open(pdf_path)
            page = doc[page_num - 1]  # Convert to 0-based index
            
            # Get text from the entire page
            page_text = page.get_text()
            
            # For now, return a portion of the page text as context
            # In a more sophisticated implementation, we could extract text
            # specifically around the table's bounding box
            
            words = page_text.split()
            context_words = min(self.config.table_processing.context_window_chars // 5, len(words))
            
            doc.close()
            return " ".join(words[:context_words])
            
        except Exception as e:
            logger.error(f"Error extracting table context: {e}")
            return None
    
    def _deduplicate_and_filter_tables(self, tables: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Remove duplicate tables and filter by quality"""
        try:
            # Simple deduplication based on content hash
            seen_hashes = set()
            unique_tables = []
            
            for table in tables:
                content_hash = table.get("content_hash")
                if content_hash and content_hash not in seen_hashes:
                    seen_hashes.add(content_hash)
                    unique_tables.append(table)
            
            # Sort by quality metrics (accuracy, size, etc.)
            def table_quality_score(table):
                score = 0
                score += table.get("num_rows", 0) * 0.1
                score += table.get("num_cols", 0) * 0.1
                if table.get("accuracy"):
                    score += table["accuracy"] * 0.8
                return score
            
            unique_tables.sort(key=table_quality_score, reverse=True)
            
            return unique_tables
            
        except Exception as e:
            logger.error(f"Error deduplicating tables: {e}")
            return tables

# Global processor instance
_multimodal_table_processor = None

def get_multimodal_table_processor() -> MultimodalTableProcessor:
    """Get the global multimodal table processor instance"""
    global _multimodal_table_processor
    if _multimodal_table_processor is None:
        _multimodal_table_processor = MultimodalTableProcessor()
    return _multimodal_table_processor

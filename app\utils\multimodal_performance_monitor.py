"""
Multimodal Performance Monitor
Extended performance monitoring for multimodal RAG operations
"""

import time
import logging
import psutil
import threading
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, field
from datetime import datetime
import json

from config.multimodal_config import get_multimodal_config
from app.utils.performance_monitor import performance_monitor

logger = logging.getLogger(__name__)

@dataclass
class MultimodalOperationMetrics:
    """Metrics for a multimodal operation"""
    operation_name: str
    start_time: float
    end_time: Optional[float] = None
    duration: Optional[float] = None
    
    # Content processing metrics
    documents_processed: int = 0
    images_processed: int = 0
    tables_processed: int = 0
    chunks_created: int = 0
    embeddings_generated: int = 0
    
    # Performance metrics
    memory_usage_mb: float = 0.0
    cpu_usage_percent: float = 0.0
    peak_memory_mb: float = 0.0
    
    # Content type breakdown
    content_type_distribution: Dict[str, int] = field(default_factory=dict)
    
    # Processing times by stage
    stage_timings: Dict[str, float] = field(default_factory=dict)
    
    # Error tracking
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    
    # Vision model metrics
    vision_model_calls: int = 0
    vision_model_total_time: float = 0.0
    vision_model_avg_time: float = 0.0
    
    # Storage metrics
    blobs_stored: int = 0
    total_blob_size_mb: float = 0.0
    cache_hits: int = 0
    cache_misses: int = 0

class MultimodalPerformanceMonitor:
    """Performance monitor for multimodal operations"""
    
    def __init__(self, config=None):
        self.config = config or get_multimodal_config()
        self.active_operations: Dict[str, MultimodalOperationMetrics] = {}
        self.completed_operations: List[MultimodalOperationMetrics] = []
        self.lock = threading.Lock()
        
    def start_operation(self, operation_name: str, operation_id: Optional[str] = None) -> str:
        """Start monitoring a multimodal operation"""
        if operation_id is None:
            operation_id = f"{operation_name}_{int(time.time() * 1000)}"
        
        with self.lock:
            metrics = MultimodalOperationMetrics(
                operation_name=operation_name,
                start_time=time.time()
            )
            self.active_operations[operation_id] = metrics
        
        logger.debug(f"Started monitoring operation: {operation_name} ({operation_id})")
        return operation_id
    
    def end_operation(self, operation_id: str) -> Optional[MultimodalOperationMetrics]:
        """End monitoring a multimodal operation"""
        with self.lock:
            if operation_id not in self.active_operations:
                logger.warning(f"Operation {operation_id} not found in active operations")
                return None
            
            metrics = self.active_operations.pop(operation_id)
            metrics.end_time = time.time()
            metrics.duration = metrics.end_time - metrics.start_time
            
            # Calculate vision model average time
            if metrics.vision_model_calls > 0:
                metrics.vision_model_avg_time = metrics.vision_model_total_time / metrics.vision_model_calls
            
            self.completed_operations.append(metrics)
        
        logger.info(f"Completed operation: {metrics.operation_name} in {metrics.duration:.2f}s")
        return metrics
    
    def update_operation_metrics(self, operation_id: str, **kwargs):
        """Update metrics for an active operation"""
        with self.lock:
            if operation_id not in self.active_operations:
                logger.warning(f"Operation {operation_id} not found for metrics update")
                return
            
            metrics = self.active_operations[operation_id]
            
            # Update basic counters
            for key in ['documents_processed', 'images_processed', 'tables_processed', 
                       'chunks_created', 'embeddings_generated', 'vision_model_calls',
                       'blobs_stored', 'cache_hits', 'cache_misses']:
                if key in kwargs:
                    setattr(metrics, key, getattr(metrics, key) + kwargs[key])
            
            # Update timing metrics
            if 'vision_model_time' in kwargs:
                metrics.vision_model_total_time += kwargs['vision_model_time']
            
            # Update size metrics
            if 'blob_size_mb' in kwargs:
                metrics.total_blob_size_mb += kwargs['blob_size_mb']
            
            # Update content type distribution
            if 'content_type' in kwargs:
                content_type = kwargs['content_type']
                metrics.content_type_distribution[content_type] = \
                    metrics.content_type_distribution.get(content_type, 0) + 1
            
            # Update stage timings
            if 'stage_timing' in kwargs:
                stage_name, timing = kwargs['stage_timing']
                metrics.stage_timings[stage_name] = timing
            
            # Update system metrics
            if 'memory_usage_mb' in kwargs:
                metrics.memory_usage_mb = kwargs['memory_usage_mb']
                metrics.peak_memory_mb = max(metrics.peak_memory_mb, metrics.memory_usage_mb)
            
            if 'cpu_usage_percent' in kwargs:
                metrics.cpu_usage_percent = kwargs['cpu_usage_percent']
            
            # Add errors and warnings
            if 'error' in kwargs:
                metrics.errors.append(kwargs['error'])
            
            if 'warning' in kwargs:
                metrics.warnings.append(kwargs['warning'])
    
    def get_operation_metrics(self, operation_id: str) -> Optional[MultimodalOperationMetrics]:
        """Get metrics for an operation"""
        with self.lock:
            if operation_id in self.active_operations:
                return self.active_operations[operation_id]
            
            # Search in completed operations
            for metrics in self.completed_operations:
                if f"{metrics.operation_name}_{int(metrics.start_time * 1000)}" == operation_id:
                    return metrics
        
        return None
    
    def get_system_metrics(self) -> Dict[str, Any]:
        """Get current system metrics"""
        try:
            process = psutil.Process()
            memory_info = process.memory_info()
            
            return {
                "memory_usage_mb": memory_info.rss / 1024 / 1024,
                "cpu_usage_percent": process.cpu_percent(),
                "timestamp": time.time()
            }
        except Exception as e:
            logger.error(f"Error getting system metrics: {e}")
            return {}
    
    def get_performance_summary(self, operation_name: Optional[str] = None) -> Dict[str, Any]:
        """Get performance summary for operations"""
        with self.lock:
            operations = self.completed_operations
            if operation_name:
                operations = [op for op in operations if op.operation_name == operation_name]
            
            if not operations:
                return {"message": "No completed operations found"}
            
            # Calculate aggregate metrics
            total_operations = len(operations)
            total_duration = sum(op.duration for op in operations if op.duration)
            avg_duration = total_duration / total_operations if total_operations > 0 else 0
            
            total_documents = sum(op.documents_processed for op in operations)
            total_images = sum(op.images_processed for op in operations)
            total_tables = sum(op.tables_processed for op in operations)
            total_chunks = sum(op.chunks_created for op in operations)
            total_embeddings = sum(op.embeddings_generated for op in operations)
            
            total_vision_calls = sum(op.vision_model_calls for op in operations)
            total_vision_time = sum(op.vision_model_total_time for op in operations)
            avg_vision_time = total_vision_time / total_vision_calls if total_vision_calls > 0 else 0
            
            total_blobs = sum(op.blobs_stored for op in operations)
            total_blob_size = sum(op.total_blob_size_mb for op in operations)
            
            total_cache_hits = sum(op.cache_hits for op in operations)
            total_cache_misses = sum(op.cache_misses for op in operations)
            cache_hit_rate = total_cache_hits / (total_cache_hits + total_cache_misses) if (total_cache_hits + total_cache_misses) > 0 else 0
            
            # Content type distribution
            content_type_totals = {}
            for op in operations:
                for content_type, count in op.content_type_distribution.items():
                    content_type_totals[content_type] = content_type_totals.get(content_type, 0) + count
            
            # Error summary
            total_errors = sum(len(op.errors) for op in operations)
            total_warnings = sum(len(op.warnings) for op in operations)
            
            return {
                "operation_name": operation_name or "all_operations",
                "summary_period": {
                    "start_time": min(op.start_time for op in operations),
                    "end_time": max(op.end_time for op in operations if op.end_time),
                    "total_operations": total_operations
                },
                "performance_metrics": {
                    "total_duration": total_duration,
                    "average_duration": avg_duration,
                    "operations_per_second": total_operations / total_duration if total_duration > 0 else 0
                },
                "content_processing": {
                    "total_documents": total_documents,
                    "total_images": total_images,
                    "total_tables": total_tables,
                    "total_chunks": total_chunks,
                    "total_embeddings": total_embeddings,
                    "content_type_distribution": content_type_totals
                },
                "vision_model_performance": {
                    "total_calls": total_vision_calls,
                    "total_time": total_vision_time,
                    "average_time_per_call": avg_vision_time,
                    "calls_per_second": total_vision_calls / total_duration if total_duration > 0 else 0
                },
                "storage_metrics": {
                    "total_blobs_stored": total_blobs,
                    "total_blob_size_mb": total_blob_size,
                    "average_blob_size_mb": total_blob_size / total_blobs if total_blobs > 0 else 0
                },
                "cache_performance": {
                    "total_hits": total_cache_hits,
                    "total_misses": total_cache_misses,
                    "hit_rate": cache_hit_rate
                },
                "error_summary": {
                    "total_errors": total_errors,
                    "total_warnings": total_warnings,
                    "error_rate": total_errors / total_operations if total_operations > 0 else 0
                }
            }
    
    def export_metrics(self, filepath: str, operation_name: Optional[str] = None):
        """Export metrics to JSON file"""
        try:
            summary = self.get_performance_summary(operation_name)
            
            with open(filepath, 'w') as f:
                json.dump(summary, f, indent=2, default=str)
            
            logger.info(f"Metrics exported to {filepath}")
            
        except Exception as e:
            logger.error(f"Error exporting metrics: {e}")
    
    def clear_completed_operations(self):
        """Clear completed operations to free memory"""
        with self.lock:
            cleared_count = len(self.completed_operations)
            self.completed_operations.clear()
        
        logger.info(f"Cleared {cleared_count} completed operations from memory")

# Global monitor instance
_multimodal_performance_monitor = None

def get_multimodal_performance_monitor() -> MultimodalPerformanceMonitor:
    """Get the global multimodal performance monitor instance"""
    global _multimodal_performance_monitor
    if _multimodal_performance_monitor is None:
        _multimodal_performance_monitor = MultimodalPerformanceMonitor()
    return _multimodal_performance_monitor

def multimodal_performance_monitor(operation_name: str):
    """Decorator for monitoring multimodal operations"""
    def decorator(func):
        def wrapper(*args, **kwargs):
            monitor = get_multimodal_performance_monitor()
            operation_id = monitor.start_operation(operation_name)
            
            try:
                # Update system metrics at start
                system_metrics = monitor.get_system_metrics()
                monitor.update_operation_metrics(operation_id, **system_metrics)
                
                # Execute function
                result = func(*args, **kwargs)
                
                # Update system metrics at end
                system_metrics = monitor.get_system_metrics()
                monitor.update_operation_metrics(operation_id, **system_metrics)
                
                return result
                
            except Exception as e:
                monitor.update_operation_metrics(operation_id, error=str(e))
                raise
            finally:
                monitor.end_operation(operation_id)
        
        return wrapper
    return decorator

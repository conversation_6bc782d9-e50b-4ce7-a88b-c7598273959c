"""
Multimodal RAG Pipeline Example
Demonstrates how to use the enhanced RAG pipeline with multimodal content
"""

import os
import sys
import logging
from pathlib import Path

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from config.multimodal_config import get_multimodal_config, MultimodalRAGConfig
from app.services.multimodal_document_processor import get_multimodal_document_processor
from app.services.multimodal_chunking_service import get_multimodal_chunking_service
from app.services.multimodal_embedding_service import get_multimodal_embedding_service
from app.services.hybrid_search_service import get_hybrid_search_service
from app.services.context_assembler import get_context_assembler

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def setup_multimodal_config():
    """Setup multimodal configuration for the example"""
    config = MultimodalRAGConfig()
    
    # Enable all multimodal features
    config.enable_multimodal = True
    config.image_processing.enable_image_extraction = True
    config.image_processing.enable_vision_captions = True
    config.table_processing.enable_table_extraction = True
    config.chunking.enable_multimodal_chunking = True
    config.search.enable_hybrid_search = True
    
    # Configure vision model (using local Ollama)
    config.vision_model.model_name = "llama3.2-vision"
    config.vision_model.base_url = "http://localhost:11434"
    
    # Configure processing
    config.enable_parallel_processing = True
    config.max_workers = 4
    
    # Configure storage
    config.storage.blob_storage_path = "./data/multimodal_blobs"
    config.storage.compress_images = True
    
    # Set debug mode for detailed logging
    config.debug_mode = True
    config.log_processing_steps = True
    
    return config

def process_multimodal_document(document_path: str, category: str = "MULTIMODAL_TEST"):
    """
    Process a document with multimodal content extraction
    
    Args:
        document_path: Path to the document to process
        category: Category for organizing content
        
    Returns:
        Processing results
    """
    logger.info(f"Processing document: {document_path}")
    
    # Get the document processor
    processor = get_multimodal_document_processor()
    
    # Process the document
    result = processor.process_document(
        document_path=document_path,
        category=category
    )
    
    if not result:
        logger.error("Document processing failed")
        return None
    
    logger.info(f"Document processing completed:")
    logger.info(f"  - Text content: {len(result.get('text_content', []))} items")
    logger.info(f"  - Images: {len(result.get('images', []))} items")
    logger.info(f"  - Tables: {len(result.get('tables', []))} items")
    logger.info(f"  - Multimodal chunks: {len(result.get('multimodal_chunks', []))} items")
    
    return result

def create_multimodal_chunks(processing_result):
    """
    Create multimodal chunks from processing result
    
    Args:
        processing_result: Result from document processing
        
    Returns:
        List of LangChain documents
    """
    logger.info("Creating multimodal chunks")
    
    # Get the chunking service
    chunking_service = get_multimodal_chunking_service()
    
    # Create chunks
    documents = chunking_service.chunk_multimodal_document(processing_result)
    
    logger.info(f"Created {len(documents)} multimodal chunks")
    
    # Log chunk types
    chunk_types = {}
    for doc in documents:
        chunk_type = doc.metadata.get("chunk_type", "unknown")
        chunk_types[chunk_type] = chunk_types.get(chunk_type, 0) + 1
    
    logger.info(f"Chunk types: {chunk_types}")
    
    return documents

def generate_multimodal_embeddings(documents, category: str = "MULTIMODAL_TEST"):
    """
    Generate embeddings for multimodal documents
    
    Args:
        documents: List of LangChain documents
        category: Category for organizing content
        
    Returns:
        Embedding results
    """
    logger.info("Generating multimodal embeddings")
    
    # Get the embedding service
    embedding_service = get_multimodal_embedding_service()
    
    # Generate embeddings and store in vector database
    result = embedding_service.embed_multimodal_documents(documents, category)
    
    if "error" in result:
        logger.error(f"Embedding generation failed: {result['error']}")
        return None
    
    logger.info(f"Embedding generation completed:")
    logger.info(f"  - Total documents: {result.get('total_documents', 0)}")
    logger.info(f"  - Documents by type: {result.get('documents_by_type', {})}")
    
    return result

def perform_hybrid_search(query: str, category: str = "MULTIMODAL_TEST", k: int = 10):
    """
    Perform hybrid search across multimodal content
    
    Args:
        query: Search query
        category: Category to search within
        k: Number of results to return
        
    Returns:
        Search results
    """
    logger.info(f"Performing hybrid search for: {query}")
    
    # Get the search service
    search_service = get_hybrid_search_service()
    
    # Perform search
    results = search_service.search_multimodal(
        query=query,
        category=category,
        k=k
    )
    
    if "error" in results:
        logger.error(f"Search failed: {results['error']}")
        return None
    
    logger.info(f"Search completed:")
    logger.info(f"  - Total results: {results.get('total_results', 0)}")
    logger.info(f"  - Results by type: {results.get('results_by_type', {})}")
    logger.info(f"  - Merged results: {len(results.get('merged_results', []))}")
    
    return results

def assemble_multimodal_context(search_results, query: str):
    """
    Assemble multimodal context from search results
    
    Args:
        search_results: Results from hybrid search
        query: Original search query
        
    Returns:
        Assembled context
    """
    logger.info("Assembling multimodal context")
    
    # Get the context assembler
    assembler = get_context_assembler()
    
    # Assemble context
    context = assembler.assemble_multimodal_context(search_results, query)
    
    metadata = context.get("context_metadata", {})
    logger.info(f"Context assembly completed:")
    logger.info(f"  - Total sources: {metadata.get('total_sources', 0)}")
    logger.info(f"  - Content types: {metadata.get('content_types', [])}")
    logger.info(f"  - Text length: {metadata.get('text_length', 0)}")
    logger.info(f"  - Images: {metadata.get('num_images', 0)}")
    logger.info(f"  - Tables: {metadata.get('num_tables', 0)}")
    
    return context

def format_context_for_display(context):
    """Format context for display"""
    assembler = get_context_assembler()
    formatted_context = assembler.format_context_for_llm(context)
    return formatted_context

def main():
    """Main example function"""
    logger.info("Starting Multimodal RAG Pipeline Example")

    # Setup configuration
    config = setup_multimodal_config()
    logger.info("Multimodal configuration setup completed")

    # Example document path (replace with your actual document)
    document_path = "./test_documents/sample_document.pdf"

    # Check if document exists
    if not os.path.exists(document_path):
        logger.warning(f"Document not found: {document_path}")
        logger.info("Please place a PDF document at the specified path to run the example")
        logger.info("Creating test documents directory...")
        os.makedirs("./test_documents", exist_ok=True)
        return
    
    try:
        # Step 1: Process document with multimodal extraction
        processing_result = process_multimodal_document(document_path)
        if not processing_result:
            return
        
        # Step 2: Create multimodal chunks
        documents = create_multimodal_chunks(processing_result)
        if not documents:
            logger.error("No chunks created")
            return
        
        # Step 3: Generate embeddings and store in vector database
        embedding_result = generate_multimodal_embeddings(documents)
        if not embedding_result:
            return
        
        # Step 4: Perform hybrid search
        test_queries = [
            "What images are shown in the document?",
            "Show me any tables with data",
            "What is the main content of this document?",
            "Are there any charts or diagrams?"
        ]
        
        for query in test_queries:
            logger.info(f"\n{'='*50}")
            logger.info(f"Testing query: {query}")
            logger.info(f"{'='*50}")
            
            # Perform search
            search_results = perform_hybrid_search(query)
            if not search_results:
                continue
            
            # Assemble context
            context = assemble_multimodal_context(search_results, query)
            
            # Format for display
            formatted_context = format_context_for_display(context)
            
            logger.info(f"\nFormatted Context for Query: {query}")
            logger.info(f"{'='*30}")
            logger.info(formatted_context[:500] + "..." if len(formatted_context) > 500 else formatted_context)
            
        logger.info("\nMultimodal RAG Pipeline Example completed successfully!")
        
    except Exception as e:
        logger.error(f"Error in multimodal RAG example: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()

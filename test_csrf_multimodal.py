#!/usr/bin/env python3
"""
Test script to verify CSRF token implementation for multimodal test endpoint
"""

import requests
import json
import sys

def test_multimodal_csrf():
    """Test the multimodal test endpoint with CSRF token handling"""
    
    base_url = "http://localhost:8080"
    
    print("=== Testing Multimodal Test Endpoint CSRF Implementation ===")
    
    # First, get a CSRF token
    print("\n1. Getting CSRF token...")
    try:
        response = requests.get(f"{base_url}/api/csrf-token")
        if response.status_code == 200:
            csrf_data = response.json()
            csrf_token = csrf_data.get('csrf_token')
            print(f"✓ CSRF token obtained: {csrf_token[:20]}...")
        else:
            print(f"✗ Failed to get CSRF token: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ Error getting CSRF token: {e}")
        return False
    
    # Test the multimodal test endpoint with CSRF token
    print("\n2. Testing multimodal test endpoint with CSRF token...")
    try:
        # Create a simple test file
        test_file_content = b"%PDF-1.4\n%Test PDF content\n"
        
        headers = {
            'X-CSRFToken': csrf_token
        }
        
        files = {
            'file': ('test.pdf', test_file_content, 'application/pdf')
        }
        
        response = requests.post(f"{base_url}/api/multimodal/test", 
                               headers=headers, 
                               files=files)
        
        print(f"Response status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✓ Multimodal test endpoint works with CSRF token!")
            print(f"Response: {json.dumps(data, indent=2)}")
            return True
        elif response.status_code == 400:
            error_data = response.json()
            if 'CSRF' in str(error_data):
                print("✗ CSRF token still not working")
                print(f"Error: {error_data}")
                return False
            else:
                print(f"✗ Other error: {error_data}")
                return False
        else:
            print(f"✗ Unexpected status code: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"✗ Error testing endpoint: {e}")
        return False

def test_metrics_reset_csrf():
    """Test the multimodal metrics reset endpoint with CSRF token handling"""
    
    base_url = "http://localhost:8080"
    
    print("\n3. Testing multimodal metrics reset endpoint with CSRF token...")
    
    # First, get a CSRF token
    try:
        response = requests.get(f"{base_url}/api/csrf-token")
        if response.status_code == 200:
            csrf_data = response.json()
            csrf_token = csrf_data.get('csrf_token')
            print(f"✓ CSRF token obtained: {csrf_token[:20]}...")
        else:
            print(f"✗ Failed to get CSRF token: {response.status_code}")
            return False
    except Exception as e:
        print(f"✗ Error getting CSRF token: {e}")
        return False
    
    # Test the metrics reset endpoint with CSRF token
    try:
        headers = {
            'Content-Type': 'application/json',
            'X-CSRFToken': csrf_token
        }
        
        response = requests.post(f"{base_url}/api/multimodal/metrics/reset", 
                               headers=headers)
        
        print(f"Response status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✓ Multimodal metrics reset endpoint works with CSRF token!")
            print(f"Response: {json.dumps(data, indent=2)}")
            return True
        elif response.status_code == 400:
            error_data = response.json()
            if 'CSRF' in str(error_data):
                print("✗ CSRF token still not working")
                print(f"Error: {error_data}")
                return False
            else:
                print(f"✗ Other error: {error_data}")
                return False
        else:
            print(f"✗ Unexpected status code: {response.status_code}")
            print(f"Response: {response.text}")
            return False
            
    except Exception as e:
        print(f"✗ Error testing metrics reset endpoint: {e}")
        return False

def test_without_csrf():
    """Test the endpoints without CSRF token (should fail)"""
    
    base_url = "http://localhost:8080"
    
    print("\n4. Testing endpoints without CSRF token (should fail)...")
    
    # Test multimodal test endpoint without CSRF
    print("\n   Testing /api/multimodal/test without CSRF token...")
    try:
        test_file_content = b"%PDF-1.4\n%Test PDF content\n"
        
        files = {
            'file': ('test.pdf', test_file_content, 'application/pdf')
        }
        
        response = requests.post(f"{base_url}/api/multimodal/test", 
                               files=files)
        
        print(f"   Response status: {response.status_code}")
        
        if response.status_code == 400:
            error_data = response.json()
            if 'CSRF' in str(error_data):
                print("   ✓ Correctly rejected test endpoint without CSRF token")
            else:
                print(f"   ✗ Unexpected error: {error_data}")
                return False
        else:
            print(f"   ✗ Should have failed with 400, got {response.status_code}")
            return False
    except Exception as e:
        print(f"   ✗ Error testing test endpoint without CSRF: {e}")
        return False
    
    # Test multimodal metrics reset endpoint without CSRF
    print("\n   Testing /api/multimodal/metrics/reset without CSRF token...")
    try:
        headers = {
            'Content-Type': 'application/json'
        }
        
        response = requests.post(f"{base_url}/api/multimodal/metrics/reset", 
                               headers=headers)
        
        print(f"   Response status: {response.status_code}")
        
        if response.status_code == 400:
            error_data = response.json()
            if 'CSRF' in str(error_data):
                print("   ✓ Correctly rejected metrics reset endpoint without CSRF token")
                return True
            else:
                print(f"   ✗ Unexpected error: {error_data}")
                return False
        else:
            print(f"   ✗ Should have failed with 400, got {response.status_code}")
            return False
    except Exception as e:
        print(f"   ✗ Error testing metrics reset endpoint without CSRF: {e}")
        return False

if __name__ == "__main__":
    print("Starting CSRF token test for multimodal endpoints...")
    
    # Test multimodal test endpoint with CSRF token
    success1 = test_multimodal_csrf()
    
    # Test multimodal metrics reset endpoint with CSRF token
    success2 = test_metrics_reset_csrf()
    
    # Test endpoints without CSRF token (should fail)
    success3 = test_without_csrf()
    
    print("\n=== Test Results ===")
    print(f"Multimodal test endpoint with CSRF: {'✓ PASS' if success1 else '✗ FAIL'}")
    print(f"Multimodal metrics reset with CSRF: {'✓ PASS' if success2 else '✗ FAIL'}")
    print(f"Endpoints without CSRF (should fail): {'✓ PASS' if success3 else '✗ FAIL'}")
    
    if success1 and success2 and success3:
        print("\n🎉 All tests passed! CSRF implementation is working correctly.")
        sys.exit(0)
    else:
        print("\n❌ Some tests failed. Please check the implementation.")
        sys.exit(1) 
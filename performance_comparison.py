#!/usr/bin/env python3
"""
Performance Comparison Script

This script compares the performance and quality of the old vs new chunking implementations.
"""

import os
import sys
import time
import logging
from pathlib import Path
from typing import List, Dict, Any
import json
from langchain.schema import Document

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def compare_chunking_methods():
    """Compare old vs new chunking methods"""
    logger.info("🔍 Comparing chunking methods...")

    # Sample document for testing
    sample_text = """
    # Machine Learning in Healthcare

    ## Introduction

    Machine learning (ML) has revolutionized healthcare by enabling predictive analytics,
    personalized treatment plans, and automated diagnostic systems. This comprehensive
    review examines the current state of ML applications in healthcare, focusing on
    deep learning algorithms, natural neural networks, and their implementation in
    clinical decision support systems.

    ## Methodology

    Our analysis encompasses the following key areas:

    1. **Data Collection**: We analyzed over 10,000 patient records from multiple
       healthcare institutions, ensuring compliance with HIPAA regulations.

    2. **Algorithm Development**: We implemented several ML algorithms including:
       - Random Forest classifiers
       - Support Vector Machines (SVM)
       - Convolutional Neural Networks (CNN)
       - Long Short-Term Memory (LSTM) networks

    3. **Performance Evaluation**: Models were evaluated using standard metrics:
       - Accuracy: 94.2%
       - Precision: 91.8%
       - Recall: 93.5%
       - F1-Score: 92.6%

    ## Results and Discussion

    The implementation of ML algorithms showed significant improvements in diagnostic
    accuracy compared to traditional methods. Table 1 summarizes the performance
    metrics across different algorithms.

    | Algorithm | Accuracy | Precision | Recall | F1-Score |
    |-----------|----------|-----------|--------|----------|
    | Random Forest | 89.3% | 87.1% | 88.9% | 88.0% |
    | SVM | 91.7% | 89.4% | 90.8% | 90.1% |
    | CNN | 94.2% | 91.8% | 93.5% | 92.6% |
    | LSTM | 92.8% | 90.2% | 91.9% | 91.0% |

    ### Clinical Applications

    The developed models have been successfully deployed in several clinical scenarios:

    - **Radiology**: Automated detection of anomalies in X-rays and MRI scans
    - **Pathology**: Classification of tissue samples and cancer detection
    - **Cardiology**: ECG analysis and arrhythmia detection
    - **Emergency Medicine**: Triage prioritization and risk assessment

    ## Conclusion

    Machine learning represents a paradigm shift in healthcare delivery, offering
    unprecedented opportunities for improving patient outcomes while reducing costs.
    Future research should focus on addressing ethical considerations, ensuring
    algorithmic fairness, and developing robust validation frameworks.

    ## References

    [1] Smith, J. et al. (2023). "Deep Learning in Medical Imaging: A Comprehensive Review"
    [2] Johnson, A. & Brown, K. (2022). "Ethical AI in Healthcare: Challenges and Solutions"
    [3] Davis, M. et al. (2023). "Federated Learning for Healthcare: Privacy-Preserving ML"
    """

    # Create test document
    test_doc = Document(
        page_content=sample_text,
        metadata={'page': 1, 'source': 'test_paper.pdf', 'type': 'pdf'}
    )

    # Test old method (RecursiveCharacterTextSplitter)
    logger.info("Testing old chunking method...")
    old_results = test_old_chunking([test_doc])

    # Test new method (Enhanced Chunking)
    logger.info("Testing new enhanced chunking method...")
    new_results = test_new_chunking([test_doc])

    # Compare results
    comparison = compare_results(old_results, new_results)

    # Display comparison
    display_comparison(comparison)

    return comparison

def test_old_chunking(documents: List[Document]) -> Dict[str, Any]:
    """Test the old RecursiveCharacterTextSplitter method"""
    from langchain.text_splitter import RecursiveCharacterTextSplitter

    start_time = time.time()

    # Old method
    splitter = RecursiveCharacterTextSplitter(chunk_size=800, chunk_overlap=250)
    chunks = splitter.split_documents(documents)

    processing_time = time.time() - start_time

    # Analyze chunks
    chunk_sizes = [len(chunk.page_content) for chunk in chunks]

    return {
        'method': 'RecursiveCharacterTextSplitter',
        'processing_time': processing_time,
        'chunk_count': len(chunks),
        'avg_chunk_size': sum(chunk_sizes) / len(chunk_sizes) if chunk_sizes else 0,
        'min_chunk_size': min(chunk_sizes) if chunk_sizes else 0,
        'max_chunk_size': max(chunk_sizes) if chunk_sizes else 0,
        'chunks': chunks,
        'metadata_richness': calculate_metadata_richness(chunks),
        'structure_preservation': 0  # Old method doesn't preserve structure
    }

def test_new_chunking(documents: List[Document]) -> Dict[str, Any]:
    """Test the new enhanced chunking method"""
    from app.services.enhanced_chunking_service import EnhancedChunkingService
    from app.services.document_structure_preserver import DocumentStructurePreserver

    start_time = time.time()

    # New method
    chunking_service = EnhancedChunkingService()
    structure_preserver = DocumentStructurePreserver()

    # Detect content type
    sample_text = ' '.join([doc.page_content[:500] for doc in documents])
    content_type = chunking_service.content_detector.detect_content_type(sample_text)

    # Apply adaptive chunking
    chunks = chunking_service.adaptive_chunk(documents, content_type)

    # Enhance with structure preservation
    enhanced_chunks = []
    for chunk in chunks:
        enhanced_chunk = structure_preserver.enhance_chunk_metadata(chunk)
        enhanced_chunks.append(enhanced_chunk)

    processing_time = time.time() - start_time

    # Analyze chunks
    chunk_sizes = [len(chunk.page_content) for chunk in enhanced_chunks]

    return {
        'method': 'Enhanced Adaptive Chunking',
        'processing_time': processing_time,
        'chunk_count': len(enhanced_chunks),
        'avg_chunk_size': sum(chunk_sizes) / len(chunk_sizes) if chunk_sizes else 0,
        'min_chunk_size': min(chunk_sizes) if chunk_sizes else 0,
        'max_chunk_size': max(chunk_sizes) if chunk_sizes else 0,
        'chunks': enhanced_chunks,
        'content_type_detected': content_type,
        'metadata_richness': calculate_metadata_richness(enhanced_chunks),
        'structure_preservation': calculate_structure_preservation(enhanced_chunks)
    }

def calculate_metadata_richness(chunks: List[Document]) -> float:
    """Calculate the richness of metadata in chunks"""
    if not chunks:
        return 0.0

    total_metadata_fields = 0
    for chunk in chunks:
        total_metadata_fields += len(chunk.metadata.keys())

    avg_metadata_fields = total_metadata_fields / len(chunks)

    # Normalize to 0-1 scale (assuming max 20 metadata fields is very rich)
    return min(avg_metadata_fields / 20.0, 1.0)

def calculate_structure_preservation(chunks: List[Document]) -> float:
    """Calculate how well document structure is preserved"""
    if not chunks:
        return 0.0

    structure_indicators = 0
    total_chunks = len(chunks)

    for chunk in chunks:
        metadata = chunk.metadata

        # Check for structure-related metadata
        if metadata.get('has_headers', False):
            structure_indicators += 1
        if metadata.get('has_lists', False):
            structure_indicators += 1
        if metadata.get('has_tables', False):
            structure_indicators += 1
        if metadata.get('section_hierarchy'):
            structure_indicators += 1
        if metadata.get('semantic_density', 0) > 0:
            structure_indicators += 1

    # Normalize to 0-1 scale
    return min(structure_indicators / (total_chunks * 5), 1.0)

def compare_results(old_results: Dict[str, Any], new_results: Dict[str, Any]) -> Dict[str, Any]:
    """Compare old vs new results"""

    # Calculate improvements
    processing_time_improvement = (
        (old_results['processing_time'] - new_results['processing_time']) /
        old_results['processing_time'] * 100
        if old_results['processing_time'] > 0 else 0
    )

    chunk_count_change = new_results['chunk_count'] - old_results['chunk_count']

    metadata_improvement = (
        new_results['metadata_richness'] - old_results['metadata_richness']
    ) * 100

    structure_improvement = (
        new_results['structure_preservation'] - old_results['structure_preservation']
    ) * 100

    return {
        'old_method': old_results,
        'new_method': new_results,
        'improvements': {
            'processing_time_change_percent': processing_time_improvement,
            'chunk_count_change': chunk_count_change,
            'metadata_richness_improvement_percent': metadata_improvement,
            'structure_preservation_improvement_percent': structure_improvement
        }
    }

def display_comparison(comparison: Dict[str, Any]):
    """Display the comparison results in a formatted way"""

    old = comparison['old_method']
    new = comparison['new_method']
    improvements = comparison['improvements']

    print("\n" + "="*80)
    print("📊 CHUNKING METHOD COMPARISON RESULTS")
    print("="*80)

    print(f"\n🔧 OLD METHOD: {old['method']}")
    print(f"   ⏱️  Processing Time: {old['processing_time']:.4f} seconds")
    print(f"   📄 Chunk Count: {old['chunk_count']}")
    print(f"   📏 Avg Chunk Size: {old['avg_chunk_size']:.0f} characters")
    print(f"   📊 Metadata Richness: {old['metadata_richness']:.2f}")
    print(f"   🏗️  Structure Preservation: {old['structure_preservation']:.2f}")

    print(f"\n✨ NEW METHOD: {new['method']}")
    print(f"   ⏱️  Processing Time: {new['processing_time']:.4f} seconds")
    print(f"   📄 Chunk Count: {new['chunk_count']}")
    print(f"   📏 Avg Chunk Size: {new['avg_chunk_size']:.0f} characters")
    print(f"   🎯 Content Type Detected: {new.get('content_type_detected', 'N/A')}")
    print(f"   📊 Metadata Richness: {new['metadata_richness']:.2f}")
    print(f"   🏗️  Structure Preservation: {new['structure_preservation']:.2f}")

    print(f"\n🚀 IMPROVEMENTS:")

    if improvements['processing_time_change_percent'] > 0:
        print(f"   ⚡ Processing Speed: {improvements['processing_time_change_percent']:.1f}% faster")
    elif improvements['processing_time_change_percent'] < 0:
        print(f"   ⏳ Processing Speed: {abs(improvements['processing_time_change_percent']):.1f}% slower")
    else:
        print(f"   ⏱️  Processing Speed: No significant change")

    print(f"   📄 Chunk Count Change: {improvements['chunk_count_change']:+d}")
    print(f"   📊 Metadata Richness: {improvements['metadata_richness_improvement_percent']:+.1f}%")
    print(f"   🏗️  Structure Preservation: {improvements['structure_preservation_improvement_percent']:+.1f}%")

    print(f"\n🎯 KEY BENEFITS OF NEW METHOD:")
    print(f"   ✅ Content-type aware chunking")
    print(f"   ✅ Enhanced metadata with structural information")
    print(f"   ✅ Document hierarchy preservation")
    print(f"   ✅ Semantic density calculation")
    print(f"   ✅ Quality scoring for chunks")
    print(f"   ✅ Adaptive optimization based on document characteristics")

    # Show sample enhanced metadata
    if new['chunks']:
        sample_chunk = new['chunks'][0]
        enhanced_metadata = {k: v for k, v in sample_chunk.metadata.items()
                           if k.startswith(('has_', 'chunk_', 'semantic_', 'structure_', 'content_type'))}

        if enhanced_metadata:
            print(f"\n📋 SAMPLE ENHANCED METADATA:")
            for key, value in enhanced_metadata.items():
                print(f"   {key}: {value}")

    print("\n" + "="*80)

def main():
    """Run the performance comparison"""
    logger.info("🚀 Starting performance comparison...")

    try:
        comparison = compare_chunking_methods()

        # Save results to file
        results_file = "chunking_comparison_results.json"
        with open(results_file, 'w') as f:
            # Convert Document objects to dict for JSON serialization
            serializable_comparison = {
                'old_method': {k: v for k, v in comparison['old_method'].items() if k != 'chunks'},
                'new_method': {k: v for k, v in comparison['new_method'].items() if k != 'chunks'},
                'improvements': comparison['improvements']
            }
            json.dump(serializable_comparison, f, indent=2)

        logger.info(f"📁 Results saved to {results_file}")
        logger.info("✅ Performance comparison completed successfully!")

        return True

    except Exception as e:
        logger.error(f"❌ Performance comparison failed: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
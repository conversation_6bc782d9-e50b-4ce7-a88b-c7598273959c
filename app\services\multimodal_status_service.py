"""
Multimodal Pipeline Status and Monitoring Service
Provides comprehensive status checking and monitoring for the multimodal RAG pipeline
"""

import os
import json
import time
import logging
import requests
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from pathlib import Path
import threading
from dataclasses import dataclass, asdict

logger = logging.getLogger(__name__)

@dataclass
class MultimodalStatus:
    """Status information for multimodal pipeline components"""
    enabled: bool = False
    vision_model_available: bool = False
    vision_model_name: str = ""
    vision_model_status: str = "unknown"
    storage_accessible: bool = False
    storage_path: str = ""
    dependencies_available: bool = False
    processing_active: bool = False
    last_check: str = ""
    errors: List[str] = None
    
    def __post_init__(self):
        if self.errors is None:
            self.errors = []

@dataclass
class ProcessingMetrics:
    """Metrics for multimodal processing operations"""
    total_documents: int = 0
    total_text_chunks: int = 0
    total_images_extracted: int = 0
    total_tables_extracted: int = 0
    total_processing_time: float = 0.0
    average_processing_time: float = 0.0
    success_rate: float = 0.0
    last_processed: str = ""
    errors_count: int = 0
    
class MultimodalStatusService:
    """Service for monitoring multimodal pipeline status and performance"""
    
    def __init__(self):
        self.status = MultimodalStatus()
        self.metrics = ProcessingMetrics()
        self.metrics_file = "data/multimodal_metrics.json"
        self.status_cache_duration = 30  # seconds
        self.last_status_check = 0
        self._lock = threading.Lock()
        
        # Ensure metrics directory exists
        os.makedirs(os.path.dirname(self.metrics_file), exist_ok=True)
        self.load_metrics()
    
    def get_status(self, force_refresh: bool = False) -> Dict[str, Any]:
        """Get current multimodal pipeline status"""
        current_time = time.time()
        
        # Use cached status if recent and not forcing refresh
        if not force_refresh and (current_time - self.last_status_check) < self.status_cache_duration:
            return asdict(self.status)
        
        with self._lock:
            self._check_multimodal_status()
            self.last_status_check = current_time
            
        return asdict(self.status)
    
    def _check_multimodal_status(self):
        """Perform comprehensive status check of multimodal pipeline"""
        self.status = MultimodalStatus()
        self.status.last_check = datetime.now().isoformat()
        
        try:
            # Check if multimodal is enabled in configuration
            self._check_configuration()
            
            # Check vision model availability
            self._check_vision_model()
            
            # Check storage accessibility
            self._check_storage()
            
            # Check dependencies
            self._check_dependencies()
            
            # Check current processing status
            self._check_processing_status()
            
        except Exception as e:
            logger.error(f"Error checking multimodal status: {e}")
            self.status.errors.append(f"Status check failed: {str(e)}")
    
    def _check_configuration(self):
        """Check multimodal configuration"""
        try:
            from config.multimodal_config import get_multimodal_config
            config = get_multimodal_config()
            
            self.status.enabled = config.enable_multimodal
            if not self.status.enabled:
                self.status.errors.append("Multimodal processing is disabled in configuration")
                
        except Exception as e:
            self.status.errors.append(f"Configuration check failed: {str(e)}")
    
    def _check_vision_model(self):
        """Check vision model availability and connectivity"""
        try:
            from config.multimodal_config import get_multimodal_config
            config = get_multimodal_config()
            
            self.status.vision_model_name = config.vision_model.model_name
            
            # Test connection to Ollama API
            base_url = config.vision_model.base_url
            try:
                response = requests.get(f"{base_url}/api/tags", timeout=5)
                if response.status_code == 200:
                    models = response.json().get('models', [])
                    model_names = [model.get('name', '') for model in models]
                    
                    if self.status.vision_model_name in model_names:
                        self.status.vision_model_available = True
                        self.status.vision_model_status = "available"
                    else:
                        self.status.vision_model_status = "model_not_found"
                        self.status.errors.append(f"Vision model '{self.status.vision_model_name}' not found in Ollama")
                else:
                    self.status.vision_model_status = "api_error"
                    self.status.errors.append(f"Ollama API returned status {response.status_code}")
                    
            except requests.exceptions.ConnectionError:
                self.status.vision_model_status = "connection_failed"
                self.status.errors.append(f"Cannot connect to Ollama at {base_url}")
            except requests.exceptions.Timeout:
                self.status.vision_model_status = "timeout"
                self.status.errors.append("Ollama API request timed out")
                
        except Exception as e:
            self.status.errors.append(f"Vision model check failed: {str(e)}")
    
    def _check_storage(self):
        """Check storage accessibility"""
        try:
            from config.multimodal_config import get_multimodal_config
            config = get_multimodal_config()
            
            storage_path = config.storage.blob_storage_path
            self.status.storage_path = storage_path
            
            # Check if path exists and is writable
            path_obj = Path(storage_path)
            if path_obj.exists():
                if os.access(storage_path, os.W_OK):
                    self.status.storage_accessible = True
                else:
                    self.status.errors.append(f"Storage path '{storage_path}' is not writable")
            else:
                # Try to create the directory
                try:
                    path_obj.mkdir(parents=True, exist_ok=True)
                    self.status.storage_accessible = True
                except Exception as e:
                    self.status.errors.append(f"Cannot create storage path '{storage_path}': {str(e)}")
                    
        except Exception as e:
            self.status.errors.append(f"Storage check failed: {str(e)}")
    
    def _check_dependencies(self):
        """Check if required dependencies are available"""
        try:
            required_modules = [
                'PIL',  # Pillow for image processing
                'camelot',  # Table extraction
                'tabula',  # Table extraction fallback
                'cv2',  # OpenCV for image processing
                'numpy',  # Numerical operations
            ]
            
            missing_modules = []
            for module in required_modules:
                try:
                    __import__(module)
                except ImportError:
                    missing_modules.append(module)
            
            if not missing_modules:
                self.status.dependencies_available = True
            else:
                self.status.errors.append(f"Missing dependencies: {', '.join(missing_modules)}")
                
        except Exception as e:
            self.status.errors.append(f"Dependency check failed: {str(e)}")
    
    def _check_processing_status(self):
        """Check if any multimodal processing is currently active"""
        try:
            # This would check for active processing threads/tasks
            # For now, we'll implement a simple check
            self.status.processing_active = False  # TODO: Implement actual processing status check
            
        except Exception as e:
            self.status.errors.append(f"Processing status check failed: {str(e)}")
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get current processing metrics"""
        return asdict(self.metrics)
    
    def record_processing_start(self, document_name: str):
        """Record the start of document processing"""
        # TODO: Implement processing tracking
        pass
    
    def record_processing_complete(self, document_name: str, processing_time: float,
                                 images_extracted: int, tables_extracted: int, success: bool,
                                 text_chunks: int = 0):
        """Record completion of document processing"""
        with self._lock:
            self.metrics.total_documents += 1
            if success:
                self.metrics.total_text_chunks += text_chunks
                self.metrics.total_images_extracted += images_extracted
                self.metrics.total_tables_extracted += tables_extracted
                self.metrics.total_processing_time += processing_time
                self.metrics.average_processing_time = (
                    self.metrics.total_processing_time / self.metrics.total_documents
                )
            else:
                self.metrics.errors_count += 1

            self.metrics.success_rate = (
                (self.metrics.total_documents - self.metrics.errors_count) /
                self.metrics.total_documents * 100
            ) if self.metrics.total_documents > 0 else 0
            
            self.metrics.last_processed = datetime.now().isoformat()
            self.save_metrics()
    
    def load_metrics(self):
        """Load metrics from file"""
        try:
            if os.path.exists(self.metrics_file):
                with open(self.metrics_file, 'r') as f:
                    data = json.load(f)
                    self.metrics = ProcessingMetrics(**data)
        except Exception as e:
            logger.error(f"Error loading metrics: {e}")
            self.metrics = ProcessingMetrics()
    
    def save_metrics(self):
        """Save metrics to file"""
        try:
            with open(self.metrics_file, 'w') as f:
                json.dump(asdict(self.metrics), f, indent=2)
        except Exception as e:
            logger.error(f"Error saving metrics: {e}")
    
    def reset_metrics(self):
        """Reset all metrics"""
        with self._lock:
            self.metrics = ProcessingMetrics()
            self.save_metrics()
    
    def get_health_summary(self) -> Dict[str, Any]:
        """Get a summary of multimodal pipeline health"""
        status = self.get_status()
        metrics = self.get_metrics()
        
        # Calculate health score
        health_score = 0
        max_score = 5
        
        if status['enabled']:
            health_score += 1
        if status['vision_model_available']:
            health_score += 1
        if status['storage_accessible']:
            health_score += 1
        if status['dependencies_available']:
            health_score += 1
        if len(status['errors']) == 0:
            health_score += 1
        
        health_percentage = (health_score / max_score) * 100
        
        if health_percentage >= 80:
            health_status = "healthy"
        elif health_percentage >= 60:
            health_status = "warning"
        else:
            health_status = "critical"
        
        return {
            'health_status': health_status,
            'health_percentage': health_percentage,
            'health_score': f"{health_score}/{max_score}",
            'status': status,
            'metrics': metrics,
            'recommendations': self._get_recommendations(status)
        }
    
    def _get_recommendations(self, status: Dict[str, Any]) -> List[str]:
        """Get recommendations based on current status"""
        recommendations = []
        
        if not status['enabled']:
            recommendations.append("Enable multimodal processing in Model Settings → Multimodal RAG")
        
        if not status['vision_model_available']:
            recommendations.append("Install and start the required vision model in Ollama")
        
        if not status['storage_accessible']:
            recommendations.append("Check storage path permissions and disk space")
        
        if not status['dependencies_available']:
            recommendations.append("Install missing Python dependencies")
        
        if status['errors']:
            recommendations.append("Review and resolve the errors listed in the status")
        
        if not recommendations:
            recommendations.append("Multimodal pipeline is healthy and ready for use")
        
        return recommendations

# Global instance
multimodal_status_service = MultimodalStatusService()

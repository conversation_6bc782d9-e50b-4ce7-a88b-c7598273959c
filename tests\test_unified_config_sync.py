"""
Test suite for unified configuration interface synchronization with RAG optimizations
Verifies that the web interface properly displays and saves RAG optimization parameters
"""

import pytest
import os
import sys
import json
from unittest.mock import Mock, patch, MagicMock

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from app.utils.config import get_query_config_data, get_embedding_config_data
from config.rag_optimizations import RAGOptimizationConfig, get_rag_config

class TestQueryConfigurationSync:
    """Test query configuration section synchronization"""
    
    def test_query_config_includes_rag_optimizations(self):
        """Test that query configuration data includes RAG optimization parameters"""
        config_data = get_query_config_data()
        
        # Verify RAG optimizations section exists
        assert 'rag_optimizations' in config_data
        rag_opts = config_data['rag_optimizations']
        
        # Verify query caching parameters
        assert 'query_caching' in rag_opts
        query_caching = rag_opts['query_caching']
        assert 'enabled' in query_caching
        assert 'ttl' in query_caching
        assert 'use_redis' in query_caching
        assert 'redis_url' in query_caching
        
        # Verify parallel processing parameters
        assert 'parallel_processing' in rag_opts
        parallel_proc = rag_opts['parallel_processing']
        assert 'enabled' in parallel_proc
        assert 'threshold' in parallel_proc
        assert 'max_workers' in parallel_proc
        
        # Verify adaptive retrieval parameters
        assert 'adaptive_retrieval' in rag_opts
        adaptive_ret = rag_opts['adaptive_retrieval']
        assert 'enabled' in adaptive_ret
        assert 'simple_k_ratio' in adaptive_ret
        assert 'complex_k_ratio' in adaptive_ret
        assert 'min_k' in adaptive_ret
        assert 'max_k' in adaptive_ret
        
        # Verify performance monitoring parameters
        assert 'performance_monitoring' in rag_opts
        perf_mon = rag_opts['performance_monitoring']
        assert 'enabled' in perf_mon
        assert 'log_cache' in perf_mon
        assert 'log_adaptive' in perf_mon
        assert 'log_parallel' in perf_mon
        
        # Verify cache invalidation parameters
        assert 'cache_invalidation' in rag_opts
        cache_inv = rag_opts['cache_invalidation']
        assert 'auto_invalidate' in cache_inv
        assert 'pattern' in cache_inv
    
    def test_query_config_values_match_rag_config(self):
        """Test that query config values match actual RAG configuration"""
        config_data = get_query_config_data()
        rag_config = get_rag_config()
        
        rag_opts = config_data['rag_optimizations']
        
        # Test query caching values
        assert rag_opts['query_caching']['enabled'] == rag_config.enable_query_caching
        assert rag_opts['query_caching']['ttl'] == rag_config.query_cache_ttl
        assert rag_opts['query_caching']['use_redis'] == rag_config.use_redis_cache
        assert rag_opts['query_caching']['redis_url'] == rag_config.redis_url
        
        # Test parallel processing values
        assert rag_opts['parallel_processing']['enabled'] == rag_config.enable_parallel_scoring
        assert rag_opts['parallel_processing']['threshold'] == rag_config.parallel_scoring_threshold
        assert rag_opts['parallel_processing']['max_workers'] == rag_config.max_parallel_workers
        
        # Test adaptive retrieval values
        assert rag_opts['adaptive_retrieval']['enabled'] == rag_config.enable_adaptive_retrieval
        assert rag_opts['adaptive_retrieval']['simple_k_ratio'] == rag_config.simple_query_k_ratio
        assert rag_opts['adaptive_retrieval']['complex_k_ratio'] == rag_config.complex_query_k_ratio
        assert rag_opts['adaptive_retrieval']['min_k'] == rag_config.min_adaptive_k
        assert rag_opts['adaptive_retrieval']['max_k'] == rag_config.max_adaptive_k

class TestEmbeddingConfigurationSync:
    """Test embedding configuration section synchronization"""
    
    def test_embedding_config_includes_rag_optimizations(self):
        """Test that embedding configuration data includes RAG optimization parameters"""
        config_data = get_embedding_config_data()
        
        # Verify RAG optimizations section exists
        assert 'rag_optimizations' in config_data
        rag_opts = config_data['rag_optimizations']
        
        # Verify embedding caching parameters
        assert 'embedding_caching' in rag_opts
        embedding_caching = rag_opts['embedding_caching']
        assert 'enabled' in embedding_caching
        assert 'ttl' in embedding_caching
        
        # Verify semantic chunking parameters
        assert 'semantic_chunking' in rag_opts
        semantic_chunking = rag_opts['semantic_chunking']
        assert 'enabled' in semantic_chunking
        assert 'threshold_length' in semantic_chunking
        assert 'buffer_size' in semantic_chunking
        assert 'breakpoint_threshold' in semantic_chunking
        assert 'fallback_to_sentence' in semantic_chunking
    
    def test_embedding_config_values_match_rag_config(self):
        """Test that embedding config values match actual RAG configuration"""
        config_data = get_embedding_config_data()
        rag_config = get_rag_config()
        
        rag_opts = config_data['rag_optimizations']
        
        # Test embedding caching values
        assert rag_opts['embedding_caching']['enabled'] == rag_config.enable_embedding_caching
        assert rag_opts['embedding_caching']['ttl'] == rag_config.embedding_cache_ttl
        
        # Test semantic chunking values
        assert rag_opts['semantic_chunking']['enabled'] == rag_config.prefer_semantic_chunking
        assert rag_opts['semantic_chunking']['threshold_length'] == rag_config.semantic_threshold_length
        assert rag_opts['semantic_chunking']['buffer_size'] == rag_config.semantic_buffer_size
        assert rag_opts['semantic_chunking']['breakpoint_threshold'] == rag_config.semantic_breakpoint_threshold
        assert rag_opts['semantic_chunking']['fallback_to_sentence'] == rag_config.fallback_to_sentence_chunking

class TestConfigurationSynchronization:
    """Test configuration synchronization between interface and runtime"""
    
    def test_environment_variable_override_detection(self):
        """Test that environment variable overrides are properly detected"""
        # Set test environment variables
        test_env_vars = {
            'RAG_ENABLE_QUERY_CACHING': 'false',
            'RAG_QUERY_CACHE_TTL': '3600',
            'RAG_MAX_PARALLEL_WORKERS': '8'
        }
        
        # Apply test environment variables
        original_values = {}
        for key, value in test_env_vars.items():
            original_values[key] = os.environ.get(key)
            os.environ[key] = value
        
        try:
            # Reload configuration to pick up environment changes
            from config.rag_optimizations import reload_rag_config
            rag_config = reload_rag_config()
            
            # Verify environment variables are reflected
            assert rag_config.enable_query_caching == False
            assert rag_config.query_cache_ttl == 3600
            assert rag_config.max_parallel_workers == 8
            
            # Verify configuration data reflects environment variables
            config_data = get_query_config_data()
            rag_opts = config_data['rag_optimizations']
            
            assert rag_opts['query_caching']['enabled'] == False
            assert rag_opts['query_caching']['ttl'] == 3600
            assert rag_opts['parallel_processing']['max_workers'] == 8
            
        finally:
            # Restore original environment variables
            for key, original_value in original_values.items():
                if original_value is None:
                    os.environ.pop(key, None)
                else:
                    os.environ[key] = original_value
    
    @patch('app.routes.api.save_default_models')
    @patch('config.rag_optimizations.reload_rag_config')
    def test_configuration_save_updates_environment(self, mock_reload, mock_save):
        """Test that saving configuration updates environment variables"""
        from app.routes.api import handle_query_config
        from flask import Flask
        
        # Mock Flask app and request
        app = Flask(__name__)
        with app.test_request_context(
            '/api/settings/query_config',
            method='POST',
            json={
                'enable_query_caching': True,
                'query_cache_ttl': 2400,
                'use_redis_cache': False,
                'enable_parallel_scoring': True,
                'max_parallel_workers': 6,
                'enable_adaptive_retrieval': True,
                'simple_k_ratio': 0.5,
                'complex_k_ratio': 1.5
            }
        ):
            mock_save.return_value = True
            
            # Call the handler
            response = handle_query_config()
            
            # Verify environment variables were set
            assert os.environ.get('RAG_ENABLE_QUERY_CACHING') == 'true'
            assert os.environ.get('RAG_QUERY_CACHE_TTL') == '2400'
            assert os.environ.get('RAG_USE_REDIS_CACHE') == 'false'
            assert os.environ.get('RAG_MAX_PARALLEL_WORKERS') == '6'
            assert os.environ.get('RAG_SIMPLE_QUERY_K_RATIO') == '0.5'
            assert os.environ.get('RAG_COMPLEX_QUERY_K_RATIO') == '1.5'
            
            # Verify reload was called
            mock_reload.assert_called_once()

class TestMissingConfigurationDetection:
    """Test detection of missing configuration parameters"""
    
    def test_all_rag_optimization_parameters_exposed(self):
        """Test that all RAG optimization parameters are exposed in the interface"""
        # Get RAG configuration object
        rag_config = get_rag_config()
        rag_config_dict = rag_config.to_dict()
        
        # Get query configuration data
        query_config_data = get_query_config_data()
        query_rag_opts = query_config_data.get('rag_optimizations', {})
        
        # Get embedding configuration data
        embedding_config_data = get_embedding_config_data()
        embedding_rag_opts = embedding_config_data.get('rag_optimizations', {})
        
        # Check that all query-related RAG parameters are exposed
        expected_query_sections = ['query_caching', 'parallel_processing', 'adaptive_retrieval', 'performance_monitoring', 'cache_invalidation']
        for section in expected_query_sections:
            assert section in query_rag_opts, f"Missing query RAG optimization section: {section}"
        
        # Check that all embedding-related RAG parameters are exposed
        expected_embedding_sections = ['embedding_caching', 'semantic_chunking']
        for section in expected_embedding_sections:
            assert section in embedding_rag_opts, f"Missing embedding RAG optimization section: {section}"
    
    def test_no_deprecated_settings_present(self):
        """Test that no deprecated settings are present in the interface"""
        # Get configuration data
        query_config_data = get_query_config_data()
        embedding_config_data = get_embedding_config_data()
        
        # List of deprecated settings that should not be present
        deprecated_settings = [
            'old_caching_enabled',
            'legacy_retrieval_mode',
            'deprecated_chunk_strategy'
        ]
        
        # Check query configuration
        for setting in deprecated_settings:
            assert setting not in query_config_data, f"Deprecated setting found in query config: {setting}"
            if 'rag_optimizations' in query_config_data:
                for section in query_config_data['rag_optimizations'].values():
                    if isinstance(section, dict):
                        assert setting not in section, f"Deprecated setting found in query RAG config: {setting}"
        
        # Check embedding configuration
        for setting in deprecated_settings:
            assert setting not in embedding_config_data, f"Deprecated setting found in embedding config: {setting}"
            if 'rag_optimizations' in embedding_config_data:
                for section in embedding_config_data['rag_optimizations'].values():
                    if isinstance(section, dict):
                        assert setting not in section, f"Deprecated setting found in embedding RAG config: {setting}"

if __name__ == "__main__":
    # Run synchronization tests
    pytest.main([__file__, "-v"])

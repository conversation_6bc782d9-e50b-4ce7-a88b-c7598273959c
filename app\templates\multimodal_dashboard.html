<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>Multimodal RAG Pipeline Dashboard</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body class="bg-gray-100 dark:bg-gray-900 min-h-screen">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white mb-2">
                <i class="fas fa-chart-line mr-3"></i>Multimodal RAG Pipeline Dashboard
            </h1>
            <p class="text-gray-600 dark:text-gray-400">
                Monitor and verify the status of your multimodal document processing pipeline
            </p>
        </div>

        <!-- Status Overview Cards -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <!-- Overall Health -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Overall Health</p>
                        <p id="health-status" class="text-2xl font-bold text-gray-900 dark:text-white">Loading...</p>
                    </div>
                    <div id="health-icon" class="text-3xl">
                        <i class="fas fa-spinner fa-spin text-gray-400"></i>
                    </div>
                </div>
                <div class="mt-4">
                    <div class="w-full bg-gray-200 rounded-full h-2">
                        <div id="health-bar" class="bg-blue-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                    </div>
                </div>
            </div>

            <!-- Processing Status -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Processing Status</p>
                        <p id="processing-status" class="text-2xl font-bold text-gray-900 dark:text-white">Idle</p>
                    </div>
                    <div class="text-3xl">
                        <i id="processing-icon" class="fas fa-pause text-gray-400"></i>
                    </div>
                </div>
            </div>

            <!-- Vision Model -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Vision Model</p>
                        <p id="vision-model-name" class="text-lg font-bold text-gray-900 dark:text-white">Loading...</p>
                    </div>
                    <div class="text-3xl">
                        <i id="vision-model-icon" class="fas fa-eye text-gray-400"></i>
                    </div>
                </div>
                <p id="vision-model-status" class="text-sm text-gray-500 dark:text-gray-400 mt-2">Checking...</p>
            </div>

            <!-- Storage Status -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                <div class="flex items-center justify-between">
                    <div>
                        <p class="text-sm font-medium text-gray-600 dark:text-gray-400">Storage</p>
                        <p id="storage-status" class="text-2xl font-bold text-gray-900 dark:text-white">Checking...</p>
                    </div>
                    <div class="text-3xl">
                        <i id="storage-icon" class="fas fa-database text-gray-400"></i>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Content Grid -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Status Details -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                <h2 class="text-xl font-bold text-gray-900 dark:text-white mb-4">
                    <i class="fas fa-info-circle mr-2"></i>System Status
                </h2>
                
                <!-- Status Items -->
                <div id="status-items" class="space-y-3">
                    <!-- Status items will be populated by JavaScript -->
                </div>

                <!-- Refresh Button -->
                <div class="mt-6">
                    <button id="refresh-status" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors">
                        <i class="fas fa-sync-alt mr-2"></i>Refresh Status
                    </button>
                </div>
            </div>

            <!-- Processing Metrics -->
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                <h2 class="text-xl font-bold text-gray-900 dark:text-white mb-4">
                    <i class="fas fa-chart-bar mr-2"></i>Processing Metrics
                </h2>
                
                <div class="grid grid-cols-3 gap-4 mb-6">
                    <div class="text-center">
                        <p class="text-2xl font-bold text-blue-600" id="total-documents">0</p>
                        <p class="text-sm text-gray-600 dark:text-gray-400">Documents Processed</p>
                    </div>
                    <div class="text-center">
                        <p class="text-2xl font-bold text-green-600" id="success-rate">0%</p>
                        <p class="text-sm text-gray-600 dark:text-gray-400">Success Rate</p>
                    </div>
                    <div class="text-center">
                        <p class="text-2xl font-bold text-indigo-600" id="total-text-chunks">0</p>
                        <p class="text-sm text-gray-600 dark:text-gray-400">Text Chunks</p>
                    </div>
                    <div class="text-center">
                        <p class="text-2xl font-bold text-purple-600" id="total-images">0</p>
                        <p class="text-sm text-gray-600 dark:text-gray-400">Images Extracted</p>
                    </div>
                    <div class="text-center">
                        <p class="text-2xl font-bold text-orange-600" id="total-tables">0</p>
                        <p class="text-sm text-gray-600 dark:text-gray-400">Tables Extracted</p>
                    </div>
                    <div class="text-center">
                        <p class="text-2xl font-bold text-gray-600" id="avg-processing-time">0s</p>
                        <p class="text-sm text-gray-600 dark:text-gray-400">Avg Processing Time</p>
                    </div>
                </div>

                <div class="mb-4">
                    <p class="text-sm text-gray-600 dark:text-gray-400 mb-2">Average Processing Time</p>
                    <p class="text-lg font-bold text-gray-900 dark:text-white" id="avg-processing-time">0.0s</p>
                </div>

                <button id="reset-metrics" class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg transition-colors">
                    <i class="fas fa-trash mr-2"></i>Reset Metrics
                </button>
            </div>
        </div>

        <!-- Pipeline Testing Section -->
        <div class="mt-8 bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <h2 class="text-xl font-bold text-gray-900 dark:text-white mb-4">
                <i class="fas fa-flask mr-2"></i>Pipeline Testing
            </h2>
            
            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Test Upload -->
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">Test Document Upload</h3>
                    <p class="text-gray-600 dark:text-gray-400 mb-4">
                        Upload a document to test the multimodal processing pipeline
                    </p>
                    
                    <div class="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-6 text-center">
                        <input type="file" id="test-file" accept=".pdf,.docx,.doc" class="hidden">
                        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
                        <label for="test-file" class="cursor-pointer">
                            <i class="fas fa-cloud-upload-alt text-4xl text-gray-400 mb-4"></i>
                            <p class="text-gray-600 dark:text-gray-400">Click to select a test document</p>
                            <p class="text-sm text-gray-500 dark:text-gray-500 mt-2">PDF, DOCX, or DOC files</p>
                        </label>
                    </div>
                    
                    <button id="run-test" class="mt-4 bg-green-600 hover:bg-green-700 text-white px-6 py-2 rounded-lg transition-colors disabled:opacity-50" disabled>
                        <i class="fas fa-play mr-2"></i>Run Test
                    </button>
                </div>

                <!-- Test Results -->
                <div>
                    <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">Test Results</h3>
                    <div id="test-results" class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 min-h-[200px]">
                        <p class="text-gray-500 dark:text-gray-400 text-center">
                            No test results yet. Upload and test a document to see results.
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Recommendations Section -->
        <div class="mt-8 bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
            <h2 class="text-xl font-bold text-gray-900 dark:text-white mb-4">
                <i class="fas fa-lightbulb mr-2"></i>Recommendations
            </h2>
            <div id="recommendations" class="space-y-2">
                <!-- Recommendations will be populated by JavaScript -->
            </div>
        </div>
    </div>

    <!-- Loading Overlay -->
    <div id="loading-overlay" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white dark:bg-gray-800 rounded-lg p-6 text-center">
            <i class="fas fa-spinner fa-spin text-4xl text-blue-600 mb-4"></i>
            <p class="text-gray-900 dark:text-white">Processing test document...</p>
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/utilities.js') }}"></script>
    <script src="{{ url_for('static', filename='multimodal_dashboard.js') }}"></script>
</body>
</html>

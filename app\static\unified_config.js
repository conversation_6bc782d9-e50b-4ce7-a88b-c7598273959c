document.addEventListener('DOMContentLoaded', function() {
    // Check URL hash for active tab
    checkUrlHash();

    // Setup main tab switching
    setupMainTabs();

    // Setup configuration summary toggle
    setupSummaryToggle();

    // Load query and embedding configuration content
    loadQueryConfig();
    loadEmbeddingConfig();

    // Setup interdependent settings validation
    setupDependencyValidation();

    // Setup form submission
    setupFormSubmission();

    // Setup individual tab save buttons
    setupTabSaveButtons();

    // Setup validation button
    setupValidationButton();

    // Handle hash changes in URL
    window.addEventListener('hashchange', checkUrlHash);

    // Utility function to collect form data as an object (handles arrays and model parameter modes)
    function collectFormData(formElement) {
        const formData = new FormData(formElement);
        const data = {};
        const model_parameters = { strict: {}, balanced: {}, off: {} };
        formData.forEach((value, key) => {
            // Check for mode-specific model parameter fields
            const modeMatch = key.match(/^(strict|balanced|off)_(.+)$/);
            if (modeMatch) {
                const mode = modeMatch[1];
                const param = modeMatch[2];
                model_parameters[mode][param] = value;
            } else {
                if (data[key]) {
                    if (!Array.isArray(data[key])) data[key] = [data[key]];
                    data[key].push(value);
                } else {
                    data[key] = value;
                }
            }
        });
        data.model_parameters = model_parameters;
        return data;
    }

    // Save Models Settings button
    const saveModelsBtn = document.getElementById('saveModelsBtn');
    if (saveModelsBtn) {
        saveModelsBtn.addEventListener('click', async function() {
            saveModelsBtn.disabled = true;
            const originalText = saveModelsBtn.textContent;
            saveModelsBtn.textContent = 'Saving...';
            try {
                // Collect model settings
                let formElem = document.getElementById('unifiedConfigForm') || document.querySelector('form');
                if (!formElem) {
                    console.warn('Form element not found.');
                    return;
                }
                const data = collectFormData(formElem);
                const response = await fetch('/api/settings/query_config', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(data)
                });
                if (!response.ok) throw new Error('Failed to save model settings');
                const result = await response.json();
                if (result.error) throw new Error(result.error);
                await updateSummaryPanel();
                if (typeof showToast === 'function') showToast('Model settings updated!', 'success');
                window.dispatchEvent(new CustomEvent('config-save-success', { detail: { message: 'Model settings updated!' } }));
            } catch (err) {
                if (typeof showToast === 'function') showToast('Save failed: ' + err.message, 'danger');
                window.dispatchEvent(new CustomEvent('config-save-error', { detail: { message: 'Save failed: ' + err.message } }));
            } finally {
                saveModelsBtn.disabled = false;
                saveModelsBtn.textContent = originalText;
            }
        });
    } else {
        console.warn('saveModelsBtn not found.');
    }

    // Save Query Settings button
    const saveQueryConfigBtn = document.getElementById('saveQueryConfigBtn');
    if (saveQueryConfigBtn) {
        saveQueryConfigBtn.addEventListener('click', async function() {
            saveQueryConfigBtn.disabled = true;
            const originalText = saveQueryConfigBtn.textContent;
            saveQueryConfigBtn.textContent = 'Saving...';
            try {
                let formElem = document.getElementById('unifiedConfigForm') || document.querySelector('form');
                if (!formElem) {
                    console.warn('Form element not found.');
                    return;
                }
                const data = collectFormData(formElem);
                const response = await fetch('/api/settings/query_config', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(data)
                });
                if (!response.ok) throw new Error('Failed to save query settings');
                const result = await response.json();
                if (result.error) throw new Error(result.error);
                await updateSummaryPanel();
                if (typeof showToast === 'function') showToast('Query settings updated!', 'success');
                window.dispatchEvent(new CustomEvent('config-save-success', { detail: { message: 'Query settings updated!' } }));
            } catch (err) {
                if (typeof showToast === 'function') showToast('Save failed: ' + err.message, 'danger');
                window.dispatchEvent(new CustomEvent('config-save-error', { detail: { message: 'Save failed: ' + err.message } }));
            } finally {
                saveQueryConfigBtn.disabled = false;
                saveQueryConfigBtn.textContent = originalText;
            }
        });
    } else {
        console.warn('saveQueryConfigBtn not found.');
    }

    // Save Embedding Settings button
    const saveEmbeddingBtn = document.getElementById('saveEmbeddingBtn');
    if (saveEmbeddingBtn) {
        saveEmbeddingBtn.addEventListener('click', async function() {
            saveEmbeddingBtn.disabled = true;
            const originalText = saveEmbeddingBtn.textContent;
            saveEmbeddingBtn.textContent = 'Saving...';
            try {
                let formElem = document.getElementById('unifiedConfigForm') || document.querySelector('form');
                if (!formElem) {
                    console.warn('Form element not found.');
                    return;
                }
                const data = collectFormData(formElem);
                const response = await fetch('/api/settings/query_config', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(data)
                });
                if (!response.ok) throw new Error('Failed to save embedding settings');
                const result = await response.json();
                if (result.error) throw new Error(result.error);
                await updateSummaryPanel();
                if (typeof showToast === 'function') showToast('Embedding settings updated!', 'success');
                window.dispatchEvent(new CustomEvent('config-save-success', { detail: { message: 'Embedding settings updated!' } }));
            } catch (err) {
                if (typeof showToast === 'function') showToast('Save failed: ' + err.message, 'danger');
                window.dispatchEvent(new CustomEvent('config-save-error', { detail: { message: 'Save failed: ' + err.message } }));
            } finally {
                saveEmbeddingBtn.disabled = false;
                saveEmbeddingBtn.textContent = originalText;
            }
        });
    } else {
        console.warn('saveEmbeddingBtn not found.');
    }

    // Main Save All Settings button
    const saveAllBtn = document.querySelector('button[type="submit"]');
    if (saveAllBtn) {
        saveAllBtn.addEventListener('click', async function(e) {
            e.preventDefault();
            saveAllBtn.disabled = true;
            const originalText = saveAllBtn.textContent;
            saveAllBtn.textContent = 'Saving...';
            try {
                let formElem = document.getElementById('unifiedConfigForm') || document.querySelector('form');
                if (!formElem) {
                    console.warn('Form element not found.');
                    return;
                }
                const data = collectFormData(formElem);
                const response = await fetch('/api/settings/query_config', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(data)
                });
                if (!response.ok) throw new Error('Failed to save all settings');
                const result = await response.json();
                if (result.error) throw new Error(result.error);
                await updateSummaryPanel();
                if (typeof showToast === 'function') showToast('All settings updated!', 'success');
                window.dispatchEvent(new CustomEvent('config-save-success', { detail: { message: 'All settings updated!' } }));
            } catch (err) {
                if (typeof showToast === 'function') showToast('Save failed: ' + err.message, 'danger');
                window.dispatchEvent(new CustomEvent('config-save-error', { detail: { message: 'Save failed: ' + err.message } }));
            } finally {
                saveAllBtn.disabled = false;
                saveAllBtn.textContent = originalText;
            }
        });
    } else {
        console.warn('Save All button not found.');
    }
});

// Check URL hash and activate corresponding tab
function checkUrlHash() {
    const hash = window.location.hash.substring(1);
    if (hash) {
        const tabMap = {
            'models': 'models-tab',
            'query': 'query-tab',
            'embedding': 'embedding-tab'
        };

        const tabId = tabMap[hash];
        if (tabId) {
            const tab = document.getElementById(tabId);
            if (tab) {
                // Simulate a click on the tab
                setTimeout(() => tab.click(), 100);
            }
        }
    } else {
        // Try to restore from localStorage if no hash
        const savedTab = localStorage.getItem('unifiedConfigActiveTab');
        if (savedTab) {
            const tab = document.getElementById(savedTab);
            if (tab) {
                // Simulate a click on the tab
                setTimeout(() => tab.click(), 100);
            }
        }
    }
}

function setupMainTabs() {
    const tabs = document.querySelectorAll('#mainTabs button');

    tabs.forEach(tab => {
        tab.addEventListener('click', function() {
            const targetId = this.getAttribute('data-target');
            const targetContent = document.getElementById(targetId);

            // Don't do anything if this tab is already active
            if (this.classList.contains('tab-active')) {
                return;
            }

            // First, prepare the new content but keep it hidden
            // This allows us to have a smooth transition
            document.querySelectorAll('.tab-content').forEach(content => {
                if (content !== targetContent) {
                    // Add hidden class but don't immediately hide
                    // This triggers the CSS transition
                    content.classList.add('hidden');
                }
            });

            // Update tab styling and accessibility attributes
            tabs.forEach(t => {
                // Update classes
                t.classList.remove('tab-active');
                t.classList.add('tab-inactive');

                // Update ARIA attributes
                t.setAttribute('aria-selected', 'false');

                // Hide checkmark indicator
                const indicator = t.querySelector('.tab-indicator');
                if (indicator) {
                    indicator.classList.add('hidden');
                }
            });

            // Update active tab
            this.classList.remove('tab-inactive');
            this.classList.add('tab-active');
            this.setAttribute('aria-selected', 'true');

            // Show checkmark indicator
            const activeIndicator = this.querySelector('.tab-indicator');
            if (activeIndicator) {
                activeIndicator.classList.remove('hidden');
            }

            // Show the target content with animation
            targetContent.classList.remove('hidden');

            // Save active tab state
            const tabId = this.getAttribute('id');
            localStorage.setItem('unifiedConfigActiveTab', tabId);

            // Update URL hash without scrolling
            const hashMap = {
                'models-tab': 'models',
                'query-tab': 'query',
                'embedding-tab': 'embedding'
            };

            const hash = hashMap[tabId];
            if (hash) {
                // Update URL without scrolling
                history.replaceState(null, null, `#${hash}`);
            }
        });
    });
}

function setupSummaryToggle() {
    const toggleButton = document.getElementById('toggleSummary');
    const summaryContent = document.getElementById('summaryContent');

    toggleButton.addEventListener('click', function() {
        if (summaryContent.classList.contains('hidden')) {
            summaryContent.classList.remove('hidden');
            toggleButton.textContent = 'Hide Details';
            updateSummary();
        } else {
            summaryContent.classList.add('hidden');
            toggleButton.textContent = 'Show Details';
        }
    });
}

function updateSummary() {
    // Update AI Models summary
    const selectedLLM = document.querySelector('input[name="llm_model"]:checked');
    const selectedEmbedding = document.querySelector('input[name="embedding_model"]:checked');
    const selectedVision = document.querySelector('input[name="vision_model"]:checked');

    if (selectedLLM) document.getElementById('summary-llm').textContent = selectedLLM.value;
    if (selectedEmbedding) document.getElementById('summary-embedding').textContent = selectedEmbedding.value;
    if (selectedVision) document.getElementById('summary-vision').textContent = selectedVision.value;

    // Update Query Settings summary
    const antiHallucination = document.querySelector('input[name="default_mode"]:checked');
    if (antiHallucination) document.getElementById('summary-hallucination').textContent = antiHallucination.value;

    // Count configured templates
    const templateCount = document.querySelectorAll('textarea[name^="template_"]').length;
    document.getElementById('summary-templates').textContent = `${templateCount} configured`;

    // Count insufficient info phrases
    const phraseCount = document.querySelectorAll('input[name="insufficient_phrase[]"]').length;
    document.getElementById('summary-phrases').textContent = `${phraseCount} configured`;

    // Update Embedding Settings summary
    const chunkSize = document.querySelector('input[name="chunk_size"]');
    if (chunkSize) document.getElementById('summary-chunk-size').textContent = chunkSize.value;

    const visionEnabled = document.querySelector('input[name="use_vision_model"]');
    document.getElementById('summary-vision-enabled').textContent = visionEnabled && visionEnabled.checked ? 'Enabled' : 'Disabled';

    const batchSize = document.querySelector('input[name="batch_size"]');
    if (batchSize) document.getElementById('summary-batch-size').textContent = batchSize.value;
}

async function loadQueryConfig() {
    try {
        const response = await fetch('/admin/query_config_partial');
        if (response.ok) {
            const html = await response.text();
            const queryConfigContainer = document.getElementById('query-config-container');
            if (queryConfigContainer) {
                queryConfigContainer.innerHTML = html;

                // Setup query config specific functionality
                setupQueryConfigFunctionality();

                // Apply text contrast fixes
                applyTextContrastFixes('query-config-container');
            } else {
                console.warn("Element 'query-config-container' not found.");
            }
        } else {
            showError('Failed to load query configuration content');
        }
    } catch (error) {
        showError(`Error loading query configuration: ${error.message}`);
    }
}

async function loadEmbeddingConfig() {
    try {
        const response = await fetch('/admin/embedding_config_partial');
        if (response.ok) {
            const html = await response.text();
            const embeddingConfigContainer = document.getElementById('embedding-config-container');
            if (embeddingConfigContainer) {
                embeddingConfigContainer.innerHTML = html;

                // Setup embedding config specific functionality
                setupEmbeddingConfigFunctionality();

                // Apply text contrast fixes
                applyTextContrastFixes('embedding-config-container');
            } else {
                console.warn("Element 'embedding-config-container' not found.");
            }
        } else {
            showError('Failed to load embedding configuration content');
        }
    } catch (error) {
        showError(`Error loading embedding configuration: ${error.message}`);
    }
}

function setupQueryConfigFunctionality() {
    // Setup enhanced threshold sliders with a small delay to ensure DOM is ready
    setTimeout(() => {
        setupEnhancedThresholdSliders();
        setupEnhancedRelevanceSlider();
    }, 100);

    // Tab switching for prompt templates
    const templateTabs = [
        { id: 'templateTabStrict', content: 'templateContentStrict' },
        { id: 'templateTabBalanced', content: 'templateContentBalanced' },
        { id: 'templateTabOff', content: 'templateContentOff' },
        { id: 'templateTabGeneral', content: 'templateContentGeneral' },
        { id: 'templateTabDocSpecific', content: 'templateContentDocSpecific' }
    ];

    templateTabs.forEach(tab => {
        const tabElement = document.getElementById(tab.id);
        if (tabElement) {
            tabElement.addEventListener('click', function() {
                // Hide all content
                document.querySelectorAll('.template-content').forEach(el => {
                    el.classList.add('hidden');
                });

                // Show selected content
                document.getElementById(tab.content).classList.remove('hidden');

                // Update tab styling
                templateTabs.forEach(t => {
                    const element = document.getElementById(t.id);
                    if (element) {
                        element.classList.remove('bg-blue-600', 'text-white');
                        element.classList.add('bg-gray-300', 'text-gray-700');
                    }
                });

                this.classList.remove('bg-gray-300', 'text-gray-700');
                this.classList.add('bg-blue-600', 'text-white');
            });
        }
    });

    // Tab switching for followup templates
    const followupTabs = [
        { id: 'followupTabDefault', content: 'followupContentDefault' },
        { id: 'followupTabInsufficient', content: 'followupContentInsufficient' }
    ];

    followupTabs.forEach(tab => {
        const tabElement = document.getElementById(tab.id);
        if (tabElement) {
            tabElement.addEventListener('click', function() {
                // Hide all content
                document.querySelectorAll('.followup-content').forEach(el => {
                    el.classList.add('hidden');
                });

                // Show selected content
                document.getElementById(tab.content).classList.remove('hidden');

                // Update tab styling
                followupTabs.forEach(t => {
                    const element = document.getElementById(t.id);
                    if (element) {
                        element.classList.remove('bg-blue-600', 'text-white');
                        element.classList.add('bg-gray-300', 'text-gray-700');
                    }
                });

                this.classList.remove('bg-gray-300', 'text-gray-700');
                this.classList.add('bg-blue-600', 'text-white');
            });
        }
    });

    // Add/remove phrases for insufficient info
    const addPhraseButton = document.getElementById('addPhrase');
    if (addPhraseButton) {
        addPhraseButton.addEventListener('click', function() {
            const container = document.getElementById('insufficientPhrases');
            const newPhrase = document.createElement('div');
            newPhrase.className = 'flex items-center';
            newPhrase.innerHTML = `
                <input type="text" name="insufficient_phrase[]"
                    class="flex-grow px-3 py-2 text-gray-700 border rounded-lg focus:outline-none focus:shadow-outline"
                    placeholder="Enter a phrase...">
                <button type="button" class="remove-phrase ml-2 px-2 py-1 bg-red-500 text-white rounded hover:bg-red-600">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd" />
                    </svg>
                </button>
            `;
            container.appendChild(newPhrase);

            // Add event listener to the new remove button
            newPhrase.querySelector('.remove-phrase').addEventListener('click', function() {
                container.removeChild(newPhrase);
            });
        });
    }

    // Remove phrase (for existing buttons)
    document.querySelectorAll('.remove-phrase').forEach(button => {
        button.addEventListener('click', function() {
            this.parentElement.remove();
        });
    });
}

function setupEmbeddingConfigFunctionality() {
    // Toggle vision model options based on checkbox state
    const useVisionModelCheckbox = document.getElementById('use_vision_model');
    const visionModelOptions = document.getElementById('visionModelOptions');

    if (useVisionModelCheckbox && visionModelOptions) {
        // Initial state
        updateVisionOptions();

        // Toggle on change
        useVisionModelCheckbox.addEventListener('change', updateVisionOptions);

        function updateVisionOptions() {
            if (useVisionModelCheckbox.checked) {
                visionModelOptions.classList.remove('opacity-50');
                document.querySelectorAll('#visionModelOptions select, #visionModelOptions input').forEach(el => {
                    el.disabled = false;
                });
            } else {
                visionModelOptions.classList.add('opacity-50');
                document.querySelectorAll('#visionModelOptions select, #visionModelOptions input').forEach(el => {
                    el.disabled = true;
                });
            }
        }
    }

    // Toggle location extraction options based on checkbox state
    const extractLocationsCheckbox = document.getElementById('extract_locations');
    const locationExtractionOptions = document.getElementById('locationExtractionOptions');

    if (extractLocationsCheckbox && locationExtractionOptions) {
        // Initial state
        updateLocationOptions();

        // Toggle on change
        extractLocationsCheckbox.addEventListener('change', updateLocationOptions);

        function updateLocationOptions() {
            if (extractLocationsCheckbox.checked) {
                locationExtractionOptions.classList.remove('opacity-50');
                document.querySelectorAll('#locationExtractionOptions select, #locationExtractionOptions input').forEach(el => {
                    el.disabled = false;
                });
            } else {
                locationExtractionOptions.classList.add('opacity-50');
                document.querySelectorAll('#locationExtractionOptions select, #locationExtractionOptions input').forEach(el => {
                    el.disabled = true;
                });
            }
        }
    }
}

function setupDependencyValidation() {
    // Vision model dependencies
    const visionModelRadios = document.querySelectorAll('input[name="vision_model"]');
    const useVisionCheckbox = document.querySelector('input[name="use_vision"]');
    const useVisionDuringEmbeddingCheckbox = document.getElementById('use_vision_during_embedding');

    // Update dependencies when vision model changes
    visionModelRadios.forEach(radio => {
        radio.addEventListener('change', function() {
            validateVisionDependencies();
        });
    });

    // Update dependencies when vision checkboxes change
    if (useVisionCheckbox) {
        useVisionCheckbox.addEventListener('change', function() {
            validateVisionDependencies();
        });
    }

    if (useVisionDuringEmbeddingCheckbox) {
        useVisionDuringEmbeddingCheckbox.addEventListener('change', function() {
            validateVisionDependencies();
        });
    }

    // Initial validation
    validateVisionDependencies();

    function validateVisionDependencies() {
        const selectedVisionModel = document.querySelector('input[name="vision_model"]:checked');

        // If no vision model is selected, disable vision features
        if (!selectedVisionModel) {
            if (useVisionCheckbox) {
                useVisionCheckbox.checked = false;
                useVisionCheckbox.disabled = true;
            }

            if (useVisionDuringEmbeddingCheckbox) {
                useVisionDuringEmbeddingCheckbox.checked = false;
                useVisionDuringEmbeddingCheckbox.disabled = true;
            }
        } else {
            if (useVisionCheckbox) {
                useVisionCheckbox.disabled = false;
            }

            if (useVisionDuringEmbeddingCheckbox) {
                // Only enable if use_vision is checked
                useVisionDuringEmbeddingCheckbox.disabled = !useVisionCheckbox.checked;
            }
        }
    }
}

function setupValidationButton() {
    const validateButton = document.getElementById('validateConfigBtn');

    if (validateButton) {
        validateButton.addEventListener('click', function() {
            // Perform validation checks
            const validationResults = validateConfiguration();

            if (validationResults.valid) {
                Toastify({
                    text: "Configuration is valid!",
                    duration: 3000,
                    backgroundColor: "#00C851"
                }).showToast();
            } else {
                // Show validation errors
                const statusMessage = document.getElementById('statusMessage');
                statusMessage.innerHTML = `
                    <div class="p-4 bg-yellow-100 text-yellow-700 rounded-md">
                        <h3 class="font-medium mb-2">Configuration Issues:</h3>
                        <ul class="list-disc pl-5">
                            ${validationResults.errors.map(error => `<li>${error}</li>`).join('')}
                        </ul>
                    </div>
                `;
                statusMessage.classList.remove('hidden');

                Toastify({
                    text: "Configuration has issues. See details above.",
                    duration: 3000,
                    backgroundColor: "#ff9800"
                }).showToast();
            }
        });
    }
}

function validateConfiguration() {
    const errors = [];

    // Check if LLM model is selected
    const selectedLLM = document.querySelector('input[name="llm_model"]:checked');
    if (!selectedLLM) {
        errors.push("No LLM model selected. Please select a language model.");
    }

    // Check if embedding model is selected
    const selectedEmbedding = document.querySelector('input[name="embedding_model"]:checked');
    if (!selectedEmbedding) {
        errors.push("No embedding model selected. Please select an embedding model.");
    }

    // Check vision model dependencies
    const useVision = document.querySelector('input[name="use_vision"]');
    const useVisionDuringEmbedding = document.getElementById('use_vision_during_embedding');
    const selectedVision = document.querySelector('input[name="vision_model"]:checked');

    if ((useVision && useVision.checked) || (useVisionDuringEmbedding && useVisionDuringEmbedding.checked)) {
        if (!selectedVision) {
            errors.push("Vision features are enabled but no vision model is selected.");
        }
    }

    // Check embedding parameters
    const chunkSize = document.querySelector('input[name="chunk_size"]');
    const chunkOverlap = document.querySelector('input[name="chunk_overlap"]');

    if (chunkSize && chunkOverlap) {
        const sizeValue = parseInt(chunkSize.value);
        const overlapValue = parseInt(chunkOverlap.value);

        if (overlapValue >= sizeValue) {
            errors.push("Chunk overlap must be smaller than chunk size.");
        }
    }

    return {
        valid: errors.length === 0,
        errors: errors
    };
}

function setupFormSubmission() {
    document.getElementById('unifiedConfigForm').addEventListener('submit', async function(e) {
        e.preventDefault();

        // Show loading state
        const statusMessage = document.getElementById('statusMessage');
        statusMessage.innerHTML = `
            <div class="p-4 bg-blue-100 text-blue-700 rounded-md flex items-center">
                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-blue-700" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Saving configuration...
            </div>
        `;
        statusMessage.classList.remove('hidden');

        try {
            // Validate configuration before saving
            const validationResults = validateConfiguration();
            if (!validationResults.valid) {
                statusMessage.innerHTML = `
                    <div class="p-4 bg-yellow-100 text-yellow-700 rounded-md">
                        <h3 class="font-medium mb-2">Cannot save configuration due to the following issues:</h3>
                        <ul class="list-disc pl-5">
                            ${validationResults.errors.map(error => `<li>${error}</li>`).join('')}
                        </ul>
                    </div>
                `;

                Toastify({
                    text: "Configuration has issues and cannot be saved.",
                    duration: 3000,
                    backgroundColor: "#ff4444"
                }).showToast();

                return;
            }

            // Gather form data
            const formData = new FormData(this);

            // Collect insufficient info phrases
            const insufficientPhrases = [];
            document.querySelectorAll('input[name="insufficient_phrase[]"]').forEach(input => {
                if (input.value.trim()) {
                    insufficientPhrases.push(input.value.trim());
                }
            });

            // Prepare data object
            const data = {
                tab: 'all',  // Indicate this is a full save of all settings
                // AI Models
                llm_model: formData.get('llm_model'),
                embedding_model: formData.get('embedding_model'),
                vision_model: formData.get('vision_model'),
                use_vision: formData.get('use_vision') === 'on',

                // Query Configuration
                preamble: formData.get('preamble'),
                anti_hallucination_mode: formData.get('default_mode'),
                anti_hallucination_custom_instructions: formData.get('custom_instructions'),
                prompt_templates: {
                    strict: formData.get('template_strict'),
                    balanced: formData.get('template_balanced'),
                    off: formData.get('template_off'),
                    general: formData.get('template_general'),
                    document_specific: formData.get('template_doc_specific')
                },
                insufficient_info_phrases: insufficientPhrases,
                followup_question_templates: {
                    default: formData.get('followup_default'),
                    insufficient_info: formData.get('followup_insufficient')
                },

                // Embedding Configuration
                chunk_size: parseInt(formData.get('chunk_size')),
                chunk_overlap: parseInt(formData.get('chunk_overlap')),
                extract_tables: formData.get('extract_tables') === 'on',
                extract_images: formData.get('extract_images') === 'on',
                extract_locations: formData.get('extract_locations') === 'on',
                location_confidence_threshold: parseFloat(formData.get('location_confidence_threshold')) || 0.5,
                max_locations_per_document: parseInt(formData.get('max_locations_per_document')) || 50,
                enable_geocoding: formData.get('enable_geocoding') === 'on',
                batch_size: parseInt(formData.get('batch_size')),
                processing_threads: parseInt(formData.get('processing_threads')),
                // Vision settings for embedding
                use_vision_during_embedding: formData.get('use_vision_during_embedding') === 'on',
                filter_sensitivity: formData.get('filter_sensitivity'),
                max_pdf_images: parseInt(formData.get('max_pdf_images')),
                show_filtered_images: formData.get('show_filtered_images') === 'on',

                // Multimodal RAG Configuration
                enable_multimodal: formData.get('enable_multimodal') === 'on',
                enable_parallel_processing: formData.get('enable_parallel_processing') === 'on',
                max_workers: parseInt(formData.get('max_workers')) || 4,
                processing_timeout: parseInt(formData.get('processing_timeout')) || 300,

                // Vision Model Configuration (Local Ollama only)
                vision_model_name: formData.get('vision_model_name') || 'gemma3:4b-it-q4_K_M',
                vision_model_base_url: formData.get('vision_model_base_url') || 'http://localhost:11434',
                vision_fallback_models: formData.get('vision_fallback_models') || 'minicpm-v,llava',

                // Image Processing Settings
                enable_image_extraction: formData.get('enable_image_extraction') === 'on',
                enable_vision_captions: formData.get('enable_vision_captions') === 'on',
                max_images_per_document: parseInt(formData.get('max_images_per_document')) || 50,
                min_image_size: parseInt(formData.get('min_image_size')) || 1024,
                filter_logos: formData.get('filter_logos') === 'on',
                filter_sensitivity_multimodal: parseFloat(formData.get('filter_sensitivity')) || 0.3,

                // Table Processing Settings
                enable_table_extraction: formData.get('enable_table_extraction') === 'on',
                table_strategy: formData.get('table_strategy') || 'advanced',
                use_camelot: formData.get('use_camelot') === 'on',
                use_tabula: formData.get('use_tabula') === 'on',
                max_tables_per_document: parseInt(formData.get('max_tables_per_document')) || 100,
                min_table_rows: parseInt(formData.get('min_table_rows')) || 2,
                min_table_cols: parseInt(formData.get('min_table_cols')) || 2,
                include_table_context: formData.get('include_table_context') === 'on',

                // Multimodal Chunking Settings
                enable_multimodal_chunking: formData.get('enable_multimodal_chunking') === 'on',
                preserve_content_relationships: formData.get('preserve_content_relationships') === 'on',
                max_chunk_size: parseInt(formData.get('max_chunk_size')) || 1000,
                chunk_overlap_multimodal: parseInt(formData.get('chunk_overlap')) || 200,
                image_chunk_strategy: formData.get('image_chunk_strategy') || 'separate',
                table_chunk_strategy: formData.get('table_chunk_strategy') || 'preserve',

                // Hybrid Search Settings
                enable_hybrid_search: formData.get('enable_hybrid_search') === 'on',
                separate_collections: formData.get('separate_collections') === 'on',
                text_weight: parseFloat(formData.get('text_weight')) || 0.6,
                image_weight: parseFloat(formData.get('image_weight')) || 0.2,
                table_weight: parseFloat(formData.get('table_weight')) || 0.2,
                max_text_results: parseInt(formData.get('max_text_results')) || 10,
                max_image_results: parseInt(formData.get('max_image_results')) || 5,
                max_table_results: parseInt(formData.get('max_table_results')) || 5,
                similarity_threshold: parseFloat(formData.get('similarity_threshold')) || 0.7,
                rerank_results: formData.get('rerank_results') === 'on',

                // Storage Settings
                enable_blob_storage: formData.get('enable_blob_storage') === 'on',
                compress_images: formData.get('compress_images') === 'on',
                blob_storage_path: formData.get('blob_storage_path') || './data/multimodal_blobs',
                max_blob_size_mb: parseInt(formData.get('max_blob_size_mb')) || 50,
                image_quality: parseInt(formData.get('image_quality')) || 85,
                metadata_cache_ttl: parseInt(formData.get('metadata_cache_ttl')) || 3600,

                // Performance Monitoring
                enable_performance_monitoring: formData.get('enable_performance_monitoring') === 'on',
                debug_mode: formData.get('debug_mode') === 'on',
                log_processing_steps: formData.get('log_processing_steps') === 'on'
            };

            // Get CSRF token
            const csrfToken = document.querySelector('input[name="csrf_token"]').value;

            // Send data to server
            const response = await fetch('/admin/unified_config', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrfToken
                },
                body: JSON.stringify(data)
            });

            const result = await response.json();

            // Show success/error message
            if (response.ok) {
                statusMessage.innerHTML = `
                    <div class="p-4 bg-green-100 text-green-700 rounded-md">
                        ${result.message || "Configuration saved successfully"}
                    </div>
                `;

                Toastify({
                    text: result.message || "Configuration saved successfully",
                    duration: 3000,
                    backgroundColor: "#00C851"
                }).showToast();

                // Update summary
                updateSummary();
            } else {
                statusMessage.innerHTML = `
                    <div class="p-4 bg-red-100 text-red-700 rounded-md">
                        Error: ${result.error || "Failed to save configuration"}
                    </div>
                `;

                Toastify({
                    text: `Error: ${result.error || "Failed to save configuration"}`,
                    duration: 3000,
                    backgroundColor: "#ff4444"
                }).showToast();
            }
        } catch (error) {
            statusMessage.innerHTML = `
                <div class="p-4 bg-red-100 text-red-700 rounded-md">
                    Error: ${error.message || "An unexpected error occurred"}
                </div>
            `;

            Toastify({
                text: `Error: ${error.message || "An unexpected error occurred"}`,
                duration: 3000,
                backgroundColor: "#ff4444"
            }).showToast();
        }
    });
}

function setupTabSaveButtons() {
    // Setup Models tab save button
    const saveModelsBtn = document.getElementById('saveModelsBtn');
    if (saveModelsBtn) {
        saveModelsBtn.addEventListener('click', async function() {
            await saveTabSettings('models');
        });
    } else {
        console.warn('saveModelsBtn not found.');
    }

    // Setup Query tab save button
    const saveQueryConfigBtn = document.getElementById('saveQueryConfigBtn');
    if (saveQueryConfigBtn) {
        saveQueryConfigBtn.addEventListener('click', async function() {
            await saveTabSettings('query');
        });
    } else {
        console.warn('saveQueryConfigBtn not found.');
    }

    // Setup Embedding tab save button
    const saveEmbeddingBtn = document.getElementById('saveEmbeddingBtn');
    if (saveEmbeddingBtn) {
        saveEmbeddingBtn.addEventListener('click', async function() {
            await saveTabSettings('embedding');
        });
    } else {
        console.warn('saveEmbeddingBtn not found.');
    }

    // Setup Multimodal tab save button
    const saveMultimodalBtn = document.getElementById('saveMultimodalBtn');
    if (saveMultimodalBtn) {
        saveMultimodalBtn.addEventListener('click', async function() {
            await saveTabSettings('multimodal');
        });
    } else {
        console.warn('saveMultimodalBtn not found.');
    }
}

async function saveTabSettings(tabType) {
    // Show loading state
    const statusMessage = document.getElementById('statusMessage');
    statusMessage.innerHTML = `
        <div class="p-4 bg-blue-100 text-blue-700 rounded-md flex items-center">
            <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-blue-700" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            Saving ${tabType} settings...
        </div>
    `;
    statusMessage.classList.remove('hidden');

    try {
        // Get form data
        let allSettingsFormElem = document.getElementById('unifiedConfigForm') || document.querySelector('form');
        if (!allSettingsFormElem) {
            console.warn('Form element not found.');
            return;
        }
        let allSettingsFormData = new FormData(allSettingsFormElem);

        // Prepare data object based on tab type
        let data = {};

        if (tabType === 'models') {
            // Validate models settings
            if (!document.querySelector('input[name="llm_model"]:checked')) {
                showError("No LLM model selected. Please select a language model.");
                return;
            }

            // AI Models data
            data = {
                tab: 'models',
                llm_model: allSettingsFormData.get('llm_model'),
                embedding_model: allSettingsFormData.get('embedding_model'),
                vision_model: allSettingsFormData.get('vision_model'),
                use_vision: allSettingsFormData.get('use_vision') === 'on'
            };
        }
        else if (tabType === 'query') {
            // Collect insufficient info phrases
            const insufficientPhrases = [];
            document.querySelectorAll('input[name="insufficient_phrase[]"]').forEach(input => {
                if (input.value.trim()) {
                    insufficientPhrases.push(input.value.trim());
                }
            });

            // Query Configuration data
            data = {
                tab: 'query',
                preamble: allSettingsFormData.get('preamble'),
                anti_hallucination_mode: allSettingsFormData.get('default_mode'),
                anti_hallucination_custom_instructions: allSettingsFormData.get('custom_instructions'),
                prompt_templates: {
                    strict: allSettingsFormData.get('template_strict'),
                    balanced: allSettingsFormData.get('template_balanced'),
                    off: allSettingsFormData.get('template_off'),
                    general: allSettingsFormData.get('template_general'),
                    document_specific: allSettingsFormData.get('template_doc_specific')
                },
                insufficient_info_phrases: insufficientPhrases,
                followup_question_templates: {
                    default: allSettingsFormData.get('followup_default'),
                    insufficient_info: allSettingsFormData.get('followup_insufficient')
                },

                // RAG Optimization Parameters for Query
                enable_query_caching: allSettingsFormData.get('enable_query_caching') === 'on',
                query_cache_ttl: parseInt(allSettingsFormData.get('query_cache_ttl')),
                use_redis_cache: allSettingsFormData.get('use_redis_cache') === 'on',
                redis_url: allSettingsFormData.get('redis_url'),
                enable_parallel_scoring: allSettingsFormData.get('enable_parallel_scoring') === 'on',
                parallel_threshold: parseInt(allSettingsFormData.get('parallel_threshold')),
                max_parallel_workers: parseInt(allSettingsFormData.get('max_parallel_workers')),
                enable_adaptive_retrieval: allSettingsFormData.get('enable_adaptive_retrieval') === 'on',
                simple_k_ratio: parseFloat(allSettingsFormData.get('simple_k_ratio')),
                complex_k_ratio: parseFloat(allSettingsFormData.get('complex_k_ratio')),
                min_adaptive_k: parseInt(allSettingsFormData.get('min_adaptive_k')),
                max_adaptive_k: parseInt(allSettingsFormData.get('max_adaptive_k')),
                enable_performance_monitoring: allSettingsFormData.get('enable_performance_monitoring') === 'on',
                log_cache_performance: allSettingsFormData.get('log_cache_performance') === 'on',
                log_adaptive_retrieval: allSettingsFormData.get('log_adaptive_retrieval') === 'on',
                log_parallel_processing: allSettingsFormData.get('log_parallel_processing') === 'on',
                auto_invalidate_cache: allSettingsFormData.get('auto_invalidate_cache') === 'on'
            };
        }
        else if (tabType === 'embedding') {
            // Validate embedding settings
            const chunkSize = parseInt(allSettingsFormData.get('chunk_size'));
            const chunkOverlap = parseInt(allSettingsFormData.get('chunk_overlap'));

            if (chunkOverlap >= chunkSize) {
                showError("Chunk overlap must be smaller than chunk size.");
                return;
            }

            // Embedding Configuration data
            data = {
                tab: 'embedding',
                chunk_size: chunkSize,
                chunk_overlap: chunkOverlap,
                extract_tables: allSettingsFormData.get('extract_tables') === 'on',
                extract_images: allSettingsFormData.get('extract_images') === 'on',
                extract_locations: allSettingsFormData.get('extract_locations') === 'on',
                location_confidence_threshold: parseFloat(allSettingsFormData.get('location_confidence_threshold')) || 0.5,
                max_locations_per_document: parseInt(allSettingsFormData.get('max_locations_per_document')) || 50,
                enable_geocoding: allSettingsFormData.get('enable_geocoding') === 'on',
                batch_size: parseInt(allSettingsFormData.get('batch_size')),
                processing_threads: parseInt(allSettingsFormData.get('processing_threads')),
                // Vision settings for embedding
                use_vision_during_embedding: allSettingsFormData.get('use_vision_during_embedding') === 'on',
                filter_sensitivity: allSettingsFormData.get('filter_sensitivity'),
                max_pdf_images: parseInt(allSettingsFormData.get('max_pdf_images')),
                show_filtered_images: allSettingsFormData.get('show_filtered_images') === 'on',

                // RAG Optimization Parameters for Embedding
                enable_embedding_caching: allSettingsFormData.get('enable_embedding_caching') === 'on',
                embedding_cache_ttl: parseInt(allSettingsFormData.get('embedding_cache_ttl')),
                prefer_semantic_chunking: allSettingsFormData.get('prefer_semantic_chunking') === 'on',
                semantic_threshold_length: parseInt(allSettingsFormData.get('semantic_threshold_length')),
                semantic_buffer_size: parseInt(allSettingsFormData.get('semantic_buffer_size')),
                semantic_breakpoint_threshold: parseInt(allSettingsFormData.get('semantic_breakpoint_threshold')),
                fallback_to_sentence: allSettingsFormData.get('fallback_to_sentence') === 'on'
            };
        }
        else if (tabType === 'multimodal') {
            // Multimodal RAG Configuration data
            data = {
                tab: 'multimodal',
                // Main multimodal settings
                enable_multimodal: allSettingsFormData.get('enable_multimodal') === 'on',
                enable_parallel_processing: allSettingsFormData.get('enable_parallel_processing') === 'on',
                max_workers: parseInt(allSettingsFormData.get('max_workers')) || 4,
                processing_timeout: parseInt(allSettingsFormData.get('processing_timeout')) || 300,

                // Vision Model Configuration (Local Ollama only)
                vision_model_name: allSettingsFormData.get('vision_model_name') || 'gemma3:4b-it-q4_K_M',
                vision_model_base_url: allSettingsFormData.get('vision_model_base_url') || 'http://localhost:11434',
                vision_fallback_models: allSettingsFormData.get('vision_fallback_models') || 'minicpm-v,llava',

                // Image Processing Settings
                enable_image_extraction: allSettingsFormData.get('enable_image_extraction') === 'on',
                enable_vision_captions: allSettingsFormData.get('enable_vision_captions') === 'on',
                max_images_per_document: parseInt(allSettingsFormData.get('max_images_per_document')) || 50,
                min_image_size: parseInt(allSettingsFormData.get('min_image_size')) || 1024,
                filter_logos: allSettingsFormData.get('filter_logos') === 'on',
                filter_sensitivity_multimodal: parseFloat(allSettingsFormData.get('filter_sensitivity')) || 0.3,

                // Table Processing Settings
                enable_table_extraction: allSettingsFormData.get('enable_table_extraction') === 'on',
                table_strategy: allSettingsFormData.get('table_strategy') || 'advanced',
                use_camelot: allSettingsFormData.get('use_camelot') === 'on',
                use_tabula: allSettingsFormData.get('use_tabula') === 'on',
                max_tables_per_document: parseInt(allSettingsFormData.get('max_tables_per_document')) || 100,
                min_table_rows: parseInt(allSettingsFormData.get('min_table_rows')) || 2,
                min_table_cols: parseInt(allSettingsFormData.get('min_table_cols')) || 2,
                include_table_context: allSettingsFormData.get('include_table_context') === 'on',

                // Multimodal Chunking Settings
                enable_multimodal_chunking: allSettingsFormData.get('enable_multimodal_chunking') === 'on',
                preserve_content_relationships: allSettingsFormData.get('preserve_content_relationships') === 'on',
                max_chunk_size: parseInt(allSettingsFormData.get('max_chunk_size')) || 1000,
                chunk_overlap_multimodal: parseInt(allSettingsFormData.get('chunk_overlap')) || 200,
                image_chunk_strategy: allSettingsFormData.get('image_chunk_strategy') || 'separate',
                table_chunk_strategy: allSettingsFormData.get('table_chunk_strategy') || 'preserve',

                // Hybrid Search Settings
                enable_hybrid_search: allSettingsFormData.get('enable_hybrid_search') === 'on',
                separate_collections: allSettingsFormData.get('separate_collections') === 'on',
                text_weight: parseFloat(allSettingsFormData.get('text_weight')) || 0.6,
                image_weight: parseFloat(allSettingsFormData.get('image_weight')) || 0.2,
                table_weight: parseFloat(allSettingsFormData.get('table_weight')) || 0.2,
                max_text_results: parseInt(allSettingsFormData.get('max_text_results')) || 10,
                max_image_results: parseInt(allSettingsFormData.get('max_image_results')) || 5,
                max_table_results: parseInt(allSettingsFormData.get('max_table_results')) || 5,
                similarity_threshold: parseFloat(allSettingsFormData.get('similarity_threshold')) || 0.7,
                rerank_results: allSettingsFormData.get('rerank_results') === 'on',

                // Storage Settings
                enable_blob_storage: allSettingsFormData.get('enable_blob_storage') === 'on',
                compress_images: allSettingsFormData.get('compress_images') === 'on',
                blob_storage_path: allSettingsFormData.get('blob_storage_path') || './data/multimodal_blobs',
                max_blob_size_mb: parseInt(allSettingsFormData.get('max_blob_size_mb')) || 50,
                image_quality: parseInt(allSettingsFormData.get('image_quality')) || 85,
                metadata_cache_ttl: parseInt(allSettingsFormData.get('metadata_cache_ttl')) || 3600,

                // Performance Monitoring
                enable_performance_monitoring: allSettingsFormData.get('enable_performance_monitoring') === 'on',
                debug_mode: allSettingsFormData.get('debug_mode') === 'on',
                log_processing_steps: allSettingsFormData.get('log_processing_steps') === 'on'
            };
        }

        // Get CSRF token
        const csrfToken = document.querySelector('input[name="csrf_token"]').value;

        // Send data to server
        const response = await fetch('/admin/unified_config', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': csrfToken
            },
            body: JSON.stringify(data)
        });

        const result = await response.json();

        // Show success/error message
        if (response.ok) {
            statusMessage.innerHTML = `
                <div class="p-4 bg-green-100 text-green-700 rounded-md">
                    ${result.message || `${tabType.charAt(0).toUpperCase() + tabType.slice(1)} settings saved successfully`}
                </div>
            `;

            Toastify({
                text: result.message || `${tabType.charAt(0).toUpperCase() + tabType.slice(1)} settings saved successfully`,
                duration: 3000,
                backgroundColor: "#00C851"
            }).showToast();

            // Update summary
            updateSummary();
        } else {
            statusMessage.innerHTML = `
                <div class="p-4 bg-red-100 text-red-700 rounded-md">
                    Error: ${result.error || `Failed to save ${tabType} settings`}
                </div>
            `;

            Toastify({
                text: `Error: ${result.error || `Failed to save ${tabType} settings`}`,
                duration: 3000,
                backgroundColor: "#ff4444"
            }).showToast();
        }
    } catch (error) {
        console.error(`Error saving ${tabType} settings:`, error);
        showError(`Failed to save ${tabType} settings: ${error.message}`);
    }
}

function showError(message) {
    const statusMessage = document.getElementById('statusMessage');
    statusMessage.innerHTML = `
        <div class="p-4 bg-red-100 text-red-700 rounded-md">
            ${message}
        </div>
    `;
    statusMessage.classList.remove('hidden');

    Toastify({
        text: message,
        duration: 3000,
        backgroundColor: "#ff4444"
    }).showToast();
}

/**
 * Apply text contrast fixes to ensure proper readability
 * @param {string} containerId - The ID of the container element
 */
function applyTextContrastFixes(containerId) {
    const container = document.getElementById(containerId);
    if (!container) return;

    // Apply text contrast fixes to ensure proper readability
    // These are additional fixes beyond what's in the CSS

    // Fix text colors for section headers
    container.querySelectorAll('.config-section-header').forEach(el => {
        el.style.color = '#1a202c';
    });

    // Fix text colors for section descriptions
    container.querySelectorAll('.config-section-description').forEach(el => {
        el.style.color = '#4a5568';
    });

    // Fix text colors for form elements
    container.querySelectorAll('input, select, textarea').forEach(el => {
        el.style.color = '#1a202c';
    });

    // Fix text colors for labels
    container.querySelectorAll('label').forEach(el => {
        if (el.classList.contains('text-gray-700') || el.classList.contains('font-medium')) {
            el.style.color = '#2d3748';
        }
    });

    // Fix text colors for helper text
    container.querySelectorAll('.text-gray-500, .text-xs').forEach(el => {
        el.style.color = '#6b7280';
    });

    // Fix text colors for dependency indicators
    container.querySelectorAll('.dependency-indicator, .text-blue-600').forEach(el => {
        el.style.color = '#2563eb';
    });
}

// Enhanced Threshold Slider Setup
function setupEnhancedThresholdSliders() {
    console.log('Setting up enhanced threshold sliders...');

    const sliders = [
        {
            id: 'hallucination_threshold_strict',
            type: 'strict',
            valueId: 'hallucination_threshold_strict_value',
            badgeId: 'strict_mode_badge',
            indicatorId: 'strict_indicator',
            descriptionId: 'strict_description',
            trackId: 'strict_track'
        },
        {
            id: 'hallucination_threshold_balanced',
            type: 'balanced',
            valueId: 'hallucination_threshold_balanced_value',
            badgeId: 'balanced_mode_badge',
            indicatorId: 'balanced_indicator',
            descriptionId: 'balanced_description',
            trackId: 'balanced_track'
        },
        {
            id: 'hallucination_threshold_default',
            type: 'default',
            valueId: 'hallucination_threshold_default_value',
            badgeId: 'default_mode_badge',
            indicatorId: 'default_indicator',
            descriptionId: 'default_description',
            trackId: 'default_track'
        }
    ];

    sliders.forEach(config => {
        const slider = document.getElementById(config.id);
        const valueDisplay = document.getElementById(config.valueId);
        const badge = document.getElementById(config.badgeId);
        const indicator = document.getElementById(config.indicatorId);
        const description = document.getElementById(config.descriptionId);
        const track = slider ? slider.parentNode.querySelector('.slider-track-fill') : null;

        console.log(`Checking slider ${config.id}:`, {
            slider: !!slider,
            valueDisplay: !!valueDisplay,
            badge: !!badge,
            indicator: !!indicator,
            description: !!description,
            track: !!track
        });

        if (slider && valueDisplay) {
            console.log(`Setting up slider: ${config.id}`);
            // Set initial values
            updateSliderDisplay(slider, valueDisplay, badge, indicator, description, track, config.type);

            // Enhanced event listeners for real-time updates with improved cross-browser support
            let isDragging = false;
            let updateTimeout = null;
            let animationFrameId = null;

            // Optimized update function using requestAnimationFrame
            function scheduleUpdate() {
                if (animationFrameId) {
                    cancelAnimationFrame(animationFrameId);
                }
                animationFrameId = requestAnimationFrame(() => {
                    updateSliderDisplay(slider, valueDisplay, badge, indicator, description, track, config.type);
                    if (isDragging) {
                        showSliderTooltip(slider, true);
                    }
                });
            }

            // Mouse/touch drag start
            slider.addEventListener('mousedown', function(e) {
                isDragging = true;
                this.dataset.dragging = 'true';
                // Show tooltip immediately on drag start
                showSliderTooltip(this, true);
            });

            slider.addEventListener('touchstart', function(e) {
                isDragging = true;
                this.dataset.dragging = 'true';
                showSliderTooltip(this, true);
            }, { passive: true });

            // Real-time input updates (fires during drag) - Enhanced for all browsers
            slider.addEventListener('input', function(e) {
                // Use requestAnimationFrame for smooth updates
                scheduleUpdate();
            });

            // Additional event for browsers that don't fire 'input' continuously
            slider.addEventListener('mousemove', function(e) {
                if (isDragging && e.buttons === 1) {
                    // Force update during mouse drag using RAF
                    scheduleUpdate();
                }
            });

            // Touch move for mobile devices
            slider.addEventListener('touchmove', function(e) {
                if (isDragging) {
                    scheduleUpdate();
                }
            }, { passive: true });

            // Additional events for better cross-browser support
            slider.addEventListener('propertychange', function(e) {
                // IE support for real-time updates
                if (e.propertyName === 'value') {
                    scheduleUpdate();
                }
            });

            // For older browsers that might not support 'input' event properly
            slider.addEventListener('oninput', function(e) {
                scheduleUpdate();
            });

            // Drag end events
            slider.addEventListener('mouseup', function() {
                isDragging = false;
                this.dataset.dragging = 'false';
                // Cancel any pending animation frame
                if (animationFrameId) {
                    cancelAnimationFrame(animationFrameId);
                    animationFrameId = null;
                }
                // Trigger final animation and save indication
                updateSliderDisplay(this, valueDisplay, badge, indicator, description, track, config.type);
                showSliderChangeNotification(config.type, this.value);
                // Hide tooltip after a delay
                setTimeout(() => showSliderTooltip(this, false), 1000);
            });

            slider.addEventListener('touchend', function() {
                isDragging = false;
                this.dataset.dragging = 'false';
                // Cancel any pending animation frame
                if (animationFrameId) {
                    cancelAnimationFrame(animationFrameId);
                    animationFrameId = null;
                }
                updateSliderDisplay(this, valueDisplay, badge, indicator, description, track, config.type);
                showSliderChangeNotification(config.type, this.value);
                setTimeout(() => showSliderTooltip(this, false), 1000);
            });

            // Handle mouse leave during drag (for desktop)
            slider.addEventListener('mouseleave', function() {
                if (isDragging) {
                    isDragging = false;
                    this.dataset.dragging = 'false';
                    // Cancel any pending animation frame
                    if (animationFrameId) {
                        cancelAnimationFrame(animationFrameId);
                        animationFrameId = null;
                    }
                    updateSliderDisplay(this, valueDisplay, badge, indicator, description, track, config.type);
                    showSliderTooltip(this, false);
                }
            });

            // Change event (fallback for other input methods)
            slider.addEventListener('change', function() {
                isDragging = false;
                this.dataset.dragging = 'false';
                // Cancel any pending animation frame
                if (animationFrameId) {
                    cancelAnimationFrame(animationFrameId);
                    animationFrameId = null;
                }
                updateSliderDisplay(this, valueDisplay, badge, indicator, description, track, config.type);
                showSliderChangeNotification(config.type, this.value);
            });

            // Enhanced keyboard accessibility
            slider.addEventListener('keydown', function(e) {
                let step = 0.05;
                let newValue = parseFloat(this.value);

                switch(e.key) {
                    case 'ArrowLeft':
                    case 'ArrowDown':
                        newValue = Math.max(parseFloat(this.min), newValue - step);
                        break;
                    case 'ArrowRight':
                    case 'ArrowUp':
                        newValue = Math.min(parseFloat(this.max), newValue + step);
                        break;
                    case 'Home':
                        newValue = parseFloat(this.min);
                        break;
                    case 'End':
                        newValue = parseFloat(this.max);
                        break;
                    case 'PageUp':
                        newValue = Math.min(parseFloat(this.max), newValue + (step * 5));
                        break;
                    case 'PageDown':
                        newValue = Math.max(parseFloat(this.min), newValue - (step * 5));
                        break;
                    default:
                        return;
                }

                e.preventDefault();
                this.value = newValue.toFixed(2);
                this.dataset.dragging = 'false';
                updateSliderDisplay(this, valueDisplay, badge, indicator, description, track, config.type);

                // Debounced notification for keyboard input
                clearTimeout(this.keyboardTimeout);
                this.keyboardTimeout = setTimeout(() => {
                    showSliderChangeNotification(config.type, this.value);
                }, 300);
            });

            // Focus and blur events for better accessibility
            slider.addEventListener('focus', function() {
                const tooltip = this.parentNode.querySelector('.slider-value-tooltip');
                if (tooltip) {
                    tooltip.style.opacity = '1';
                    tooltip.style.transform = 'translateX(-50%) translateY(-5px)';
                }
            });

            slider.addEventListener('blur', function() {
                const tooltip = this.parentNode.querySelector('.slider-value-tooltip');
                if (tooltip) {
                    tooltip.style.opacity = '0';
                    tooltip.style.transform = 'translateX(-50%) translateY(0)';
                }
            });
        }
    });
}

// Enhanced Relevance Slider Setup
function setupEnhancedRelevanceSlider() {
    console.log('Setting up enhanced relevance slider...');

    const config = {
        id: 'relevance_threshold',
        type: 'relevance',
        valueId: 'relevance_threshold_value',
        badgeId: 'relevance_mode_badge',
        indicatorId: 'relevance_indicator',
        descriptionId: 'relevance_description',
        trackId: 'relevance_track'
    };

    const slider = document.getElementById(config.id);
    const valueDisplay = document.getElementById(config.valueId);
    const badge = document.getElementById(config.badgeId);
    const indicator = document.getElementById(config.indicatorId);
    const description = document.getElementById(config.descriptionId);
    const track = slider ? slider.parentNode.querySelector('.slider-track-fill') : null;

    console.log(`Checking relevance slider ${config.id}:`, {
        slider: !!slider,
        valueDisplay: !!valueDisplay,
        badge: !!badge,
        indicator: !!indicator,
        description: !!description,
        track: !!track
    });

    if (slider && valueDisplay) {
        console.log(`Setting up relevance slider: ${config.id}`);
        // Set initial values
        updateSliderDisplay(slider, valueDisplay, badge, indicator, description, track, config.type);

        // Enhanced event listeners for real-time updates with improved cross-browser support
        let isDragging = false;
        let updateTimeout = null;
        let animationFrameId = null;

        // Optimized update function using requestAnimationFrame
        function scheduleUpdate() {
            if (animationFrameId) {
                cancelAnimationFrame(animationFrameId);
            }
            animationFrameId = requestAnimationFrame(() => {
                updateSliderDisplay(slider, valueDisplay, badge, indicator, description, track, config.type);
                if (isDragging) {
                    showSliderTooltip(slider, true);
                }
            });
        }

        // Mouse/touch drag start
        slider.addEventListener('mousedown', function(e) {
            isDragging = true;
            this.dataset.dragging = 'true';
            showSliderTooltip(this, true);
        });

        slider.addEventListener('touchstart', function(e) {
            isDragging = true;
            this.dataset.dragging = 'true';
            showSliderTooltip(this, true);
        }, { passive: true });

        // Real-time input updates (fires during drag) - Enhanced for all browsers
        slider.addEventListener('input', function(e) {
            scheduleUpdate();
        });

        // Additional event for browsers that don't fire 'input' continuously
        slider.addEventListener('mousemove', function(e) {
            if (isDragging && e.buttons === 1) {
                scheduleUpdate();
            }
        });

        // Touch move for mobile devices
        slider.addEventListener('touchmove', function(e) {
            if (isDragging) {
                scheduleUpdate();
            }
        }, { passive: true });

        // Additional events for better cross-browser support
        slider.addEventListener('propertychange', function(e) {
            if (e.propertyName === 'value') {
                scheduleUpdate();
            }
        });

        slider.addEventListener('oninput', function(e) {
            scheduleUpdate();
        });

        // Drag end events
        slider.addEventListener('mouseup', function() {
            isDragging = false;
            this.dataset.dragging = 'false';
            if (animationFrameId) {
                cancelAnimationFrame(animationFrameId);
                animationFrameId = null;
            }
            updateSliderDisplay(this, valueDisplay, badge, indicator, description, track, config.type);
            showSliderChangeNotification(config.type, this.value);
            setTimeout(() => showSliderTooltip(this, false), 1000);
        });

        slider.addEventListener('touchend', function() {
            isDragging = false;
            this.dataset.dragging = 'false';
            if (animationFrameId) {
                cancelAnimationFrame(animationFrameId);
                animationFrameId = null;
            }
            updateSliderDisplay(this, valueDisplay, badge, indicator, description, track, config.type);
            showSliderChangeNotification(config.type, this.value);
            setTimeout(() => showSliderTooltip(this, false), 1000);
        });

        // Handle mouse leave during drag (for desktop)
        slider.addEventListener('mouseleave', function() {
            if (isDragging) {
                isDragging = false;
                this.dataset.dragging = 'false';
                if (animationFrameId) {
                    cancelAnimationFrame(animationFrameId);
                    animationFrameId = null;
                }
                updateSliderDisplay(this, valueDisplay, badge, indicator, description, track, config.type);
                showSliderTooltip(this, false);
            }
        });

        // Change event (fallback for other input methods)
        slider.addEventListener('change', function() {
            isDragging = false;
            this.dataset.dragging = 'false';
            if (animationFrameId) {
                cancelAnimationFrame(animationFrameId);
                animationFrameId = null;
            }
            updateSliderDisplay(this, valueDisplay, badge, indicator, description, track, config.type);
            showSliderChangeNotification(config.type, this.value);
        });

        // Enhanced keyboard accessibility
        slider.addEventListener('keydown', function(e) {
            let step = 0.05;
            let newValue = parseFloat(this.value);

            switch(e.key) {
                case 'ArrowLeft':
                case 'ArrowDown':
                    newValue = Math.max(parseFloat(this.min), newValue - step);
                    break;
                case 'ArrowRight':
                case 'ArrowUp':
                    newValue = Math.min(parseFloat(this.max), newValue + step);
                    break;
                case 'Home':
                    newValue = parseFloat(this.min);
                    break;
                case 'End':
                    newValue = parseFloat(this.max);
                    break;
                case 'PageUp':
                    newValue = Math.min(parseFloat(this.max), newValue + (step * 5));
                    break;
                case 'PageDown':
                    newValue = Math.max(parseFloat(this.min), newValue - (step * 5));
                    break;
                default:
                    return;
            }

            e.preventDefault();
            this.value = newValue.toFixed(2);
            this.dataset.dragging = 'false';
            updateSliderDisplay(this, valueDisplay, badge, indicator, description, track, config.type);

            clearTimeout(this.keyboardTimeout);
            this.keyboardTimeout = setTimeout(() => {
                showSliderChangeNotification(config.type, this.value);
            }, 300);
        });

        // Focus and blur events for better accessibility
        slider.addEventListener('focus', function() {
            const tooltip = this.parentNode.querySelector('.slider-value-tooltip');
            if (tooltip) {
                tooltip.style.opacity = '1';
                tooltip.style.transform = 'translateX(-50%) translateY(-5px)';
            }
        });

        slider.addEventListener('blur', function() {
            const tooltip = this.parentNode.querySelector('.slider-value-tooltip');
            if (tooltip) {
                tooltip.style.opacity = '0';
                tooltip.style.transform = 'translateX(-50%) translateY(0)';
            }
        });
    } else {
        console.warn(`Missing elements for relevance slider ${config.id}`);
    }
}

function updateSliderDisplay(slider, valueDisplay, badge, indicator, description, track, type) {
    const value = parseFloat(slider.value);
    const min = parseFloat(slider.min);
    const max = parseFloat(slider.max);
    const percentage = ((value - min) / (max - min)) * 100;

    // Update value display immediately (no animation delay for real-time updates)
    if (valueDisplay) {
        valueDisplay.textContent = value.toFixed(2);
        // Only animate on change, not during drag
        if (slider.dataset.dragging !== 'true') {
            valueDisplay.style.transform = 'scale(1.1)';
            setTimeout(() => {
                valueDisplay.style.transform = 'scale(1)';
            }, 150);
        }
    }

    // Update track fill immediately with smooth transition
    if (track) {
        track.style.width = percentage + '%';
        // Add smooth transition only when not dragging for better performance
        if (slider.dataset.dragging !== 'true') {
            track.style.transition = 'width 0.2s ease';
        } else {
            track.style.transition = 'none';
        }
    }

    // Update indicator bar immediately with smooth transition
    if (indicator) {
        indicator.style.width = percentage + '%';
        // Add smooth transition only when not dragging for better performance
        if (slider.dataset.dragging !== 'true') {
            indicator.style.transition = 'width 0.2s ease';
        } else {
            indicator.style.transition = 'none';
        }
    }

    // Update badge and description based on value immediately
    if (badge && description) {
        const { badgeText, badgeClass, descText } = getThresholdLabels(type, value);

        // Only update if values have changed to prevent unnecessary DOM manipulation
        if (badge.textContent !== badgeText) {
            badge.textContent = badgeText;
            badge.className = `badge threshold-badge ${badgeClass}`;

            // Add subtle animation for badge changes
            if (slider.dataset.dragging !== 'true') {
                badge.style.transform = 'scale(1.05)';
                setTimeout(() => {
                    badge.style.transform = 'scale(1)';
                }, 150);
            }
        }

        if (description.textContent !== descText) {
            description.textContent = descText;
        }
    }

    // Update tooltip position and content immediately
    const tooltip = slider.parentNode.querySelector('.slider-value-tooltip');
    if (tooltip) {
        const tooltipValue = tooltip.querySelector('span');
        if (tooltipValue) {
            tooltipValue.textContent = value.toFixed(2);
        }

        // Improved tooltip positioning
        updateTooltipPosition(slider, tooltip, percentage);
    }
}

// Helper function for tooltip positioning
function updateTooltipPosition(slider, tooltip, percentage) {
    // Use CSS custom property for smoother positioning
    tooltip.style.setProperty('--slider-percentage', percentage + '%');

    // Fallback for browsers that don't support CSS custom properties
    const sliderRect = slider.getBoundingClientRect();
    const tooltipRect = tooltip.getBoundingClientRect();
    const sliderWidth = sliderRect.width;
    const tooltipWidth = tooltipRect.width || 60; // fallback width

    // Calculate position with bounds checking
    let leftPosition = percentage;
    const tooltipOffset = (tooltipWidth / 2) / sliderWidth * 100;

    if (leftPosition < tooltipOffset) {
        leftPosition = tooltipOffset;
    } else if (leftPosition > (100 - tooltipOffset)) {
        leftPosition = 100 - tooltipOffset;
    }

    tooltip.style.left = leftPosition + '%';
    tooltip.style.transform = 'translateX(-50%)';
}

// Function to show/hide slider tooltip
function showSliderTooltip(slider, show) {
    const tooltip = slider.parentNode.querySelector('.slider-value-tooltip');
    if (tooltip) {
        if (show) {
            tooltip.style.opacity = '1';
            tooltip.style.transform = 'translateX(-50%) translateY(-5px)';
            tooltip.style.visibility = 'visible';
        } else {
            tooltip.style.opacity = '0';
            tooltip.style.transform = 'translateX(-50%) translateY(0px)';
            // Use timeout to hide visibility after transition
            setTimeout(() => {
                if (tooltip.style.opacity === '0') {
                    tooltip.style.visibility = 'hidden';
                }
            }, 200);
        }
    }
}

function getThresholdLabels(type, value) {
    const labels = {
        strict: {
            low: { badge: 'Lenient', class: 'bg-success', desc: 'Lower security, more permissive detection' },
            medium: { badge: 'Moderate', class: 'bg-warning text-dark', desc: 'Balanced security and usability' },
            high: { badge: 'High Security', class: 'bg-danger', desc: 'Maximum accuracy, minimal speculation' }
        },
        balanced: {
            low: { badge: 'Permissive', class: 'bg-success', desc: 'More helpful responses, less strict' },
            medium: { badge: 'Moderate', class: 'bg-warning text-dark', desc: 'Moderate accuracy with helpful context' },
            high: { badge: 'Strict', class: 'bg-danger', desc: 'Higher accuracy, more conservative responses' }
        },
        default: {
            low: { badge: 'Lenient', class: 'bg-success', desc: 'Relaxed detection for general use' },
            medium: { badge: 'Standard', class: 'bg-success', desc: 'Standard detection for general use' },
            high: { badge: 'Strict', class: 'bg-warning text-dark', desc: 'Stricter fallback detection' }
        },
        relevance: {
            low: { badge: 'Inclusive', class: 'bg-success', desc: 'Include more documents with lower similarity scores' },
            medium: { badge: 'Standard', class: 'bg-primary', desc: 'Standard relevance filtering for balanced results' },
            high: { badge: 'Selective', class: 'bg-warning text-dark', desc: 'Only include highly relevant documents' },
            veryHigh: { badge: 'Exact Match', class: 'bg-danger', desc: 'Only include documents with very high similarity' }
        }
    };

    let thresholds, level;

    if (type === 'relevance') {
        // Relevance thresholds: 0-0.2 (Inclusive), 0.2-0.5 (Standard), 0.5-0.8 (Selective), 0.8+ (Exact Match)
        thresholds = [0.2, 0.5, 0.8];
        if (value <= thresholds[0]) {
            level = 'low';
        } else if (value <= thresholds[1]) {
            level = 'medium';
        } else if (value <= thresholds[2]) {
            level = 'high';
        } else {
            level = 'veryHigh';
        }
    } else {
        // Hallucination detection thresholds
        thresholds = type === 'balanced' ? [0.35, 0.6] : [0.5, 0.75];
        level = value <= thresholds[0] ? 'low' : value <= thresholds[1] ? 'medium' : 'high';
    }

    return {
        badgeText: labels[type][level].badge,
        badgeClass: labels[type][level].class,
        descText: labels[type][level].desc
    };
}

// Debounced notification system to prevent spam
const notificationTimeouts = {};

function showSliderChangeNotification(type, value) {
    const typeNames = {
        strict: 'Strict Mode',
        balanced: 'Balanced Mode',
        default: 'Default Mode',
        relevance: 'Relevance Threshold'
    };

    // Clear existing timeout for this slider type
    if (notificationTimeouts[type]) {
        clearTimeout(notificationTimeouts[type]);
    }

    // Set new timeout for debounced notification
    notificationTimeouts[type] = setTimeout(() => {
        // Use Toastify if available, otherwise console log
        if (typeof Toastify !== 'undefined') {
            Toastify({
                text: `${typeNames[type]} threshold updated to ${value}`,
                duration: 3000,
                backgroundColor: "#00C851"
            }).showToast();
        } else {
            console.log(`${typeNames[type]} threshold updated to ${value}`);
        }
    }, 500); // 500ms delay to prevent spam
}

// Update summary panel with latest config
async function updateSummaryPanel() {
    try {
        const response = await fetch('/api/settings/query_config', { method: 'GET' });
        if (!response.ok) return;
        const data = await response.json();
        if (data.llm_model && document.getElementById('summary-llm')) {
            document.getElementById('summary-llm').textContent = data.llm_model;
        }
        if (data.embedding_model && document.getElementById('summary-embedding')) {
            document.getElementById('summary-embedding').textContent = data.embedding_model;
        }
        if (data.vision_model && document.getElementById('summary-vision')) {
            document.getElementById('summary-vision').textContent = data.vision_model;
        }
        if (typeof data.use_vision !== 'undefined' && document.getElementById('summary-vision-enabled')) {
            document.getElementById('summary-vision-enabled').textContent = data.use_vision ? 'Enabled' : 'Disabled';
        }
    } catch (err) {
        // Silent fail
    }
}
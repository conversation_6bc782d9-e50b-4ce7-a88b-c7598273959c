Requirement already satisfied: llama-index in d:\erdb_ai_cursor\venv_new\lib\site-packages (0.13.0)
Requirement already satisfied: llama-index-core in d:\erdb_ai_cursor\venv_new\lib\site-packages (0.13.0)
Requirement already satisfied: llama-index-embeddings-ollama in d:\erdb_ai_cursor\venv_new\lib\site-packages (0.6.0)
Collecting tabula-py==2.7.0
  Downloading tabula_py-2.7.0-py3-none-any.whl.metadata (7.3 kB)
Collecting python-docx==0.8.11
  Downloading python-docx-0.8.11.tar.gz (5.6 MB)
     ---------------------------------------- 5.6/5.6 MB 4.2 MB/s eta 0:00:00
  Preparing metadata (setup.py): started
  Preparing metadata (setup.py): finished with status 'done'
Requirement already satisfied: pandas>=0.25.3 in d:\erdb_ai_cursor\venv_new\lib\site-packages (from tabula-py==2.7.0) (2.2.3)
Requirement already satisfied: numpy in d:\erdb_ai_cursor\venv_new\lib\site-packages (from tabula-py==2.7.0) (2.2.5)
Requirement already satisfied: distro in d:\erdb_ai_cursor\venv_new\lib\site-packages (from tabula-py==2.7.0) (1.9.0)
Requirement already satisfied: lxml>=2.3.2 in d:\erdb_ai_cursor\venv_new\lib\site-packages (from python-docx==0.8.11) (5.4.0)
Requirement already satisfied: llama-index-cli<0.6,>=0.5.0 in d:\erdb_ai_cursor\venv_new\lib\site-packages (from llama-index) (0.5.0)
Requirement already satisfied: llama-index-embeddings-openai<0.6,>=0.5.0 in d:\erdb_ai_cursor\venv_new\lib\site-packages (from llama-index) (0.5.0)
Requirement already satisfied: llama-index-indices-managed-llama-cloud>=0.4.0 in d:\erdb_ai_cursor\venv_new\lib\site-packages (from llama-index) (0.9.0)
Requirement already satisfied: llama-index-llms-openai<0.6,>=0.5.0 in d:\erdb_ai_cursor\venv_new\lib\site-packages (from llama-index) (0.5.0)
Requirement already satisfied: llama-index-readers-file<0.6,>=0.5.0 in d:\erdb_ai_cursor\venv_new\lib\site-packages (from llama-index) (0.5.0)
Requirement already satisfied: llama-index-readers-llama-parse>=0.4.0 in d:\erdb_ai_cursor\venv_new\lib\site-packages (from llama-index) (0.5.0)
Requirement already satisfied: nltk>3.8.1 in d:\erdb_ai_cursor\venv_new\lib\site-packages (from llama-index) (3.9.1)
Requirement already satisfied: aiohttp<4,>=3.8.6 in d:\erdb_ai_cursor\venv_new\lib\site-packages (from llama-index-core) (3.11.18)
Requirement already satisfied: aiosqlite in d:\erdb_ai_cursor\venv_new\lib\site-packages (from llama-index-core) (0.21.0)
Requirement already satisfied: banks<3,>=2.2.0 in d:\erdb_ai_cursor\venv_new\lib\site-packages (from llama-index-core) (2.2.0)
Requirement already satisfied: dataclasses-json in d:\erdb_ai_cursor\venv_new\lib\site-packages (from llama-index-core) (0.6.7)
Requirement already satisfied: deprecated>=1.2.9.3 in d:\erdb_ai_cursor\venv_new\lib\site-packages (from llama-index-core) (1.2.18)
Requirement already satisfied: dirtyjson<2,>=1.0.8 in d:\erdb_ai_cursor\venv_new\lib\site-packages (from llama-index-core) (1.0.8)
Requirement already satisfied: filetype<2,>=1.2.0 in d:\erdb_ai_cursor\venv_new\lib\site-packages (from llama-index-core) (1.2.0)
Requirement already satisfied: fsspec>=2023.5.0 in d:\erdb_ai_cursor\venv_new\lib\site-packages (from llama-index-core) (2025.5.1)
Requirement already satisfied: httpx in d:\erdb_ai_cursor\venv_new\lib\site-packages (from llama-index-core) (0.28.1)
Requirement already satisfied: llama-index-workflows<2,>=1.0.1 in d:\erdb_ai_cursor\venv_new\lib\site-packages (from llama-index-core) (1.2.0)
Requirement already satisfied: nest-asyncio<2,>=1.5.8 in d:\erdb_ai_cursor\venv_new\lib\site-packages (from llama-index-core) (1.6.0)
Requirement already satisfied: networkx>=3.0 in d:\erdb_ai_cursor\venv_new\lib\site-packages (from llama-index-core) (3.5)
Requirement already satisfied: pillow>=9.0.0 in d:\erdb_ai_cursor\venv_new\lib\site-packages (from llama-index-core) (10.4.0)
Requirement already satisfied: platformdirs in d:\erdb_ai_cursor\venv_new\lib\site-packages (from llama-index-core) (4.3.8)
Requirement already satisfied: pydantic>=2.8.0 in d:\erdb_ai_cursor\venv_new\lib\site-packages (from llama-index-core) (2.11.7)
Requirement already satisfied: pyyaml>=6.0.1 in d:\erdb_ai_cursor\venv_new\lib\site-packages (from llama-index-core) (6.0.2)
Requirement already satisfied: requests>=2.31.0 in d:\erdb_ai_cursor\venv_new\lib\site-packages (from llama-index-core) (2.32.3)
Requirement already satisfied: setuptools>=80.9.0 in d:\erdb_ai_cursor\venv_new\lib\site-packages (from llama-index-core) (80.9.0)
Requirement already satisfied: sqlalchemy>=1.4.49 in d:\erdb_ai_cursor\venv_new\lib\site-packages (from sqlalchemy[asyncio]>=1.4.49->llama-index-core) (2.0.41)
Requirement already satisfied: tenacity!=8.4.0,<10.0.0,>=8.2.0 in d:\erdb_ai_cursor\venv_new\lib\site-packages (from llama-index-core) (9.1.2)
Requirement already satisfied: tiktoken>=0.7.0 in d:\erdb_ai_cursor\venv_new\lib\site-packages (from llama-index-core) (0.9.0)
Requirement already satisfied: tqdm<5,>=4.66.1 in d:\erdb_ai_cursor\venv_new\lib\site-packages (from llama-index-core) (4.67.1)
Requirement already satisfied: typing-extensions>=4.5.0 in d:\erdb_ai_cursor\venv_new\lib\site-packages (from llama-index-core) (4.14.0)
Requirement already satisfied: typing-inspect>=0.8.0 in d:\erdb_ai_cursor\venv_new\lib\site-packages (from llama-index-core) (0.9.0)
Requirement already satisfied: wrapt in d:\erdb_ai_cursor\venv_new\lib\site-packages (from llama-index-core) (1.17.2)
Requirement already satisfied: aiohappyeyeballs>=2.3.0 in d:\erdb_ai_cursor\venv_new\lib\site-packages (from aiohttp<4,>=3.8.6->llama-index-core) (2.6.1)
Requirement already satisfied: aiosignal>=1.1.2 in d:\erdb_ai_cursor\venv_new\lib\site-packages (from aiohttp<4,>=3.8.6->llama-index-core) (1.3.2)
Requirement already satisfied: attrs>=17.3.0 in d:\erdb_ai_cursor\venv_new\lib\site-packages (from aiohttp<4,>=3.8.6->llama-index-core) (25.3.0)
Requirement already satisfied: frozenlist>=1.1.1 in d:\erdb_ai_cursor\venv_new\lib\site-packages (from aiohttp<4,>=3.8.6->llama-index-core) (1.7.0)
Requirement already satisfied: multidict<7.0,>=4.5 in d:\erdb_ai_cursor\venv_new\lib\site-packages (from aiohttp<4,>=3.8.6->llama-index-core) (6.5.0)
Requirement already satisfied: propcache>=0.2.0 in d:\erdb_ai_cursor\venv_new\lib\site-packages (from aiohttp<4,>=3.8.6->llama-index-core) (0.3.2)
Requirement already satisfied: yarl<2.0,>=1.17.0 in d:\erdb_ai_cursor\venv_new\lib\site-packages (from aiohttp<4,>=3.8.6->llama-index-core) (1.20.1)
Requirement already satisfied: griffe in d:\erdb_ai_cursor\venv_new\lib\site-packages (from banks<3,>=2.2.0->llama-index-core) (1.8.0)
Requirement already satisfied: jinja2 in d:\erdb_ai_cursor\venv_new\lib\site-packages (from banks<3,>=2.2.0->llama-index-core) (3.1.6)
Requirement already satisfied: openai>=1.1.0 in d:\erdb_ai_cursor\venv_new\lib\site-packages (from llama-index-embeddings-openai<0.6,>=0.5.0->llama-index) (1.90.0)
Requirement already satisfied: beautifulsoup4<5,>=4.12.3 in d:\erdb_ai_cursor\venv_new\lib\site-packages (from llama-index-readers-file<0.6,>=0.5.0->llama-index) (4.13.4)
Requirement already satisfied: defusedxml>=0.7.1 in d:\erdb_ai_cursor\venv_new\lib\site-packages (from llama-index-readers-file<0.6,>=0.5.0->llama-index) (0.7.1)
Requirement already satisfied: pypdf<6,>=5.1.0 in d:\erdb_ai_cursor\venv_new\lib\site-packages (from llama-index-readers-file<0.6,>=0.5.0->llama-index) (5.4.0)
Requirement already satisfied: striprtf<0.0.27,>=0.0.26 in d:\erdb_ai_cursor\venv_new\lib\site-packages (from llama-index-readers-file<0.6,>=0.5.0->llama-index) (0.0.26)
Requirement already satisfied: soupsieve>1.2 in d:\erdb_ai_cursor\venv_new\lib\site-packages (from beautifulsoup4<5,>=4.12.3->llama-index-readers-file<0.6,>=0.5.0->llama-index) (2.7)
Requirement already satisfied: llama-index-instrumentation>=0.1.0 in d:\erdb_ai_cursor\venv_new\lib\site-packages (from llama-index-workflows<2,>=1.0.1->llama-index-core) (0.3.0)
Requirement already satisfied: anyio<5,>=3.5.0 in d:\erdb_ai_cursor\venv_new\lib\site-packages (from openai>=1.1.0->llama-index-embeddings-openai<0.6,>=0.5.0->llama-index) (4.9.0)
Requirement already satisfied: jiter<1,>=0.4.0 in d:\erdb_ai_cursor\venv_new\lib\site-packages (from openai>=1.1.0->llama-index-embeddings-openai<0.6,>=0.5.0->llama-index) (0.10.0)
Requirement already satisfied: sniffio in d:\erdb_ai_cursor\venv_new\lib\site-packages (from openai>=1.1.0->llama-index-embeddings-openai<0.6,>=0.5.0->llama-index) (1.3.1)
Requirement already satisfied: idna>=2.8 in d:\erdb_ai_cursor\venv_new\lib\site-packages (from anyio<5,>=3.5.0->openai>=1.1.0->llama-index-embeddings-openai<0.6,>=0.5.0->llama-index) (3.10)
Requirement already satisfied: certifi in d:\erdb_ai_cursor\venv_new\lib\site-packages (from httpx->llama-index-core) (2025.4.26)
Requirement already satisfied: httpcore==1.* in d:\erdb_ai_cursor\venv_new\lib\site-packages (from httpx->llama-index-core) (1.0.9)
Requirement already satisfied: h11>=0.16 in d:\erdb_ai_cursor\venv_new\lib\site-packages (from httpcore==1.*->httpx->llama-index-core) (0.16.0)
Requirement already satisfied: python-dateutil>=2.8.2 in d:\erdb_ai_cursor\venv_new\lib\site-packages (from pandas>=0.25.3->tabula-py==2.7.0) (2.9.0.post0)
Requirement already satisfied: pytz>=2020.1 in d:\erdb_ai_cursor\venv_new\lib\site-packages (from pandas>=0.25.3->tabula-py==2.7.0) (2025.2)
Requirement already satisfied: tzdata>=2022.7 in d:\erdb_ai_cursor\venv_new\lib\site-packages (from pandas>=0.25.3->tabula-py==2.7.0) (2025.2)
Requirement already satisfied: annotated-types>=0.6.0 in d:\erdb_ai_cursor\venv_new\lib\site-packages (from pydantic>=2.8.0->llama-index-core) (0.7.0)
Requirement already satisfied: pydantic-core==2.33.2 in d:\erdb_ai_cursor\venv_new\lib\site-packages (from pydantic>=2.8.0->llama-index-core) (2.33.2)
Requirement already satisfied: typing-inspection>=0.4.0 in d:\erdb_ai_cursor\venv_new\lib\site-packages (from pydantic>=2.8.0->llama-index-core) (0.4.1)
Requirement already satisfied: colorama in d:\erdb_ai_cursor\venv_new\lib\site-packages (from tqdm<5,>=4.66.1->llama-index-core) (0.4.6)
INFO: pip is looking at multiple versions of llama-index-embeddings-ollama to determine which version is compatible with other requirements. This could take a while.
Collecting llama-index-embeddings-ollama
  Downloading llama_index_embeddings_ollama-0.7.0-py3-none-any.whl.metadata (400 bytes)
Requirement already satisfied: ollama>=0.3.1 in d:\erdb_ai_cursor\venv_new\lib\site-packages (from llama-index-embeddings-ollama) (0.5.1)
Requirement already satisfied: llama-cloud==0.1.35 in d:\erdb_ai_cursor\venv_new\lib\site-packages (from llama-index-indices-managed-llama-cloud>=0.4.0->llama-index) (0.1.35)
Requirement already satisfied: llama-parse>=0.5.0 in d:\erdb_ai_cursor\venv_new\lib\site-packages (from llama-index-readers-llama-parse>=0.4.0->llama-index) (0.6.43)
Requirement already satisfied: llama-cloud-services>=0.6.43 in d:\erdb_ai_cursor\venv_new\lib\site-packages (from llama-parse>=0.5.0->llama-index-readers-llama-parse>=0.4.0->llama-index) (0.6.54)
Requirement already satisfied: click<9,>=8.1.7 in d:\erdb_ai_cursor\venv_new\lib\site-packages (from llama-cloud-services>=0.6.43->llama-parse>=0.5.0->llama-index-readers-llama-parse>=0.4.0->llama-index) (8.1.8)
Requirement already satisfied: python-dotenv<2,>=1.0.1 in d:\erdb_ai_cursor\venv_new\lib\site-packages (from llama-cloud-services>=0.6.43->llama-parse>=0.5.0->llama-index-readers-llama-parse>=0.4.0->llama-index) (1.1.0)
Requirement already satisfied: joblib in d:\erdb_ai_cursor\venv_new\lib\site-packages (from nltk>3.8.1->llama-index) (1.5.1)
Requirement already satisfied: regex>=2021.8.3 in d:\erdb_ai_cursor\venv_new\lib\site-packages (from nltk>3.8.1->llama-index) (2024.11.6)
Requirement already satisfied: six>=1.5 in d:\erdb_ai_cursor\venv_new\lib\site-packages (from python-dateutil>=2.8.2->pandas>=0.25.3->tabula-py==2.7.0) (1.17.0)
Requirement already satisfied: charset-normalizer<4,>=2 in d:\erdb_ai_cursor\venv_new\lib\site-packages (from requests>=2.31.0->llama-index-core) (3.4.2)
Requirement already satisfied: urllib3<3,>=1.21.1 in d:\erdb_ai_cursor\venv_new\lib\site-packages (from requests>=2.31.0->llama-index-core) (2.4.0)
Requirement already satisfied: greenlet>=1 in d:\erdb_ai_cursor\venv_new\lib\site-packages (from sqlalchemy>=1.4.49->sqlalchemy[asyncio]>=1.4.49->llama-index-core) (3.2.3)
Requirement already satisfied: mypy-extensions>=0.3.0 in d:\erdb_ai_cursor\venv_new\lib\site-packages (from typing-inspect>=0.8.0->llama-index-core) (1.1.0)
Requirement already satisfied: marshmallow<4.0.0,>=3.18.0 in d:\erdb_ai_cursor\venv_new\lib\site-packages (from dataclasses-json->llama-index-core) (3.26.1)
Requirement already satisfied: packaging>=17.0 in d:\erdb_ai_cursor\venv_new\lib\site-packages (from marshmallow<4.0.0,>=3.18.0->dataclasses-json->llama-index-core) (24.2)
Requirement already satisfied: MarkupSafe>=2.0 in d:\erdb_ai_cursor\venv_new\lib\site-packages (from jinja2->banks<3,>=2.2.0->llama-index-core) (3.0.2)
Downloading tabula_py-2.7.0-py3-none-any.whl (12.0 MB)
   ---------------------------------------- 12.0/12.0 MB 3.9 MB/s eta 0:00:00
Downloading llama_index_embeddings_ollama-0.7.0-py3-none-any.whl (3.4 kB)
Building wheels for collected packages: python-docx
  Building wheel for python-docx (setup.py): started
  Building wheel for python-docx (setup.py): finished with status 'done'
  Created wheel for python-docx: filename=python_docx-0.8.11-py3-none-any.whl size=184608 sha256=5df5dbd9b57aa5d2b531245656207b111b1237e19e3ba5ed03908cfc1cd95fba
  Stored in directory: c:\users\<USER>\appdata\local\pip\cache\wheels\28\d3\8e\c09119833d575c0c97e00be8df90d7a668e3df21eedcebddc0
Successfully built python-docx
Installing collected packages: python-docx, tabula-py, llama-index-embeddings-ollama
  Attempting uninstall: tabula-py
    Found existing installation: tabula-py 2.10.0
    Uninstalling tabula-py-2.10.0:
      Successfully uninstalled tabula-py-2.10.0
  Attempting uninstall: llama-index-embeddings-ollama
    Found existing installation: llama-index-embeddings-ollama 0.6.0
    Uninstalling llama-index-embeddings-ollama-0.6.0:
      Successfully uninstalled llama-index-embeddings-ollama-0.6.0

Successfully installed llama-index-embeddings-ollama-0.7.0 python-docx-0.8.11 tabula-py-2.7.0

"""
Enhanced RAG Service with Multimodal Support
Integrates multimodal capabilities with the existing RAG pipeline
"""

import logging
from typing import Dict, Any, List, Optional, Union
from langchain.schema import Document

from config.multimodal_config import get_multimodal_config
from config.rag_optimizations import get_rag_config
from app.services.multimodal_document_processor import get_multimodal_document_processor
from app.services.multimodal_chunking_service import get_multimodal_chunking_service
from app.services.multimodal_embedding_service import get_multimodal_embedding_service
from app.services.hybrid_search_service import get_hybrid_search_service
from app.services.context_assembler import get_context_assembler
from app.services.query_service import query_category
from app.utils.multimodal_performance_monitor import multimodal_performance_monitor

logger = logging.getLogger(__name__)

class EnhancedRAGService:
    """Enhanced RAG service with multimodal support"""
    
    def __init__(self, multimodal_config=None, rag_config=None):
        self.multimodal_config = multimodal_config or get_multimodal_config()
        self.rag_config = rag_config or get_rag_config()
        
        # Initialize multimodal services
        self.document_processor = get_multimodal_document_processor()
        self.chunking_service = get_multimodal_chunking_service()
        self.embedding_service = get_multimodal_embedding_service()
        self.search_service = get_hybrid_search_service()
        self.context_assembler = get_context_assembler()
        
        logger.info("Enhanced RAG service initialized with multimodal support")
    
    @multimodal_performance_monitor("enhanced_document_processing")
    def process_document(self, document_path: str, category: Optional[str] = None,
                        enable_multimodal: Optional[bool] = None) -> Dict[str, Any]:
        """
        Process a document with optional multimodal extraction
        
        Args:
            document_path: Path to the document
            category: Category for organizing content
            enable_multimodal: Override multimodal setting for this document
            
        Returns:
            Processing results with multimodal content
        """
        try:
            # Determine if multimodal processing should be used
            use_multimodal = (
                enable_multimodal if enable_multimodal is not None 
                else self.multimodal_config.enable_multimodal
            )
            
            if use_multimodal:
                logger.info(f"Processing document with multimodal extraction: {document_path}")
                
                # Use multimodal document processor
                result = self.document_processor.process_document(
                    document_path, category
                )
                
                if result:
                    # Create multimodal chunks
                    documents = self.chunking_service.chunk_multimodal_document(result)
                    
                    # Generate embeddings and store
                    if documents:
                        embedding_result = self.embedding_service.embed_multimodal_documents(
                            documents, category
                        )
                        result["embedding_result"] = embedding_result
                    
                    result["processing_mode"] = "multimodal"
                    return result
                else:
                    logger.warning("Multimodal processing failed, falling back to text-only")
            
            # Fallback to existing text-only processing
            logger.info(f"Processing document with text-only extraction: {document_path}")
            return self._process_text_only(document_path, category)
            
        except Exception as e:
            logger.error(f"Error in enhanced document processing: {e}")
            return self._process_text_only(document_path, category)
    
    def _process_text_only(self, document_path: str, category: Optional[str]) -> Dict[str, Any]:
        """Fallback to existing text-only processing"""
        try:
            # Use existing PDF processing
            from app.services.pdf_processor import process_pdf, pdf_to_documents
            
            # Process PDF with existing pipeline
            pdf_result = process_pdf(
                document_path,
                category=category,
                extract_images=False,
                extract_tables=False,
                use_vision=False
            )
            
            # Convert to documents and add to vector database
            documents = pdf_to_documents(document_path, category)
            
            return {
                "document_path": document_path,
                "category": category,
                "text_content": pdf_result.get("text", []),
                "images": [],
                "tables": [],
                "multimodal_chunks": [],
                "processing_mode": "text_only",
                "documents_created": len(documents)
            }
            
        except Exception as e:
            logger.error(f"Error in text-only processing fallback: {e}")
            return {
                "document_path": document_path,
                "category": category,
                "error": str(e),
                "processing_mode": "failed"
            }
    
    @multimodal_performance_monitor("enhanced_question_answering")
    def answer_question(self, question: str, category: Optional[str] = None,
                       enable_multimodal: Optional[bool] = None,
                       k: Optional[int] = None) -> Dict[str, Any]:
        """
        Answer a question using enhanced RAG with optional multimodal search
        
        Args:
            question: The question to answer
            category: Category to search within
            enable_multimodal: Override multimodal setting for this query
            k: Number of results to retrieve
            
        Returns:
            Answer with multimodal context
        """
        try:
            # Determine if multimodal search should be used
            use_multimodal = (
                enable_multimodal if enable_multimodal is not None 
                else self.multimodal_config.search.enable_hybrid_search
            )
            
            # Set default k value
            if k is None:
                k = self.rag_config.retrieval_k
            
            if use_multimodal:
                logger.info(f"Answering question with multimodal search: {question[:50]}...")
                return self._answer_with_multimodal_search(question, category, k)
            else:
                logger.info(f"Answering question with text-only search: {question[:50]}...")
                return self._answer_with_text_search(question, category, k)
                
        except Exception as e:
            logger.error(f"Error in enhanced question answering: {e}")
            return {
                "question": question,
                "answer": "I apologize, but I encountered an error while processing your question.",
                "error": str(e),
                "search_mode": "failed"
            }
    
    def _answer_with_multimodal_search(self, question: str, category: Optional[str], k: int) -> Dict[str, Any]:
        """Answer question using multimodal search"""
        try:
            # Perform hybrid search
            search_results = self.search_service.search_multimodal(
                query=question,
                category=category,
                k=k
            )
            
            if "error" in search_results:
                logger.warning(f"Multimodal search failed: {search_results['error']}")
                return self._answer_with_text_search(question, category, k)
            
            # Assemble multimodal context
            context = self.context_assembler.assemble_multimodal_context(
                search_results, question
            )
            
            # Format context for LLM
            formatted_context = self.context_assembler.format_context_for_llm(context)
            
            # Generate answer using existing RAG service with enhanced context
            # Note: This would integrate with your existing LLM service
            answer = self._generate_answer_with_context(question, formatted_context)
            
            return {
                "question": question,
                "answer": answer,
                "search_results": search_results,
                "multimodal_context": context,
                "search_mode": "multimodal",
                "context_metadata": context.get("context_metadata", {})
            }
            
        except Exception as e:
            logger.error(f"Error in multimodal search: {e}")
            return self._answer_with_text_search(question, category, k)
    
    def _answer_with_text_search(self, question: str, category: Optional[str], k: int) -> Dict[str, Any]:
        """Answer question using existing text-only search"""
        try:
            # Use existing RAG service
            result = query_category(
                category=category,
                question=question,
                anti_hallucination_mode='strict'
            )
            
            # Add metadata to indicate text-only mode
            result["search_mode"] = "text_only"
            result["multimodal_context"] = None
            
            return result
            
        except Exception as e:
            logger.error(f"Error in text-only search: {e}")
            return {
                "question": question,
                "answer": "I apologize, but I couldn't find relevant information to answer your question.",
                "error": str(e),
                "search_mode": "text_only_failed"
            }
    
    def _generate_answer_with_context(self, question: str, context: str) -> str:
        """Generate answer using LLM with provided context"""
        # This is a placeholder for LLM integration
        # In a real implementation, this would call your LLM service
        # with the enhanced multimodal context
        
        if not context.strip():
            return "I couldn't find relevant information to answer your question."
        
        # For now, return a formatted response indicating multimodal content
        lines = context.split('\n')
        has_images = any('Image Descriptions:' in line for line in lines)
        has_tables = any('Table Content:' in line for line in lines)
        
        response_parts = ["Based on the available information:"]
        
        if has_images and has_tables:
            response_parts.append("(This response includes information from text, images, and tables)")
        elif has_images:
            response_parts.append("(This response includes information from text and images)")
        elif has_tables:
            response_parts.append("(This response includes information from text and tables)")
        
        response_parts.append(f"\n{context[:500]}...")
        
        return "\n".join(response_parts)
    
    def get_processing_statistics(self) -> Dict[str, Any]:
        """Get processing statistics for the enhanced RAG service"""
        try:
            from app.utils.multimodal_performance_monitor import get_multimodal_performance_monitor
            
            monitor = get_multimodal_performance_monitor()
            
            # Get statistics for different operation types
            doc_processing_stats = monitor.get_performance_summary("enhanced_document_processing")
            qa_stats = monitor.get_performance_summary("enhanced_question_answering")
            
            return {
                "document_processing": doc_processing_stats,
                "question_answering": qa_stats,
                "multimodal_enabled": self.multimodal_config.enable_multimodal,
                "hybrid_search_enabled": self.multimodal_config.search.enable_hybrid_search
            }
            
        except Exception as e:
            logger.error(f"Error getting processing statistics: {e}")
            return {"error": str(e)}
    
    def configure_multimodal_features(self, **kwargs):
        """Configure multimodal features at runtime"""
        try:
            for key, value in kwargs.items():
                if hasattr(self.multimodal_config, key):
                    setattr(self.multimodal_config, key, value)
                    logger.info(f"Updated multimodal config: {key} = {value}")
                else:
                    logger.warning(f"Unknown multimodal config key: {key}")
            
            return {"status": "success", "updated_keys": list(kwargs.keys())}
            
        except Exception as e:
            logger.error(f"Error configuring multimodal features: {e}")
            return {"status": "error", "error": str(e)}

# Global enhanced RAG service instance
_enhanced_rag_service = None

def get_enhanced_rag_service() -> EnhancedRAGService:
    """Get the global enhanced RAG service instance"""
    global _enhanced_rag_service
    if _enhanced_rag_service is None:
        _enhanced_rag_service = EnhancedRAGService()
    return _enhanced_rag_service

# Convenience functions for backward compatibility
def process_document_enhanced(document_path: str, category: Optional[str] = None, **kwargs) -> Dict[str, Any]:
    """Process document with enhanced multimodal capabilities"""
    service = get_enhanced_rag_service()
    return service.process_document(document_path, category, **kwargs)

def answer_question_enhanced(question: str, category: Optional[str] = None, **kwargs) -> Dict[str, Any]:
    """Answer question with enhanced multimodal search"""
    service = get_enhanced_rag_service()
    return service.answer_question(question, category, **kwargs)

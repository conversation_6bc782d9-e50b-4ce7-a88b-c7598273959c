"""
Test LangChain Integration

Comprehensive tests to validate that both frameworks can process the same documents,
verify caching works across frameworks, and ensure performance monitoring captures
both usages.
"""

import os
import sys
import unittest
import tempfile
import logging
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from app.services.langchain_integration.multimodal_document_loader import MultimodalDocumentLoader
from app.services.langchain_integration.ollama_embeddings_wrapper import get_default_langchain_embeddings
from app.services.langchain_integration.multimodal_text_splitter import get_multimodal_text_splitter
from app.services.langchain_integration.chroma_vectorstore_wrapper import get_chroma_vectorstore
from app.services.langchain_integration.adaptive_retriever import get_adaptive_retriever
from app.services.unified_rag_service import get_unified_rag_service, RAGFramework

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TestLangChainIntegration(unittest.TestCase):
    """Test LangChain integration components."""
    
    @classmethod
    def setUpClass(cls):
        """Set up test class."""
        cls.test_category = "TEST_CATEGORY"
        cls.test_text = "This is a test document with some content for testing purposes."
        
        # Create a simple test PDF path (mock)
        cls.test_pdf_path = "test_document.pdf"
        
    def setUp(self):
        """Set up each test."""
        self.embeddings = None
        self.vectorstore = None
        self.retriever = None
    
    def test_ollama_embeddings_wrapper(self):
        """Test OllamaEmbeddingsWrapper functionality."""
        print("\n🔤 Testing OllamaEmbeddingsWrapper...")
        
        try:
            # Initialize embeddings
            embeddings = get_default_langchain_embeddings()
            self.assertIsNotNone(embeddings)
            
            # Test model info
            model_info = embeddings.get_model_info()
            self.assertIsInstance(model_info, dict)
            self.assertIn("model", model_info)
            self.assertIn("provider", model_info)
            
            print(f"✅ Model info: {model_info}")
            
            # Test single embedding
            test_query = "test query"
            query_embedding = embeddings.embed_query(test_query)
            self.assertIsInstance(query_embedding, list)
            self.assertGreater(len(query_embedding), 0)
            self.assertTrue(all(isinstance(x, float) for x in query_embedding))
            
            print(f"✅ Query embedding dimension: {len(query_embedding)}")
            
            # Test document embeddings
            test_docs = ["document 1", "document 2", "document 3"]
            doc_embeddings = embeddings.embed_documents(test_docs)
            self.assertIsInstance(doc_embeddings, list)
            self.assertEqual(len(doc_embeddings), len(test_docs))
            
            for embedding in doc_embeddings:
                self.assertIsInstance(embedding, list)
                self.assertEqual(len(embedding), len(query_embedding))
            
            print(f"✅ Document embeddings: {len(doc_embeddings)} embeddings generated")
            
            # Test cache functionality
            cache_stats_before = embeddings.get_cache_stats()
            
            # Repeat same query (should hit cache)
            query_embedding_2 = embeddings.embed_query(test_query)
            self.assertEqual(query_embedding, query_embedding_2)
            
            cache_stats_after = embeddings.get_cache_stats()
            print(f"✅ Cache stats: {cache_stats_after}")
            
            self.embeddings = embeddings
            
        except Exception as e:
            self.skipTest(f"Ollama embeddings test skipped: {e}")
    
    def test_multimodal_text_splitter(self):
        """Test MultimodalTextSplitter functionality."""
        print("\n✂️ Testing MultimodalTextSplitter...")
        
        # Create test documents
        from langchain_core.documents import Document
        
        test_documents = [
            Document(
                page_content="This is a long document that should be split into multiple chunks. " * 20,
                metadata={"source": "test1.pdf", "page": 1}
            ),
            Document(
                page_content="Another document with different content that also needs splitting. " * 15,
                metadata={"source": "test2.pdf", "page": 1}
            )
        ]
        
        # Test different chunking strategies
        strategies = ["adaptive", "semantic", "sentence", "fixed"]
        
        for strategy in strategies:
            print(f"   Testing {strategy} strategy...")
            
            try:
                splitter = get_multimodal_text_splitter(
                    chunk_size=200,
                    chunk_overlap=50,
                    chunking_strategy=strategy,
                    preserve_multimodal_relationships=True
                )
                
                # Test split_documents
                chunks = splitter.split_documents(test_documents)
                self.assertIsInstance(chunks, list)
                self.assertGreater(len(chunks), len(test_documents))
                
                # Verify chunk metadata
                for chunk in chunks:
                    self.assertIsInstance(chunk, Document)
                    self.assertIn("chunking_strategy", chunk.metadata)
                    self.assertIn("splitter_type", chunk.metadata)
                
                print(f"      ✅ {strategy}: {len(chunks)} chunks created")
                
                # Test split_text
                text_chunks = splitter.split_text(test_documents[0].page_content)
                self.assertIsInstance(text_chunks, list)
                self.assertGreater(len(text_chunks), 0)
                
                # Get splitter info
                splitter_info = splitter.get_splitter_info()
                self.assertIsInstance(splitter_info, dict)
                self.assertEqual(splitter_info["chunking_strategy"], strategy)
                
            except Exception as e:
                print(f"      ⚠️ {strategy} strategy failed: {e}")
    
    def test_chroma_vectorstore_wrapper(self):
        """Test ChromaVectorStoreWrapper functionality."""
        print("\n🗄️ Testing ChromaVectorStoreWrapper...")
        
        if not self.embeddings:
            self.test_ollama_embeddings_wrapper()
        
        try:
            # Create vector store
            vectorstore = get_chroma_vectorstore(
                category=self.test_category,
                embeddings=self.embeddings
            )
            self.assertIsNotNone(vectorstore)
            
            # Test add_texts
            test_texts = [
                "This is the first test document about machine learning.",
                "This is the second test document about artificial intelligence.",
                "This is the third test document about data science."
            ]
            
            doc_ids = vectorstore.add_texts(
                texts=test_texts,
                category=self.test_category
            )
            self.assertIsInstance(doc_ids, list)
            self.assertEqual(len(doc_ids), len(test_texts))
            
            print(f"✅ Added {len(test_texts)} texts to vector store")
            
            # Test similarity search
            query = "machine learning"
            results = vectorstore.similarity_search(
                query=query,
                k=2,
                category=self.test_category
            )
            self.assertIsInstance(results, list)
            self.assertLessEqual(len(results), 2)
            
            print(f"✅ Similarity search returned {len(results)} results")
            
            # Test similarity search with scores
            results_with_scores = vectorstore.similarity_search_with_score(
                query=query,
                k=2,
                category=self.test_category
            )
            self.assertIsInstance(results_with_scores, list)
            
            for doc, score in results_with_scores:
                self.assertIsInstance(score, float)
                print(f"   📄 Result score: {score:.3f}")
            
            # Test collection stats
            stats = vectorstore.get_collection_stats(self.test_category)
            self.assertIsInstance(stats, dict)
            print(f"✅ Collection stats: {stats}")
            
            self.vectorstore = vectorstore
            
        except Exception as e:
            self.skipTest(f"ChromaDB vector store test skipped: {e}")
    
    def test_adaptive_retriever(self):
        """Test AdaptiveRetriever functionality."""
        print("\n🔍 Testing AdaptiveRetriever...")
        
        if not self.vectorstore:
            self.test_chroma_vectorstore_wrapper()
        
        try:
            # Create adaptive retriever
            retriever = get_adaptive_retriever(
                vectorstore=self.vectorstore,
                base_k=6,
                category=self.test_category,
                enable_multimodal=True
            )
            self.assertIsNotNone(retriever)
            
            # Test retriever info
            retriever_info = retriever.get_retriever_info()
            self.assertIsInstance(retriever_info, dict)
            self.assertEqual(retriever_info["retriever_type"], "adaptive")
            
            print(f"✅ Retriever info: {retriever_info}")
            
            # Test different query complexities
            test_queries = [
                ("AI", "simple"),  # Simple query (≤3 words)
                ("What is machine learning?", "medium"),  # Medium query (4-10 words)
                ("Can you explain the differences between machine learning and artificial intelligence in detail?", "complex")  # Complex query (>10 words)
            ]
            
            for query, expected_complexity in test_queries:
                print(f"   Testing {expected_complexity} query: {query}")
                
                results = retriever.get_relevant_documents(query)
                self.assertIsInstance(results, list)
                
                # Check metadata for adaptive k
                if results:
                    first_result = results[0]
                    self.assertIn("retrieval_k", first_result.metadata)
                    self.assertIn("query_complexity", first_result.metadata)
                    
                    actual_complexity = first_result.metadata["query_complexity"]
                    retrieval_k = first_result.metadata["retrieval_k"]
                    
                    print(f"      ✅ Complexity: {actual_complexity}, k: {retrieval_k}")
                    self.assertEqual(actual_complexity, expected_complexity)
            
            self.retriever = retriever
            
        except Exception as e:
            self.skipTest(f"Adaptive retriever test skipped: {e}")
    
    def test_unified_rag_service(self):
        """Test UnifiedRAGService functionality."""
        print("\n🔄 Testing UnifiedRAGService...")
        
        try:
            # Initialize unified service
            service = get_unified_rag_service(
                default_framework=RAGFramework.BOTH,
                category=self.test_category
            )
            self.assertIsNotNone(service)
            
            # Test service info
            service_info = service.get_service_info()
            self.assertIsInstance(service_info, dict)
            self.assertEqual(service_info["service_type"], "unified_rag")
            
            print(f"✅ Service info: {service_info}")
            
            # Test query with different frameworks
            test_query = "What is artificial intelligence?"
            
            frameworks_to_test = [RAGFramework.LANGCHAIN]
            if service_info.get("llamaindex_available", False):
                frameworks_to_test.append(RAGFramework.LLAMAINDEX)
            
            for framework in frameworks_to_test:
                print(f"   Testing query with {framework.value}...")
                
                try:
                    result = service.query(
                        question=test_query,
                        category=self.test_category,
                        framework=framework,
                        k=3
                    )
                    
                    self.assertIsInstance(result, dict)
                    self.assertEqual(result["question"], test_query)
                    self.assertEqual(result["framework"], framework.value)
                    
                    if framework == RAGFramework.LANGCHAIN:
                        langchain_result = result.get("langchain_result")
                        if langchain_result and "error" not in langchain_result:
                            print(f"      ✅ LangChain: {langchain_result.get('documents_retrieved', 0)} docs")
                    
                    if framework == RAGFramework.LLAMAINDEX:
                        llamaindex_result = result.get("llamaindex_result")
                        if llamaindex_result and "error" not in llamaindex_result:
                            print(f"      ✅ LlamaIndex: Processing completed")
                
                except Exception as e:
                    print(f"      ⚠️ {framework.value} query failed: {e}")
            
            # Test cache sharing
            print("   Testing cache sharing...")
            cache_stats = service.get_cache_stats()
            self.assertIsInstance(cache_stats, dict)
            print(f"   ✅ Cache stats: {cache_stats}")
            
        except Exception as e:
            self.skipTest(f"Unified RAG service test skipped: {e}")
    
    def test_performance_monitoring(self):
        """Test performance monitoring integration."""
        print("\n📊 Testing Performance Monitoring...")
        
        try:
            from app.utils.performance_monitor import get_performance_monitor
            
            monitor = get_performance_monitor()
            if monitor:
                # Get recent metrics
                recent_metrics = monitor.get_recent_metrics(hours=1)
                print(f"✅ Recent metrics: {len(recent_metrics)} entries")
                
                # Get function stats
                function_stats = monitor.get_function_stats()
                print(f"✅ Function stats: {len(function_stats)} functions monitored")
                
                # Display some stats
                for func_name, stats in list(function_stats.items())[:3]:
                    print(f"   📈 {func_name}: {stats}")
            else:
                print("⚠️ Performance monitor not available")
                
        except Exception as e:
            print(f"⚠️ Performance monitoring test failed: {e}")
    
    def test_framework_compatibility(self):
        """Test that both frameworks can process the same content."""
        print("\n🔄 Testing Framework Compatibility...")
        
        if not self.embeddings or not self.vectorstore:
            self.test_chroma_vectorstore_wrapper()
        
        try:
            # Test same query with both approaches
            test_query = "machine learning algorithms"
            
            # LangChain approach
            langchain_results = self.vectorstore.similarity_search(
                query=test_query,
                k=3,
                category=self.test_category
            )
            
            print(f"✅ LangChain results: {len(langchain_results)} documents")
            
            # Test that results contain expected metadata
            for result in langchain_results:
                self.assertIsInstance(result.metadata, dict)
                self.assertIn("category", result.metadata)
            
            # Test cache sharing by checking cache stats
            cache_stats_before = self.embeddings.get_cache_stats()
            
            # Repeat query (should hit cache)
            langchain_results_2 = self.vectorstore.similarity_search(
                query=test_query,
                k=3,
                category=self.test_category
            )
            
            cache_stats_after = self.embeddings.get_cache_stats()
            
            print(f"✅ Cache sharing test completed")
            print(f"   Before: {cache_stats_before}")
            print(f"   After: {cache_stats_after}")
            
        except Exception as e:
            print(f"⚠️ Framework compatibility test failed: {e}")


def run_integration_tests():
    """Run all integration tests."""
    print("🧪 Running LangChain Integration Tests")
    print("=" * 60)
    
    # Create test suite
    suite = unittest.TestLoader().loadTestsFromTestCase(TestLangChainIntegration)
    
    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # Print summary
    print("\n" + "=" * 60)
    print("🎯 Test Summary")
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    print(f"Skipped: {len(result.skipped)}")
    
    if result.failures:
        print("\n❌ Failures:")
        for test, traceback in result.failures:
            print(f"   - {test}: {traceback}")
    
    if result.errors:
        print("\n💥 Errors:")
        for test, traceback in result.errors:
            print(f"   - {test}: {traceback}")
    
    if result.skipped:
        print("\n⏭️ Skipped:")
        for test, reason in result.skipped:
            print(f"   - {test}: {reason}")
    
    success = len(result.failures) == 0 and len(result.errors) == 0
    print(f"\n{'✅ All tests passed!' if success else '❌ Some tests failed!'}")
    
    return success


if __name__ == "__main__":
    run_integration_tests()

/**
 * Multimodal Dashboard JavaScript
 * Handles status monitoring, metrics display, and pipeline testing
 */

class MultimodalDashboard {
    constructor() {
        this.refreshInterval = null;
        this.autoRefreshEnabled = true;
        this.autoRefreshDelay = 30000; // 30 seconds
        
        this.init();
    }
    
    init() {
        this.setupEventListeners();
        this.loadInitialData();
        this.startAutoRefresh();
    }
    
    setupEventListeners() {
        // Refresh status button
        document.getElementById('refresh-status').addEventListener('click', () => {
            this.loadStatus(true);
        });
        
        // Reset metrics button
        document.getElementById('reset-metrics').addEventListener('click', () => {
            this.resetMetrics();
        });
        
        // File upload handling
        document.getElementById('test-file').addEventListener('change', (e) => {
            const runTestBtn = document.getElementById('run-test');
            runTestBtn.disabled = !e.target.files.length;
        });
        
        // Run test button
        document.getElementById('run-test').addEventListener('click', () => {
            this.runPipelineTest();
        });
    }
    
    async loadInitialData() {
        await Promise.all([
            this.loadHealth(),
            this.loadStatus(),
            this.loadMetrics()
        ]);
    }
    
    async loadHealth() {
        try {
            const response = await fetch('/api/multimodal/health');
            const data = await response.json();
            
            if (data.success) {
                this.updateHealthDisplay(data.health);
            } else {
                console.error('Failed to load health data:', data.error);
            }
        } catch (error) {
            console.error('Error loading health data:', error);
        }
    }
    
    async loadStatus(forceRefresh = false) {
        try {
            const url = forceRefresh ? '/api/multimodal/status?refresh=true' : '/api/multimodal/status';
            const response = await fetch(url);
            const data = await response.json();
            
            if (data.success) {
                this.updateStatusDisplay(data.status);
            } else {
                console.error('Failed to load status data:', data.error);
            }
        } catch (error) {
            console.error('Error loading status data:', error);
        }
    }
    
    async loadMetrics() {
        try {
            const response = await fetch('/api/multimodal/metrics');
            const data = await response.json();
            
            if (data.success) {
                this.updateMetricsDisplay(data.metrics);
            } else {
                console.error('Failed to load metrics data:', data.error);
            }
        } catch (error) {
            console.error('Error loading metrics data:', error);
        }
    }
    
    updateHealthDisplay(health) {
        const healthStatus = document.getElementById('health-status');
        const healthIcon = document.getElementById('health-icon');
        const healthBar = document.getElementById('health-bar');
        
        // Update status text
        healthStatus.textContent = health.health_status.charAt(0).toUpperCase() + health.health_status.slice(1);
        
        // Update icon and colors
        healthIcon.innerHTML = '';
        healthBar.style.width = health.health_percentage + '%';
        
        if (health.health_status === 'healthy') {
            healthIcon.innerHTML = '<i class="fas fa-check-circle text-green-500"></i>';
            healthBar.className = 'bg-green-500 h-2 rounded-full transition-all duration-300';
            healthStatus.className = 'text-2xl font-bold text-green-600';
        } else if (health.health_status === 'warning') {
            healthIcon.innerHTML = '<i class="fas fa-exclamation-triangle text-yellow-500"></i>';
            healthBar.className = 'bg-yellow-500 h-2 rounded-full transition-all duration-300';
            healthStatus.className = 'text-2xl font-bold text-yellow-600';
        } else {
            healthIcon.innerHTML = '<i class="fas fa-times-circle text-red-500"></i>';
            healthBar.className = 'bg-red-500 h-2 rounded-full transition-all duration-300';
            healthStatus.className = 'text-2xl font-bold text-red-600';
        }
        
        // Update recommendations
        this.updateRecommendations(health.recommendations);
    }
    
    updateStatusDisplay(status) {
        // Update processing status
        const processingStatus = document.getElementById('processing-status');
        const processingIcon = document.getElementById('processing-icon');
        
        if (status.processing_active) {
            processingStatus.textContent = 'Active';
            processingIcon.className = 'fas fa-play text-green-500';
        } else {
            processingStatus.textContent = 'Idle';
            processingIcon.className = 'fas fa-pause text-gray-400';
        }
        
        // Update vision model status
        const visionModelName = document.getElementById('vision-model-name');
        const visionModelStatus = document.getElementById('vision-model-status');
        const visionModelIcon = document.getElementById('vision-model-icon');
        
        visionModelName.textContent = status.vision_model_name || 'Not configured';
        visionModelStatus.textContent = status.vision_model_status || 'Unknown';
        
        if (status.vision_model_available) {
            visionModelIcon.className = 'fas fa-eye text-green-500';
        } else {
            visionModelIcon.className = 'fas fa-eye text-red-500';
        }
        
        // Update storage status
        const storageStatus = document.getElementById('storage-status');
        const storageIcon = document.getElementById('storage-icon');
        
        if (status.storage_accessible) {
            storageStatus.textContent = 'Accessible';
            storageIcon.className = 'fas fa-database text-green-500';
        } else {
            storageStatus.textContent = 'Error';
            storageIcon.className = 'fas fa-database text-red-500';
        }
        
        // Update detailed status items
        this.updateStatusItems(status);
    }
    
    updateStatusItems(status) {
        const container = document.getElementById('status-items');
        container.innerHTML = '';
        
        const items = [
            {
                label: 'Multimodal Processing',
                value: status.enabled ? 'Enabled' : 'Disabled',
                status: status.enabled ? 'success' : 'error'
            },
            {
                label: 'Vision Model',
                value: status.vision_model_available ? 'Available' : 'Unavailable',
                status: status.vision_model_available ? 'success' : 'error'
            },
            {
                label: 'Storage',
                value: status.storage_accessible ? 'Accessible' : 'Error',
                status: status.storage_accessible ? 'success' : 'error'
            },
            {
                label: 'Dependencies',
                value: status.dependencies_available ? 'Available' : 'Missing',
                status: status.dependencies_available ? 'success' : 'error'
            }
        ];
        
        items.forEach(item => {
            const div = document.createElement('div');
            div.className = 'flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg';
            
            const statusColor = item.status === 'success' ? 'text-green-600' : 'text-red-600';
            const statusIcon = item.status === 'success' ? 'fa-check' : 'fa-times';
            
            div.innerHTML = `
                <span class="text-gray-900 dark:text-white">${item.label}</span>
                <span class="flex items-center ${statusColor}">
                    <i class="fas ${statusIcon} mr-2"></i>
                    ${item.value}
                </span>
            `;
            
            container.appendChild(div);
        });
        
        // Add errors if any
        if (status.errors && status.errors.length > 0) {
            const errorsDiv = document.createElement('div');
            errorsDiv.className = 'mt-4 p-3 bg-red-50 dark:bg-red-900 rounded-lg';
            errorsDiv.innerHTML = `
                <h4 class="font-semibold text-red-800 dark:text-red-200 mb-2">Errors:</h4>
                <ul class="text-sm text-red-700 dark:text-red-300 space-y-1">
                    ${status.errors.map(error => `<li>• ${error}</li>`).join('')}
                </ul>
            `;
            container.appendChild(errorsDiv);
        }
    }
    
    updateMetricsDisplay(metrics) {
        document.getElementById('total-documents').textContent = metrics.total_documents || 0;
        document.getElementById('success-rate').textContent = (metrics.success_rate || 0).toFixed(1) + '%';
        document.getElementById('total-text-chunks').textContent = metrics.total_text_chunks || 0;
        document.getElementById('total-images').textContent = metrics.total_images_extracted || 0;
        document.getElementById('total-tables').textContent = metrics.total_tables_extracted || 0;
        document.getElementById('avg-processing-time').textContent = (metrics.average_processing_time || 0).toFixed(2) + 's';
    }
    
    updateRecommendations(recommendations) {
        const container = document.getElementById('recommendations');
        container.innerHTML = '';
        
        if (!recommendations || recommendations.length === 0) {
            container.innerHTML = '<p class="text-gray-500 dark:text-gray-400">No recommendations at this time.</p>';
            return;
        }
        
        recommendations.forEach(recommendation => {
            const div = document.createElement('div');
            div.className = 'flex items-start space-x-3 p-3 bg-blue-50 dark:bg-blue-900 rounded-lg';
            div.innerHTML = `
                <i class="fas fa-info-circle text-blue-600 mt-1"></i>
                <p class="text-blue-800 dark:text-blue-200">${recommendation}</p>
            `;
            container.appendChild(div);
        });
    }
    
    async resetMetrics() {
        if (!confirm('Are you sure you want to reset all processing metrics?')) {
            return;
        }
        
        try {
            // Get CSRF token
            let csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || 
                           document.querySelector('input[name="csrf_token"]')?.value;
            
            // If no CSRF token found, try to refresh it
            if (!csrfToken && window.utilities && window.utilities.refreshCSRFToken) {
                const refreshed = await window.utilities.refreshCSRFToken();
                if (refreshed) {
                    csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || 
                               document.querySelector('input[name="csrf_token"]')?.value;
                }
            }
            
            // If still no CSRF token, show error
            if (!csrfToken) {
                throw new Error('CSRF token not available. Please refresh the page and try again.');
            }
            
            const response = await fetch('/api/multimodal/metrics/reset', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': csrfToken
                }
            });
            
            // Handle CSRF token errors specifically
            if (response.status === 400) {
                const errorData = await response.json();
                if (errorData.error && errorData.error.includes('CSRF')) {
                    // Try to refresh CSRF token and retry once
                    if (window.utilities && window.utilities.refreshCSRFToken) {
                        const refreshed = await window.utilities.refreshCSRFToken();
                        if (refreshed) {
                            const newCsrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || 
                                               document.querySelector('input[name="csrf_token"]')?.value;
                            
                            const retryResponse = await fetch('/api/multimodal/metrics/reset', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                    'X-CSRFToken': newCsrfToken
                                }
                            });
                            
                            if (retryResponse.ok) {
                                const data = await retryResponse.json();
                                if (data.success) {
                                    this.loadMetrics();
                                    this.showNotification('Metrics reset successfully', 'success');
                                    return;
                                } else {
                                    this.showNotification('Failed to reset metrics: ' + data.error, 'error');
                                    return;
                                }
                            }
                        }
                    }
                    throw new Error('CSRF token validation failed. Please refresh the page and try again.');
                }
            }
            
            const data = await response.json();
            
            if (data.success) {
                this.loadMetrics();
                this.showNotification('Metrics reset successfully', 'success');
            } else {
                this.showNotification('Failed to reset metrics: ' + data.error, 'error');
            }
        } catch (error) {
            this.showNotification('Error resetting metrics: ' + error.message, 'error');
        }
    }
    
    async runPipelineTest() {
        const fileInput = document.getElementById('test-file');
        const file = fileInput.files[0];
        
        if (!file) {
            this.showNotification('Please select a file to test', 'error');
            return;
        }
        
        const loadingOverlay = document.getElementById('loading-overlay');
        const resultsContainer = document.getElementById('test-results');
        
        try {
            loadingOverlay.classList.remove('hidden');
            
            // Get CSRF token
            let csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || 
                           document.querySelector('input[name="csrf_token"]')?.value;
            
            // If no CSRF token found, try to refresh it
            if (!csrfToken && window.utilities && window.utilities.refreshCSRFToken) {
                const refreshed = await window.utilities.refreshCSRFToken();
                if (refreshed) {
                    csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || 
                               document.querySelector('input[name="csrf_token"]')?.value;
                }
            }
            
            // If still no CSRF token, show error
            if (!csrfToken) {
                throw new Error('CSRF token not available. Please refresh the page and try again.');
            }
            
            const formData = new FormData();
            formData.append('file', file);
            
            const response = await fetch('/api/multimodal/test', {
                method: 'POST',
                headers: {
                    'X-CSRFToken': csrfToken
                },
                body: formData
            });
            
            // Handle CSRF token errors specifically
            if (response.status === 400) {
                const errorData = await response.json();
                if (errorData.error && errorData.error.includes('CSRF')) {
                    // Try to refresh CSRF token and retry once
                    if (window.utilities && window.utilities.refreshCSRFToken) {
                        const refreshed = await window.utilities.refreshCSRFToken();
                        if (refreshed) {
                            const newCsrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content') || 
                                               document.querySelector('input[name="csrf_token"]')?.value;
                            
                            const retryResponse = await fetch('/api/multimodal/test', {
                                method: 'POST',
                                headers: {
                                    'X-CSRFToken': newCsrfToken
                                },
                                body: formData
                            });
                            
                            if (retryResponse.ok) {
                                const data = await retryResponse.json();
                                if (data.success) {
                                    this.displayTestResults(data);
                                    this.loadMetrics(); // Refresh metrics
                                    this.showNotification('Test completed successfully', 'success');
                                    return;
                                } else {
                                    this.displayTestError(data.error);
                                    this.showNotification('Test failed: ' + data.error, 'error');
                                    return;
                                }
                            }
                        }
                    }
                    throw new Error('CSRF token validation failed. Please refresh the page and try again.');
                }
            }
            
            const data = await response.json();
            
            if (data.success) {
                this.displayTestResults(data);
                this.loadMetrics(); // Refresh metrics
                this.showNotification('Test completed successfully', 'success');
            } else {
                this.displayTestError(data.error);
                this.showNotification('Test failed: ' + data.error, 'error');
            }
        } catch (error) {
            this.displayTestError(error.message);
            this.showNotification('Test error: ' + error.message, 'error');
        } finally {
            loadingOverlay.classList.add('hidden');
        }
    }
    
    displayTestResults(data) {
        const container = document.getElementById('test-results');
        container.innerHTML = `
            <div class="space-y-4">
                <div class="flex items-center justify-between">
                    <h4 class="font-semibold text-gray-900 dark:text-white">Test Results</h4>
                    <span class="text-sm text-gray-500 dark:text-gray-400">${new Date(data.timestamp).toLocaleString()}</span>
                </div>
                
                <div class="grid grid-cols-2 gap-4">
                    <div class="text-center p-3 bg-white dark:bg-gray-600 rounded">
                        <p class="text-lg font-bold text-blue-600">${data.results.images_extracted}</p>
                        <p class="text-sm text-gray-600 dark:text-gray-400">Images</p>
                    </div>
                    <div class="text-center p-3 bg-white dark:bg-gray-600 rounded">
                        <p class="text-lg font-bold text-green-600">${data.results.tables_extracted}</p>
                        <p class="text-sm text-gray-600 dark:text-gray-400">Tables</p>
                    </div>
                </div>
                
                <div class="p-3 bg-white dark:bg-gray-600 rounded">
                    <p class="text-sm text-gray-600 dark:text-gray-400">Processing Time</p>
                    <p class="text-lg font-bold text-gray-900 dark:text-white">${data.processing_time.toFixed(2)}s</p>
                </div>
                
                <div class="p-3 bg-green-50 dark:bg-green-900 rounded">
                    <p class="text-green-800 dark:text-green-200">${data.status}</p>
                </div>
            </div>
        `;
    }
    
    displayTestError(error) {
        const container = document.getElementById('test-results');
        container.innerHTML = `
            <div class="p-4 bg-red-50 dark:bg-red-900 rounded-lg">
                <h4 class="font-semibold text-red-800 dark:text-red-200 mb-2">Test Failed</h4>
                <p class="text-red-700 dark:text-red-300">${error}</p>
            </div>
        `;
    }
    
    showNotification(message, type = 'info') {
        // Simple notification - could be enhanced with a proper notification system
        const color = type === 'success' ? 'green' : type === 'error' ? 'red' : 'blue';
        console.log(`[${type.toUpperCase()}] ${message}`);
        
        // You could implement a toast notification system here
        alert(message);
    }
    
    startAutoRefresh() {
        if (this.autoRefreshEnabled) {
            this.refreshInterval = setInterval(() => {
                this.loadHealth();
                this.loadStatus();
                this.loadMetrics();
            }, this.autoRefreshDelay);
        }
    }
    
    stopAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }
}

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new MultimodalDashboard();
});

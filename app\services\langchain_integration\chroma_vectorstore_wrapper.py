"""
LangChain ChromaVectorStoreWrapper

LangChain-compatible vector store wrapper that integrates with the existing
unified ChromaDB infrastructure, supporting multimodal content storage
and retrieval with category-based filtering.
"""

import logging
import os
from typing import List, Dict, Any, Optional, Tuple, Union

from langchain_core.vectorstores import VectorStore
from langchain_core.documents import Document
from langchain_core.embeddings import Embeddings

from app.services.vector_db import get_vector_db, add_documents_with_category
from app.services.unified_vector_db import get_unified_vector_db
from app.services.langchain_integration.ollama_embeddings_wrapper import get_default_langchain_embeddings
from app.utils.performance_monitor import performance_monitor

logger = logging.getLogger(__name__)


class ChromaVectorStoreWrapper(VectorStore):
    """
    LangChain-compatible wrapper for the existing unified ChromaDB.
    
    This wrapper provides LangChain VectorStore interface while using
    the existing unified ChromaDB infrastructure with category-based
    filtering and multimodal content support.
    """
    
    def __init__(
        self,
        embeddings: Optional[Embeddings] = None,
        category: Optional[str] = None,
        use_unified_db: bool = True,
        **kwargs
    ):
        """
        Initialize the ChromaDB vector store wrapper.
        
        Args:
            embeddings: Embeddings function (defaults to Ollama embeddings)
            category: Default category for operations
            use_unified_db: Whether to use unified DB (recommended)
            **kwargs: Additional arguments
        """
        self._embeddings = embeddings or get_default_langchain_embeddings()
        self.category = category
        self.use_unified_db = use_unified_db
        
        # Initialize the underlying database
        if self.use_unified_db:
            self._db = get_unified_vector_db()
        else:
            self._db = get_vector_db(category or "default")
        
        logger.info(f"Initialized ChromaVectorStoreWrapper with category: {category}")
    
    @property
    def embeddings(self) -> Embeddings:
        """Get the embeddings function."""
        return self._embeddings
    
    @performance_monitor(track_memory=True, track_cpu=True, log_parameters=True, log_result_size=True)
    def add_documents(
        self,
        documents: List[Document],
        category: Optional[str] = None,
        **kwargs
    ) -> List[str]:
        """
        Add documents to the vector store.
        
        Args:
            documents: List of documents to add
            category: Category for the documents (overrides default)
            **kwargs: Additional metadata
            
        Returns:
            List of document IDs
        """
        try:
            target_category = category or self.category or "default"
            
            logger.debug(f"Adding {len(documents)} documents to category: {target_category}")
            
            if self.use_unified_db:
                # Use unified database
                self._db.add_documents(documents, target_category, **kwargs)
                # Generate IDs (unified DB doesn't return them)
                doc_ids = [f"{target_category}_{i}" for i in range(len(documents))]
            else:
                # Use category-specific database
                add_documents_with_category(documents, target_category, **kwargs)
                doc_ids = [f"{target_category}_{i}" for i in range(len(documents))]
            
            logger.info(f"Successfully added {len(documents)} documents to category: {target_category}")
            return doc_ids
            
        except Exception as e:
            logger.error(f"Error adding documents: {e}")
            raise
    
    @performance_monitor(track_memory=True, track_cpu=True, log_parameters=True, log_result_size=True)
    def add_texts(
        self,
        texts: List[str],
        metadatas: Optional[List[Dict[str, Any]]] = None,
        category: Optional[str] = None,
        **kwargs
    ) -> List[str]:
        """
        Add texts to the vector store.
        
        Args:
            texts: List of texts to add
            metadatas: Optional list of metadata dicts
            category: Category for the texts (overrides default)
            **kwargs: Additional metadata
            
        Returns:
            List of document IDs
        """
        try:
            # Convert texts to documents
            documents = []
            for i, text in enumerate(texts):
                metadata = metadatas[i] if metadatas and i < len(metadatas) else {}
                metadata.update(kwargs)
                
                doc = Document(
                    page_content=text,
                    metadata=metadata
                )
                documents.append(doc)
            
            return self.add_documents(documents, category)
            
        except Exception as e:
            logger.error(f"Error adding texts: {e}")
            raise
    
    @performance_monitor(track_memory=True, track_cpu=True, log_parameters=True, log_result_size=True)
    def similarity_search(
        self,
        query: str,
        k: int = 4,
        category: Optional[str] = None,
        filter: Optional[Dict[str, Any]] = None,
        **kwargs
    ) -> List[Document]:
        """
        Perform similarity search.
        
        Args:
            query: Search query
            k: Number of results to return
            category: Category to search in (overrides default)
            filter: Additional filters
            **kwargs: Additional search parameters
            
        Returns:
            List of similar documents
        """
        try:
            target_category = category or self.category
            
            logger.debug(f"Performing similarity search for query: {query[:50]}...")
            
            if self.use_unified_db:
                # Use unified database with category filtering
                results = self._db.similarity_search(
                    query=query,
                    category=target_category,
                    k=k,
                    **kwargs
                )
            else:
                # Use category-specific database
                db = get_vector_db(target_category or "default")
                results = db.similarity_search(query, k=k, **kwargs)
            
            logger.debug(f"Found {len(results)} similar documents")
            return results
            
        except Exception as e:
            logger.error(f"Error in similarity search: {e}")
            raise
    
    @performance_monitor(track_memory=True, track_cpu=True, log_parameters=True, log_result_size=True)
    def similarity_search_with_score(
        self,
        query: str,
        k: int = 4,
        category: Optional[str] = None,
        **kwargs
    ) -> List[Tuple[Document, float]]:
        """
        Perform similarity search with scores.
        
        Args:
            query: Search query
            k: Number of results to return
            category: Category to search in (overrides default)
            **kwargs: Additional search parameters
            
        Returns:
            List of (document, score) tuples
        """
        try:
            target_category = category or self.category
            
            logger.debug(f"Performing similarity search with scores for query: {query[:50]}...")
            
            if self.use_unified_db:
                # Use unified database
                results = self._db.similarity_search_with_score(
                    query=query,
                    category=target_category,
                    k=k,
                    **kwargs
                )
            else:
                # Use category-specific database
                db = get_vector_db(target_category or "default")
                results = db.similarity_search_with_score(query, k=k, **kwargs)
            
            logger.debug(f"Found {len(results)} similar documents with scores")
            return results
            
        except Exception as e:
            logger.error(f"Error in similarity search with scores: {e}")
            raise
    
    def similarity_search_by_vector(
        self,
        embedding: List[float],
        k: int = 4,
        category: Optional[str] = None,
        **kwargs
    ) -> List[Document]:
        """
        Perform similarity search by vector.
        
        Args:
            embedding: Query embedding vector
            k: Number of results to return
            category: Category to search in (overrides default)
            **kwargs: Additional search parameters
            
        Returns:
            List of similar documents
        """
        try:
            target_category = category or self.category
            
            logger.debug(f"Performing similarity search by vector")
            
            if self.use_unified_db:
                # Use unified database
                results = self._db.similarity_search_by_vector(
                    embedding=embedding,
                    category=target_category,
                    k=k,
                    **kwargs
                )
            else:
                # Use category-specific database
                db = get_vector_db(target_category or "default")
                results = db.similarity_search_by_vector(embedding, k=k, **kwargs)
            
            logger.debug(f"Found {len(results)} similar documents by vector")
            return results
            
        except Exception as e:
            logger.error(f"Error in similarity search by vector: {e}")
            raise
    
    def max_marginal_relevance_search(
        self,
        query: str,
        k: int = 4,
        fetch_k: int = 20,
        lambda_mult: float = 0.5,
        category: Optional[str] = None,
        **kwargs
    ) -> List[Document]:
        """
        Perform max marginal relevance search.
        
        Args:
            query: Search query
            k: Number of results to return
            fetch_k: Number of documents to fetch for MMR
            lambda_mult: Lambda multiplier for MMR
            category: Category to search in (overrides default)
            **kwargs: Additional search parameters
            
        Returns:
            List of documents with maximum marginal relevance
        """
        try:
            target_category = category or self.category
            
            logger.debug(f"Performing MMR search for query: {query[:50]}...")
            
            if self.use_unified_db:
                # Use unified database
                results = self._db.max_marginal_relevance_search(
                    query=query,
                    category=target_category,
                    k=k,
                    fetch_k=fetch_k,
                    lambda_mult=lambda_mult,
                    **kwargs
                )
            else:
                # Use category-specific database
                db = get_vector_db(target_category or "default")
                results = db.max_marginal_relevance_search(
                    query, k=k, fetch_k=fetch_k, lambda_mult=lambda_mult, **kwargs
                )
            
            logger.debug(f"Found {len(results)} documents with MMR")
            return results
            
        except Exception as e:
            logger.error(f"Error in MMR search: {e}")
            raise
    
    def delete(
        self,
        ids: Optional[List[str]] = None,
        category: Optional[str] = None,
        **kwargs
    ) -> Optional[bool]:
        """
        Delete documents from the vector store.
        
        Args:
            ids: List of document IDs to delete
            category: Category to delete from (overrides default)
            **kwargs: Additional delete parameters
            
        Returns:
            True if successful, False otherwise
        """
        try:
            target_category = category or self.category
            
            logger.debug(f"Deleting documents from category: {target_category}")
            
            if self.use_unified_db:
                # Use unified database
                success = self._db.delete_documents(
                    ids=ids,
                    category=target_category,
                    **kwargs
                )
            else:
                # Use category-specific database
                db = get_vector_db(target_category or "default")
                success = db.delete(ids=ids, **kwargs)
            
            logger.info(f"Successfully deleted documents from category: {target_category}")
            return success
            
        except Exception as e:
            logger.error(f"Error deleting documents: {e}")
            return False
    
    def get_collection_stats(self, category: Optional[str] = None) -> Dict[str, Any]:
        """
        Get statistics about the collection.
        
        Args:
            category: Category to get stats for (overrides default)
            
        Returns:
            Dictionary with collection statistics
        """
        try:
            target_category = category or self.category
            
            if self.use_unified_db:
                return self._db.get_collection_stats(target_category)
            else:
                # Basic stats for category-specific database
                db = get_vector_db(target_category or "default")
                return {
                    "category": target_category,
                    "collection_name": getattr(db, '_collection_name', 'unknown'),
                    "persist_directory": getattr(db, '_persist_directory', 'unknown')
                }
                
        except Exception as e:
            logger.error(f"Error getting collection stats: {e}")
            return {}
    
    @classmethod
    def from_documents(
        cls,
        documents: List[Document],
        embedding: Optional[Embeddings] = None,
        category: Optional[str] = None,
        **kwargs
    ) -> "ChromaVectorStoreWrapper":
        """
        Create a vector store from documents.
        
        Args:
            documents: List of documents to add
            embedding: Embeddings function
            category: Category for the documents
            **kwargs: Additional arguments
            
        Returns:
            ChromaVectorStoreWrapper instance
        """
        vectorstore = cls(
            embeddings=embedding,
            category=category,
            **kwargs
        )
        vectorstore.add_documents(documents, category)
        return vectorstore
    
    @classmethod
    def from_texts(
        cls,
        texts: List[str],
        embedding: Optional[Embeddings] = None,
        metadatas: Optional[List[Dict[str, Any]]] = None,
        category: Optional[str] = None,
        **kwargs
    ) -> "ChromaVectorStoreWrapper":
        """
        Create a vector store from texts.
        
        Args:
            texts: List of texts to add
            embedding: Embeddings function
            metadatas: Optional list of metadata dicts
            category: Category for the texts
            **kwargs: Additional arguments
            
        Returns:
            ChromaVectorStoreWrapper instance
        """
        vectorstore = cls(
            embeddings=embedding,
            category=category,
            **kwargs
        )
        vectorstore.add_texts(texts, metadatas, category)
        return vectorstore


def get_chroma_vectorstore(
    category: Optional[str] = None,
    embeddings: Optional[Embeddings] = None,
    **kwargs
) -> ChromaVectorStoreWrapper:
    """
    Factory function to create a ChromaDB vector store wrapper.
    
    Args:
        category: Default category for operations
        embeddings: Embeddings function
        **kwargs: Additional arguments
        
    Returns:
        ChromaVectorStoreWrapper instance
    """
    return ChromaVectorStoreWrapper(
        embeddings=embeddings,
        category=category,
        **kwargs
    )

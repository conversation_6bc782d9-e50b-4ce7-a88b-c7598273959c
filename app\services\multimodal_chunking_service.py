"""
Multimodal Chunking Service
Extends existing chunking to handle multimodal content with context preservation
"""

import logging
from typing import Dict, Any, List, Optional, Tuple
from langchain.schema import Document

from config.multimodal_config import get_multimodal_config, ContentType
try:
    from app.services.enhanced_chunking_service import EnhancedChunkingService
    ENHANCED_CHUNKING_AVAILABLE = True
except ImportError as e:
    logger.warning(f"Enhanced chunking service not available: {e}")
    # Create a basic fallback class
    class EnhancedChunkingService:
        def chunk_documents(self, documents):
            return documents
    ENHANCED_CHUNKING_AVAILABLE = False
from app.services.multimodal_storage import get_multimodal_storage
from app.utils.performance_monitor import performance_monitor

logger = logging.getLogger(__name__)

class MultimodalChunkingService(EnhancedChunkingService):
    """Enhanced chunking service for multimodal content"""
    
    def __init__(self, config=None):
        super().__init__()
        self.multimodal_config = config or get_multimodal_config()
        self.storage = get_multimodal_storage()
        
    @performance_monitor(track_memory=True, track_cpu=True, log_parameters=True, log_result_size=True)
    def chunk_multimodal_document(self, multimodal_result: Dict[str, Any]) -> List[Document]:
        """
        Create chunks from multimodal document processing result
        
        Args:
            multimodal_result: Result from MultimodalDocumentProcessor
            
        Returns:
            List of LangChain Documents with multimodal metadata
        """
        if not self.multimodal_config.chunking.enable_multimodal_chunking:
            logger.info("Multimodal chunking is disabled, using text-only chunking")
            return self._chunk_text_only(multimodal_result)
        
        try:
            logger.info("Creating multimodal chunks")
            
            # Get multimodal chunks from processing result
            multimodal_chunks = multimodal_result.get("multimodal_chunks", [])
            
            if not multimodal_chunks:
                logger.warning("No multimodal chunks found, falling back to text-only")
                return self._chunk_text_only(multimodal_result)
            
            documents = []
            
            for chunk in multimodal_chunks:
                chunk_documents = self._process_multimodal_chunk(chunk, multimodal_result)
                documents.extend(chunk_documents)
            
            logger.info(f"Created {len(documents)} multimodal document chunks")
            return documents
            
        except Exception as e:
            logger.error(f"Error in multimodal chunking: {e}")
            return self._chunk_text_only(multimodal_result)
    
    def _process_multimodal_chunk(self, chunk: Dict[str, Any], 
                                 multimodal_result: Dict[str, Any]) -> List[Document]:
        """Process a single multimodal chunk"""
        documents = []
        
        try:
            page_num = chunk.get("page_num", 1)
            content_types = chunk.get("content_types", [])
            
            # Process text content
            if "text" in content_types:
                text_docs = self._process_text_content(chunk, multimodal_result)
                documents.extend(text_docs)
            
            # Process images
            if "image" in content_types:
                image_docs = self._process_image_content(chunk, multimodal_result)
                documents.extend(image_docs)
            
            # Process tables
            if "table" in content_types:
                table_docs = self._process_table_content(chunk, multimodal_result)
                documents.extend(table_docs)
            
            # Create combined multimodal chunk if multiple content types
            if len(content_types) > 1 and self.multimodal_config.chunking.preserve_content_relationships:
                combined_doc = self._create_combined_chunk(chunk, multimodal_result)
                if combined_doc:
                    documents.append(combined_doc)
            
            return documents
            
        except Exception as e:
            logger.error(f"Error processing multimodal chunk: {e}")
            return []
    
    def _process_text_content(self, chunk: Dict[str, Any], 
                             multimodal_result: Dict[str, Any]) -> List[Document]:
        """Process text content within a multimodal chunk"""
        try:
            text_content = chunk.get("text_content", "")
            if not text_content.strip():
                return []
            
            # Use existing chunking for text
            text_documents = [Document(page_content=text_content)]
            chunked_docs = self.chunk_documents(text_documents)
            
            # Add multimodal metadata
            for doc in chunked_docs:
                doc.metadata.update({
                    "content_type": ContentType.TEXT.value,
                    "document_id": multimodal_result.get("document_id"),
                    "page_num": chunk.get("page_num"),
                    "chunk_type": "text",
                    "has_images": bool(chunk.get("images")),
                    "has_tables": bool(chunk.get("tables")),
                    "multimodal_context": True
                })
            
            return chunked_docs
            
        except Exception as e:
            logger.error(f"Error processing text content: {e}")
            return []
    
    def _process_image_content(self, chunk: Dict[str, Any],
                              multimodal_result: Dict[str, Any]) -> List[Document]:
        """Process image content within a multimodal chunk"""
        documents = []
        
        try:
            images = chunk.get("images", [])
            
            for image in images:
                # Get image caption from storage or metadata
                caption = image.get("caption", "")
                if not caption:
                    # Try to get from storage
                    content_hash = image.get("content_hash")
                    if content_hash:
                        stored_image = self.storage.retrieve_image(content_hash)
                        if stored_image:
                            caption = stored_image.get("metadata", {}).get("caption", "")
                
                if not caption:
                    logger.warning(f"No caption found for image {image.get('content_hash')}")
                    continue
                
                # Create document for image description
                image_doc = Document(
                    page_content=f"Image description: {caption}",
                    metadata={
                        "content_type": ContentType.IMAGE.value,
                        "document_id": multimodal_result.get("document_id"),
                        "page_num": chunk.get("page_num"),
                        "image_index": image.get("image_index"),
                        "content_hash": image.get("content_hash"),
                        "image_format": image.get("format"),
                        "image_dimensions": image.get("dimensions", {}),
                        "image_bbox": image.get("bbox", {}),
                        "chunk_type": "image_description",
                        "multimodal_context": True,
                        "vision_analysis": image.get("vision_analysis", {})
                    }
                )
                
                documents.append(image_doc)
            
            return documents
            
        except Exception as e:
            logger.error(f"Error processing image content: {e}")
            return []
    
    def _process_table_content(self, chunk: Dict[str, Any],
                              multimodal_result: Dict[str, Any]) -> List[Document]:
        """Process table content within a multimodal chunk"""
        documents = []
        
        try:
            tables = chunk.get("tables", [])
            
            for table in tables:
                # Get table content from storage
                content_hash = table.get("content_hash")
                if not content_hash:
                    continue
                
                stored_table = self.storage.retrieve_table(content_hash)
                if not stored_table:
                    logger.warning(f"Could not retrieve table {content_hash}")
                    continue
                
                table_data = stored_table.get("data", {})
                
                # Use markdown representation if available
                table_content = table_data.get("markdown", "")
                if not table_content:
                    # Fallback to structured data
                    structured_data = table_data.get("structured_data", {})
                    table_content = self._format_table_for_text(structured_data)
                
                if not table_content:
                    continue
                
                # Add context if available
                context = table_data.get("context", "")
                if context and self.multimodal_config.table_processing.include_table_context:
                    table_content = f"Context: {context}\n\nTable:\n{table_content}"
                
                # Create document for table
                table_doc = Document(
                    page_content=table_content,
                    metadata={
                        "content_type": ContentType.TABLE.value,
                        "document_id": multimodal_result.get("document_id"),
                        "page_num": chunk.get("page_num"),
                        "table_index": table.get("table_index"),
                        "content_hash": content_hash,
                        "num_rows": table.get("num_rows"),
                        "num_cols": table.get("num_cols"),
                        "extraction_method": table.get("extraction_method"),
                        "chunk_type": "table",
                        "multimodal_context": True,
                        "table_bbox": table.get("bbox", {})
                    }
                )
                
                documents.append(table_doc)
            
            return documents
            
        except Exception as e:
            logger.error(f"Error processing table content: {e}")
            return []
    
    def _create_combined_chunk(self, chunk: Dict[str, Any],
                              multimodal_result: Dict[str, Any]) -> Optional[Document]:
        """Create a combined chunk that includes all content types"""
        try:
            content_parts = []
            
            # Add text content
            text_content = chunk.get("text_content", "").strip()
            if text_content:
                content_parts.append(f"Text content:\n{text_content}")
            
            # Add image descriptions
            images = chunk.get("images", [])
            if images:
                image_descriptions = []
                for image in images:
                    caption = image.get("caption", "")
                    if caption:
                        image_descriptions.append(f"- {caption}")
                
                if image_descriptions:
                    content_parts.append(f"Images on this page:\n" + "\n".join(image_descriptions))
            
            # Add table content
            tables = chunk.get("tables", [])
            if tables:
                table_contents = []
                for table in tables:
                    content_hash = table.get("content_hash")
                    if content_hash:
                        stored_table = self.storage.retrieve_table(content_hash)
                        if stored_table:
                            table_data = stored_table.get("data", {})
                            markdown = table_data.get("markdown", "")
                            if markdown:
                                table_contents.append(markdown)
                
                if table_contents:
                    content_parts.append(f"Tables on this page:\n" + "\n\n".join(table_contents))
            
            if not content_parts:
                return None
            
            combined_content = "\n\n".join(content_parts)
            
            return Document(
                page_content=combined_content,
                metadata={
                    "content_type": ContentType.MIXED.value,
                    "document_id": multimodal_result.get("document_id"),
                    "page_num": chunk.get("page_num"),
                    "content_types": chunk.get("content_types", []),
                    "chunk_type": "multimodal_combined",
                    "multimodal_context": True,
                    "num_images": len(images),
                    "num_tables": len(tables),
                    "spatial_relationships": chunk.get("spatial_relationships", {})
                }
            )
            
        except Exception as e:
            logger.error(f"Error creating combined chunk: {e}")
            return None
    
    def _format_table_for_text(self, structured_data: Dict[str, Any]) -> str:
        """Format structured table data as text"""
        try:
            rows = structured_data.get("rows", [])
            if not rows:
                return ""
            
            # Simple text formatting
            formatted_rows = []
            for row in rows:
                formatted_row = " | ".join(str(cell) for cell in row)
                formatted_rows.append(formatted_row)
            
            return "\n".join(formatted_rows)
            
        except Exception as e:
            logger.error(f"Error formatting table for text: {e}")
            return ""
    
    def _chunk_text_only(self, multimodal_result: Dict[str, Any]) -> List[Document]:
        """Fallback to text-only chunking"""
        try:
            text_content = multimodal_result.get("text_content", [])
            if not text_content:
                return []
            
            # Combine all text content
            combined_text = ""
            for text_item in text_content:
                combined_text += text_item.get("text", "") + "\n"
            
            if not combined_text.strip():
                return []
            
            # Use existing chunking
            text_documents = [Document(page_content=combined_text)]
            chunked_docs = self.chunk_documents(text_documents)
            
            # Add basic metadata
            for doc in chunked_docs:
                doc.metadata.update({
                    "content_type": ContentType.TEXT.value,
                    "document_id": multimodal_result.get("document_id"),
                    "chunk_type": "text_only",
                    "multimodal_context": False
                })
            
            return chunked_docs
            
        except Exception as e:
            logger.error(f"Error in text-only chunking: {e}")
            return []

# Global chunking service instance
_multimodal_chunking_service = None

def get_multimodal_chunking_service() -> MultimodalChunkingService:
    """Get the global multimodal chunking service instance"""
    global _multimodal_chunking_service
    if _multimodal_chunking_service is None:
        _multimodal_chunking_service = MultimodalChunkingService()
    return _multimodal_chunking_service

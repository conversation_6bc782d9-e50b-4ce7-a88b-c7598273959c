{"old_method": {"method": "RecursiveCharacterTextSplitter", "processing_time": 0.0005776882171630859, "chunk_count": 5, "avg_chunk_size": 633.0, "min_chunk_size": 291, "max_chunk_size": 757, "metadata_richness": 0.15, "structure_preservation": 0}, "new_method": {"method": "Enhanced Adaptive Chunking", "processing_time": 0.12264084815979004, "chunk_count": 4, "avg_chunk_size": 736.0, "min_chunk_size": 291, "max_chunk_size": 975, "content_type_detected": "technical", "metadata_richness": 1.0, "structure_preservation": 0.9}, "improvements": {"processing_time_change_percent": -21129.591415600495, "chunk_count_change": -1, "metadata_richness_improvement_percent": 85.0, "structure_preservation_improvement_percent": 90.0}}
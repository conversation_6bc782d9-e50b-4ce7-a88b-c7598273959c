"""
Hybrid Search Service
Combines search results from text, images, and tables with ranking and merging
"""

import logging
from typing import Dict, Any, List, Optional, Tuple
from langchain.schema import Document
import time

from config.multimodal_config import get_multimodal_config, ContentType
from app.services.vector_db import get_vector_db, similarity_search_with_category_filter
from app.services.query_service import get_adaptive_k, filter_relevant_documents
from app.services.multimodal_storage import get_multimodal_storage
from app.utils.performance_monitor import performance_monitor

logger = logging.getLogger(__name__)

class HybridSearchService:
    """Service for hybrid search across multimodal content"""
    
    def __init__(self, config=None):
        self.config = config or get_multimodal_config()
        self.storage = get_multimodal_storage()
        
    @performance_monitor(track_memory=True, track_cpu=True, log_parameters=True, log_result_size=True)
    def search_multimodal(self, query: str, category: Optional[str] = None,
                         k: int = 10, content_types: Optional[List[str]] = None) -> Dict[str, Any]:
        """
        Perform hybrid search across multimodal content
        
        Args:
            query: Search query
            category: Category to search within
            k: Number of results to return
            content_types: Specific content types to search (None for all)
            
        Returns:
            Dictionary with search results and metadata
        """
        if not self.config.search.enable_hybrid_search:
            logger.info("Hybrid search is disabled, using text-only search")
            return self._text_only_search(query, category, k)
        
        try:
            logger.info(f"Performing hybrid search for query: {query[:50]}...")
            
            # Determine content types to search
            if content_types is None:
                content_types = [ContentType.TEXT.value, ContentType.IMAGE.value, 
                               ContentType.TABLE.value, ContentType.MIXED.value]
            
            # Get adaptive k value
            adaptive_k = get_adaptive_k(query, k)
            
            # Search each content type
            search_results = {}
            total_results = []
            
            for content_type in content_types:
                type_results = self._search_content_type(
                    query, content_type, category, adaptive_k
                )
                search_results[content_type] = type_results
                total_results.extend(type_results)
            
            # Merge and rank results
            merged_results = self._merge_and_rank_results(
                search_results, query, k
            )
            
            # Enhance results with multimodal content
            enhanced_results = self._enhance_results_with_content(merged_results)
            
            # Prepare final response
            response = {
                "query": query,
                "category": category,
                "total_results": len(total_results),
                "results_by_type": {k: len(v) for k, v in search_results.items()},
                "merged_results": enhanced_results[:k],
                "search_metadata": {
                    "adaptive_k": adaptive_k,
                    "content_types_searched": content_types,
                    "hybrid_search": True,
                    "timestamp": time.time()
                }
            }
            
            logger.info(f"Hybrid search completed: {len(enhanced_results)} results")
            return response
            
        except Exception as e:
            logger.error(f"Error in hybrid search: {e}")
            return self._text_only_search(query, category, k)
    
    def _search_content_type(self, query: str, content_type: str, 
                            category: Optional[str], k: int) -> List[Document]:
        """Search within a specific content type"""
        try:
            # Determine collection to search
            if self.config.embedding.separate_collections:
                search_category = f"{category}_{content_type}" if category else content_type
            else:
                search_category = category
            
            # Get maximum results for this content type
            max_results = self.config.search.max_results_per_type.get(content_type, k)
            search_k = min(k, max_results)
            
            # Perform similarity search
            results = similarity_search_with_category_filter(
                query=query,
                category=search_category,
                k=search_k
            )
            
            # Filter results by content type if using main collection
            if not self.config.embedding.separate_collections:
                results = [
                    doc for doc in results 
                    if doc.metadata.get("content_type") == content_type
                ]
            
            # Filter by relevance threshold
            if self.config.search.similarity_threshold > 0:
                results = [
                    doc for doc in results
                    if self._calculate_relevance_score(doc, query) >= self.config.search.similarity_threshold
                ]
            
            logger.debug(f"Found {len(results)} {content_type} results")
            return results
            
        except Exception as e:
            logger.error(f"Error searching {content_type} content: {e}")
            return []
    
    def _merge_and_rank_results(self, search_results: Dict[str, List[Document]], 
                               query: str, k: int) -> List[Document]:
        """Merge and rank results from different content types"""
        try:
            all_results = []
            
            # Apply content type weights and collect results
            for content_type, results in search_results.items():
                weight = self.config.search.content_type_weights.get(content_type, 1.0)
                
                for doc in results:
                    # Calculate weighted score
                    base_score = self._calculate_relevance_score(doc, query)
                    weighted_score = base_score * weight
                    
                    # Add scoring metadata
                    doc.metadata["relevance_score"] = base_score
                    doc.metadata["weighted_score"] = weighted_score
                    doc.metadata["content_type_weight"] = weight
                    
                    all_results.append(doc)
            
            # Remove duplicates (same content hash)
            unique_results = self._deduplicate_results(all_results)
            
            # Sort by weighted score
            if self.config.search.rerank_results:
                unique_results.sort(
                    key=lambda x: x.metadata.get("weighted_score", 0), 
                    reverse=True
                )
            
            return unique_results
            
        except Exception as e:
            logger.error(f"Error merging and ranking results: {e}")
            # Fallback: return all results without ranking
            all_results = []
            for results in search_results.values():
                all_results.extend(results)
            return all_results[:k]
    
    def _calculate_relevance_score(self, doc: Document, query: str) -> float:
        """Calculate relevance score for a document"""
        try:
            # Basic scoring based on content type and metadata
            base_score = 0.5
            
            content_type = doc.metadata.get("content_type", ContentType.TEXT.value)
            
            # Boost scores for certain content types based on query
            query_lower = query.lower()
            
            if content_type == ContentType.IMAGE.value:
                # Boost for visual-related queries
                visual_keywords = ["image", "picture", "diagram", "chart", "graph", "figure"]
                if any(keyword in query_lower for keyword in visual_keywords):
                    base_score += 0.3
            
            elif content_type == ContentType.TABLE.value:
                # Boost for data-related queries
                data_keywords = ["table", "data", "statistics", "numbers", "comparison"]
                if any(keyword in query_lower for keyword in data_keywords):
                    base_score += 0.3
            
            elif content_type == ContentType.MIXED.value:
                # Mixed content gets a moderate boost for comprehensive queries
                base_score += 0.1
            
            # Additional scoring based on metadata
            if doc.metadata.get("multimodal_context"):
                base_score += 0.1
            
            # Boost for documents with multiple content types
            if doc.metadata.get("content_types"):
                num_types = len(doc.metadata["content_types"])
                if num_types > 1:
                    base_score += 0.1 * (num_types - 1)
            
            return min(1.0, base_score)
            
        except Exception as e:
            logger.error(f"Error calculating relevance score: {e}")
            return 0.5
    
    def _deduplicate_results(self, results: List[Document]) -> List[Document]:
        """Remove duplicate results based on content hash"""
        try:
            seen_hashes = set()
            unique_results = []
            
            for doc in results:
                # Use content hash if available, otherwise use content
                content_hash = doc.metadata.get("content_hash")
                if not content_hash:
                    content_hash = hash(doc.page_content)
                
                if content_hash not in seen_hashes:
                    seen_hashes.add(content_hash)
                    unique_results.append(doc)
            
            return unique_results
            
        except Exception as e:
            logger.error(f"Error deduplicating results: {e}")
            return results
    
    def _enhance_results_with_content(self, results: List[Document]) -> List[Document]:
        """Enhance results with additional multimodal content"""
        try:
            if not self.config.search.include_content_in_results:
                return results
            
            enhanced_results = []
            
            for doc in results:
                enhanced_doc = doc.copy()
                
                # Add related content based on content type
                content_type = doc.metadata.get("content_type")
                
                if content_type == ContentType.IMAGE.value:
                    enhanced_doc = self._enhance_image_result(enhanced_doc)
                elif content_type == ContentType.TABLE.value:
                    enhanced_doc = self._enhance_table_result(enhanced_doc)
                
                enhanced_results.append(enhanced_doc)
            
            return enhanced_results
            
        except Exception as e:
            logger.error(f"Error enhancing results with content: {e}")
            return results
    
    def _enhance_image_result(self, doc: Document) -> Document:
        """Enhance image result with additional metadata"""
        try:
            content_hash = doc.metadata.get("content_hash")
            if content_hash:
                # Get image metadata from storage
                image_data = self.storage.retrieve_image(content_hash)
                if image_data:
                    metadata = image_data.get("metadata", {})
                    
                    # Add image file path if available
                    doc.metadata["image_file_path"] = metadata.get("storage_path")
                    doc.metadata["image_size_bytes"] = metadata.get("size_bytes")
                    doc.metadata["image_created_at"] = metadata.get("created_at")
            
            return doc
            
        except Exception as e:
            logger.error(f"Error enhancing image result: {e}")
            return doc
    
    def _enhance_table_result(self, doc: Document) -> Document:
        """Enhance table result with additional metadata"""
        try:
            content_hash = doc.metadata.get("content_hash")
            if content_hash:
                # Get table data from storage
                table_data = self.storage.retrieve_table(content_hash)
                if table_data:
                    data = table_data.get("data", {})
                    metadata = table_data.get("metadata", {})
                    
                    # Add table structure info
                    structured_data = data.get("structured_data", {})
                    doc.metadata["table_structure"] = {
                        "num_rows": structured_data.get("num_rows"),
                        "num_cols": structured_data.get("num_cols")
                    }
                    doc.metadata["table_created_at"] = metadata.get("created_at")
            
            return doc
            
        except Exception as e:
            logger.error(f"Error enhancing table result: {e}")
            return doc
    
    def _text_only_search(self, query: str, category: Optional[str], k: int) -> Dict[str, Any]:
        """Fallback to text-only search"""
        try:
            logger.info("Performing text-only search fallback")
            
            # Use existing search functionality
            results = similarity_search_with_category_filter(
                query=query,
                category=category,
                k=k
            )
            
            return {
                "query": query,
                "category": category,
                "total_results": len(results),
                "results_by_type": {ContentType.TEXT.value: len(results)},
                "merged_results": results,
                "search_metadata": {
                    "adaptive_k": k,
                    "content_types_searched": [ContentType.TEXT.value],
                    "hybrid_search": False,
                    "fallback_mode": True,
                    "timestamp": time.time()
                }
            }
            
        except Exception as e:
            logger.error(f"Error in text-only search fallback: {e}")
            return {
                "query": query,
                "category": category,
                "total_results": 0,
                "results_by_type": {},
                "merged_results": [],
                "search_metadata": {
                    "error": str(e),
                    "timestamp": time.time()
                }
            }

# Global search service instance
_hybrid_search_service = None

def get_hybrid_search_service() -> HybridSearchService:
    """Get the global hybrid search service instance"""
    global _hybrid_search_service
    if _hybrid_search_service is None:
        _hybrid_search_service = HybridSearchService()
    return _hybrid_search_service

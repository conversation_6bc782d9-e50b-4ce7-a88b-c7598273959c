#!/usr/bin/env python3
"""
Migration Script for Enhanced Chunking System
Installs dependencies and validates the enhanced chunking setup.
"""

import sys
import os
import subprocess
import logging

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def install_dependencies():
    """Install required LlamaIndex dependencies"""
    print("Installing LlamaIndex dependencies...")
    
    dependencies = [
        "llama-index-core",
        "llama-index-embeddings-ollama"
    ]
    
    for dep in dependencies:
        try:
            print(f"Installing {dep}...")
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", dep
            ], capture_output=True, text=True, check=True)
            print(f"✓ {dep} installed successfully")
        except subprocess.CalledProcessError as e:
            print(f"✗ Failed to install {dep}: {e}")
            print(f"Error output: {e.stderr}")
            return False
    
    return True

def check_ollama_connection():
    """Check if Ollama is running and accessible"""
    print("\nChecking Ollama connection...")
    
    try:
        import requests
        from config.chunking_config import get_chunking_config
        
        config = get_chunking_config()
        response = requests.get(f"{config.ollama_base_url}/api/tags", timeout=5)
        
        if response.status_code == 200:
            models = response.json().get('models', [])
            print(f"✓ Ollama is running at {config.ollama_base_url}")
            print(f"  Available models: {len(models)}")
            
            # Check if the configured embedding model is available
            model_names = [model['name'] for model in models]
            if config.embedding_model in model_names:
                print(f"✓ Embedding model '{config.embedding_model}' is available")
            else:
                print(f"⚠ Embedding model '{config.embedding_model}' not found")
                print(f"  Available models: {', '.join(model_names[:5])}")
                print(f"  You may need to pull the model: ollama pull {config.embedding_model}")
            
            return True
        else:
            print(f"✗ Ollama responded with status {response.status_code}")
            return False
            
    except Exception as e:
        print(f"✗ Failed to connect to Ollama: {e}")
        print("  Make sure Ollama is running: ollama serve")
        return False

def validate_configuration():
    """Validate the chunking configuration"""
    print("\nValidating configuration...")
    
    try:
        from config.chunking_config import get_chunking_config
        
        config = get_chunking_config()
        print(f"✓ Configuration loaded successfully")
        print(f"  Default chunk size: {config.default_chunk_size}")
        print(f"  Default overlap: {config.default_chunk_overlap}")
        print(f"  Embedding model: {config.embedding_model}")
        print(f"  Parallel processing: {config.enable_parallel_processing}")
        
        # Test content-type configurations
        content_types = ['technical', 'scientific', 'narrative', 'general']
        for content_type in content_types:
            type_config = config.get_config_for_content_type(content_type)
            strategy = type_config.get('strategy', 'default')
            print(f"  {content_type}: {strategy} strategy")
        
        return True
        
    except Exception as e:
        print(f"✗ Configuration validation failed: {e}")
        return False

def test_enhanced_chunking():
    """Test the enhanced chunking system"""
    print("\nTesting enhanced chunking system...")
    
    try:
        from langchain.schema import Document
        from app.services.enhanced_chunking_service import EnhancedChunkingService
        
        # Create a test document
        test_doc = Document(
            page_content="""
            This is a test document for the enhanced chunking system.
            It contains multiple sentences to test the chunking behavior.
            The system should be able to detect content type and apply
            appropriate chunking strategies based on the content analysis.
            This test verifies that the migration was successful.
            """,
            metadata={"source": "migration_test.txt", "type": "test"}
        )
        
        # Initialize and test the chunking service
        chunking_service = EnhancedChunkingService()
        chunks = chunking_service.adaptive_chunk([test_doc])
        
        print(f"✓ Enhanced chunking service working")
        print(f"  Created {len(chunks)} chunks from 1 document")
        print(f"  Average chunk size: {sum(len(c.page_content) for c in chunks) / len(chunks):.0f} chars")
        
        # Verify metadata preservation
        for chunk in chunks:
            assert chunk.metadata["source"] == "migration_test.txt"
            assert chunk.metadata["type"] == "test"
        
        print(f"✓ Metadata preservation working")
        return True
        
    except Exception as e:
        print(f"✗ Enhanced chunking test failed: {e}")
        logger.exception("Detailed error:")
        return False

def check_existing_integrations():
    """Check that existing integrations still work"""
    print("\nChecking existing integrations...")
    
    try:
        # Test vector database integration
        from app.services.vector_db import get_vector_db
        
        # This should work without errors
        db = get_vector_db("test_category")
        print("✓ Vector database integration working")
        
        # Test configuration loading
        from app.utils.config import load_default_models
        models = load_default_models()
        print("✓ Configuration loading working")
        
        return True
        
    except Exception as e:
        print(f"✗ Integration check failed: {e}")
        return False

def create_backup_info():
    """Create backup information for rollback"""
    print("\nCreating backup information...")
    
    backup_info = {
        "timestamp": "2025-01-02",
        "original_chunking": "LangChain RecursiveCharacterTextSplitter",
        "enhanced_chunking": "LlamaIndex with Ollama embeddings",
        "rollback_instructions": [
            "1. Remove enhanced chunking imports from pdf_processor.py and embedding_service.py",
            "2. Restore original RecursiveCharacterTextSplitter calls",
            "3. Remove enhanced chunking service files if needed"
        ]
    }
    
    try:
        import json
        backup_path = "scripts/chunking_migration_backup.json"
        with open(backup_path, 'w') as f:
            json.dump(backup_info, f, indent=2)
        print(f"✓ Backup information saved to {backup_path}")
        return True
    except Exception as e:
        print(f"⚠ Failed to create backup info: {e}")
        return False

def main():
    """Run the migration process"""
    print("Enhanced Chunking System Migration")
    print("=" * 50)
    
    steps = [
        ("Installing Dependencies", install_dependencies),
        ("Checking Ollama Connection", check_ollama_connection),
        ("Validating Configuration", validate_configuration),
        ("Testing Enhanced Chunking", test_enhanced_chunking),
        ("Checking Existing Integrations", check_existing_integrations),
        ("Creating Backup Information", create_backup_info)
    ]
    
    results = []
    for step_name, step_func in steps:
        print(f"\n{step_name}...")
        try:
            result = step_func()
            results.append((step_name, result))
        except Exception as e:
            print(f"✗ {step_name} failed with exception: {e}")
            results.append((step_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("Migration Results:")
    
    passed = 0
    for step_name, result in results:
        status = "✓ PASS" if result else "✗ FAIL"
        print(f"  {step_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nCompleted: {passed}/{len(results)} steps")
    
    if passed == len(results):
        print("\n🎉 Migration completed successfully!")
        print("\nThe enhanced chunking system is now active with the following features:")
        print("  • Semantic chunking with Ollama embeddings")
        print("  • Content-type detection and adaptive strategies")
        print("  • Improved sentence and paragraph boundary detection")
        print("  • Parallel processing for large document sets")
        print("  • Centralized configuration management")
        print("  • Fallback compatibility with existing LangChain setup")
        
        print("\nNext steps:")
        print("  1. Test with your existing documents")
        print("  2. Monitor performance improvements")
        print("  3. Adjust content-type configurations as needed")
        
    else:
        print("\n⚠ Migration completed with some issues.")
        print("The system will fall back to LangChain when needed.")
        print("Check the error messages above and resolve any issues.")

if __name__ == "__main__":
    main()

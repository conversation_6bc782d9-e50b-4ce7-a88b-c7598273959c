"""
LangChain OllamaEmbeddingsWrapper

LangChain-compatible embeddings wrapper that integrates with the existing
cached Ollama embeddings infrastructure, maintaining cache compatibility
and performance optimizations.
"""

import logging
import os
from typing import List, Optional

from langchain_core.embeddings import Embeddings

from app.services.vector_db import (
    CachedOllamaEmbeddings, 
    get_cached_embedding, 
    cache_embedding,
    TEXT_EMBEDDING_MODEL,
    OLLAMA_BASE_URL
)
from app.utils.performance_monitor import performance_monitor

logger = logging.getLogger(__name__)


class OllamaEmbeddingsWrapper(Embeddings):
    """
    LangChain-compatible wrapper for Ollama embeddings with caching.
    
    This wrapper integrates with the existing cached Ollama embeddings
    infrastructure to provide LangChain compatibility while maintaining
    all existing performance optimizations and caching mechanisms.
    """
    
    def __init__(
        self,
        model: Optional[str] = None,
        base_url: Optional[str] = None,
        cache_ttl: int = 86400,  # 24 hours default
        **kwargs
    ):
        """
        Initialize the LangChain-compatible Ollama embeddings wrapper.
        
        Args:
            model: Ollama model name (defaults to TEXT_EMBEDDING_MODEL)
            base_url: Ollama base URL (defaults to OLLAMA_BASE_URL)
            cache_ttl: Cache TTL in seconds (default: 24 hours)
            **kwargs: Additional arguments passed to CachedOllamaEmbeddings
        """
        self.model = model or TEXT_EMBEDDING_MODEL
        self.base_url = base_url or OLLAMA_BASE_URL
        self.cache_ttl = cache_ttl
        
        # Initialize the existing cached Ollama embeddings
        try:
            self._cached_embeddings = CachedOllamaEmbeddings(
                model=self.model,
                base_url=self.base_url,
                **kwargs
            )
            logger.info(f"Initialized LangChain Ollama embeddings wrapper with model: {self.model}")
        except Exception as e:
            logger.error(f"Failed to initialize Ollama embeddings: {e}")
            raise
    
    @performance_monitor(track_memory=True, track_cpu=True, log_parameters=True, log_result_size=True)
    def embed_documents(self, texts: List[str]) -> List[List[float]]:
        """
        Embed a list of documents using cached Ollama embeddings.
        
        Args:
            texts: List of text documents to embed
            
        Returns:
            List of embedding vectors
        """
        try:
            if not texts:
                return []
            
            logger.debug(f"Embedding {len(texts)} documents with model: {self.model}")
            
            # Use existing cached embeddings implementation
            embeddings = self._cached_embeddings.embed_documents(texts)
            
            # Ensure all embeddings are lists of floats (LangChain requirement)
            processed_embeddings = []
            for embedding in embeddings:
                if isinstance(embedding, list):
                    processed_embeddings.append([float(x) for x in embedding])
                else:
                    # Handle numpy arrays or other formats
                    processed_embeddings.append([float(x) for x in embedding.tolist()])
            
            logger.debug(f"Successfully embedded {len(texts)} documents")
            return processed_embeddings
            
        except Exception as e:
            logger.error(f"Error embedding documents: {e}")
            raise
    
    @performance_monitor(track_memory=True, track_cpu=True, log_parameters=True, log_result_size=True)
    def embed_query(self, text: str) -> List[float]:
        """
        Embed a single query using cached Ollama embeddings.
        
        Args:
            text: Query text to embed
            
        Returns:
            Embedding vector as list of floats
        """
        try:
            if not text:
                return []
            
            logger.debug(f"Embedding query with model: {self.model}")
            
            # Use existing cached embeddings implementation
            embedding = self._cached_embeddings.embed_query(text)
            
            # Ensure embedding is a list of floats (LangChain requirement)
            if isinstance(embedding, list):
                processed_embedding = [float(x) for x in embedding]
            else:
                # Handle numpy arrays or other formats
                processed_embedding = [float(x) for x in embedding.tolist()]
            
            logger.debug(f"Successfully embedded query")
            return processed_embedding
            
        except Exception as e:
            logger.error(f"Error embedding query: {e}")
            raise
    
    async def aembed_documents(self, texts: List[str]) -> List[List[float]]:
        """
        Async version of embed_documents.
        
        Note: Currently delegates to sync version as the underlying
        CachedOllamaEmbeddings doesn't support async operations.
        """
        return self.embed_documents(texts)
    
    async def aembed_query(self, text: str) -> List[float]:
        """
        Async version of embed_query.
        
        Note: Currently delegates to sync version as the underlying
        CachedOllamaEmbeddings doesn't support async operations.
        """
        return self.embed_query(text)
    
    def get_cache_stats(self) -> dict:
        """
        Get cache statistics from the underlying cache service.
        
        Returns:
            Dictionary with cache hit/miss statistics
        """
        try:
            from app.services.cache_service import cache_service
            return {
                "cache_hits": cache_service._cache_hits,
                "cache_misses": cache_service._cache_misses,
                "hit_rate": cache_service._cache_hits / (cache_service._cache_hits + cache_service._cache_misses) if (cache_service._cache_hits + cache_service._cache_misses) > 0 else 0
            }
        except Exception as e:
            logger.error(f"Error getting cache stats: {e}")
            return {}
    
    def clear_cache(self, text_pattern: Optional[str] = None) -> bool:
        """
        Clear cached embeddings.
        
        Args:
            text_pattern: Optional pattern to match for selective clearing
            
        Returns:
            True if successful, False otherwise
        """
        try:
            from app.services.cache_service import cache_service
            
            if text_pattern:
                # Selective clearing would require iterating through cache keys
                # This is a simplified implementation
                logger.warning("Selective cache clearing not implemented, clearing all cache")
            
            # Clear all cache (this affects the entire cache, not just embeddings)
            cache_service.clear()
            logger.info("Cleared embedding cache")
            return True
            
        except Exception as e:
            logger.error(f"Error clearing cache: {e}")
            return False
    
    def get_model_info(self) -> dict:
        """
        Get information about the current embedding model.
        
        Returns:
            Dictionary with model information
        """
        return {
            "model": self.model,
            "base_url": self.base_url,
            "cache_ttl": self.cache_ttl,
            "provider": "ollama"
        }
    
    def test_connection(self) -> bool:
        """
        Test connection to Ollama server.
        
        Returns:
            True if connection successful, False otherwise
        """
        try:
            # Test with a simple embedding
            test_embedding = self.embed_query("test")
            return len(test_embedding) > 0
        except Exception as e:
            logger.error(f"Connection test failed: {e}")
            return False


def get_langchain_ollama_embeddings(
    model: Optional[str] = None,
    base_url: Optional[str] = None,
    cache_ttl: int = 86400,
    **kwargs
) -> OllamaEmbeddingsWrapper:
    """
    Factory function to create LangChain-compatible Ollama embeddings.
    
    Args:
        model: Ollama model name (defaults to TEXT_EMBEDDING_MODEL)
        base_url: Ollama base URL (defaults to OLLAMA_BASE_URL)
        cache_ttl: Cache TTL in seconds (default: 24 hours)
        **kwargs: Additional arguments
        
    Returns:
        OllamaEmbeddingsWrapper instance
    """
    return OllamaEmbeddingsWrapper(
        model=model,
        base_url=base_url,
        cache_ttl=cache_ttl,
        **kwargs
    )


# Global instance for reuse
_langchain_ollama_embeddings = None

def get_default_langchain_embeddings() -> OllamaEmbeddingsWrapper:
    """
    Get the default LangChain Ollama embeddings instance.
    
    Returns:
        Default OllamaEmbeddingsWrapper instance
    """
    global _langchain_ollama_embeddings
    if _langchain_ollama_embeddings is None:
        _langchain_ollama_embeddings = get_langchain_ollama_embeddings()
    return _langchain_ollama_embeddings

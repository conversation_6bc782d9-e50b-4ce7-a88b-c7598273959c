"""
Context Assembler Service
Assembles final context including relevant images and tables alongside text
"""

import logging
from typing import Dict, Any, List, Optional, Tuple
from langchain.schema import Document
import base64

from config.multimodal_config import get_multimodal_config, ContentType
from app.services.multimodal_storage import get_multimodal_storage
from app.utils.performance_monitor import performance_monitor

logger = logging.getLogger(__name__)

class ContextAssembler:
    """Service for assembling multimodal context for responses"""
    
    def __init__(self, config=None):
        self.config = config or get_multimodal_config()
        self.storage = get_multimodal_storage()
        
    @performance_monitor(track_memory=True, track_cpu=True, log_parameters=True, log_result_size=True)
    def assemble_multimodal_context(self, search_results: Dict[str, Any], 
                                   query: str, max_context_length: int = 4000) -> Dict[str, Any]:
        """
        Assemble multimodal context from search results
        
        Args:
            search_results: Results from hybrid search
            query: Original search query
            max_context_length: Maximum length of text context
            
        Returns:
            Dictionary with assembled multimodal context
        """
        try:
            logger.info("Assembling multimodal context")
            
            merged_results = search_results.get("merged_results", [])
            if not merged_results:
                return self._create_empty_context(query)
            
            # Group results by content type
            results_by_type = self._group_results_by_type(merged_results)
            
            # Assemble text context
            text_context = self._assemble_text_context(
                results_by_type.get(ContentType.TEXT.value, []) + 
                results_by_type.get(ContentType.MIXED.value, []),
                max_context_length
            )
            
            # Assemble image context
            image_context = self._assemble_image_context(
                results_by_type.get(ContentType.IMAGE.value, [])
            )
            
            # Assemble table context
            table_context = self._assemble_table_context(
                results_by_type.get(ContentType.TABLE.value, [])
            )
            
            # Create spatial context map
            spatial_context = self._create_spatial_context_map(merged_results)
            
            # Assemble final context
            assembled_context = {
                "query": query,
                "text_context": text_context,
                "image_context": image_context,
                "table_context": table_context,
                "spatial_context": spatial_context,
                "context_metadata": {
                    "total_sources": len(merged_results),
                    "content_types": list(results_by_type.keys()),
                    "text_length": len(text_context.get("combined_text", "")),
                    "num_images": len(image_context.get("images", [])),
                    "num_tables": len(table_context.get("tables", [])),
                    "multimodal": True
                }
            }
            
            logger.info("Multimodal context assembly completed")
            return assembled_context
            
        except Exception as e:
            logger.error(f"Error assembling multimodal context: {e}")
            return self._create_empty_context(query)
    
    def _group_results_by_type(self, results: List[Document]) -> Dict[str, List[Document]]:
        """Group search results by content type"""
        grouped = {}
        
        for doc in results:
            content_type = doc.metadata.get("content_type", ContentType.TEXT.value)
            if content_type not in grouped:
                grouped[content_type] = []
            grouped[content_type].append(doc)
        
        return grouped
    
    def _assemble_text_context(self, text_docs: List[Document], 
                              max_length: int) -> Dict[str, Any]:
        """Assemble text context from documents"""
        try:
            if not text_docs:
                return {"combined_text": "", "sources": [], "truncated": False}
            
            # Sort by relevance score
            sorted_docs = sorted(
                text_docs, 
                key=lambda x: x.metadata.get("weighted_score", 0), 
                reverse=True
            )
            
            combined_text = ""
            sources = []
            current_length = 0
            truncated = False
            
            for doc in sorted_docs:
                doc_text = doc.page_content
                doc_length = len(doc_text)
                
                # Check if adding this document would exceed max length
                if current_length + doc_length > max_length:
                    # Try to add a truncated version
                    remaining_space = max_length - current_length
                    if remaining_space > 100:  # Only add if meaningful space remains
                        truncated_text = doc_text[:remaining_space - 3] + "..."
                        combined_text += f"\n\n{truncated_text}"
                        truncated = True
                    break
                
                combined_text += f"\n\n{doc_text}"
                current_length += doc_length
                
                # Add source information
                source_info = {
                    "document_id": doc.metadata.get("document_id"),
                    "page_num": doc.metadata.get("page_num"),
                    "content_type": doc.metadata.get("content_type"),
                    "relevance_score": doc.metadata.get("relevance_score", 0)
                }
                sources.append(source_info)
            
            return {
                "combined_text": combined_text.strip(),
                "sources": sources,
                "truncated": truncated,
                "total_length": len(combined_text)
            }
            
        except Exception as e:
            logger.error(f"Error assembling text context: {e}")
            return {"combined_text": "", "sources": [], "truncated": False}
    
    def _assemble_image_context(self, image_docs: List[Document]) -> Dict[str, Any]:
        """Assemble image context from documents"""
        try:
            if not image_docs:
                return {"images": [], "descriptions": []}
            
            # Sort by relevance score
            sorted_docs = sorted(
                image_docs,
                key=lambda x: x.metadata.get("weighted_score", 0),
                reverse=True
            )
            
            images = []
            descriptions = []
            
            for doc in sorted_docs:
                content_hash = doc.metadata.get("content_hash")
                if not content_hash:
                    continue
                
                # Get image data from storage
                image_data = self.storage.retrieve_image(content_hash)
                if not image_data:
                    continue
                
                # Prepare image information
                image_info = {
                    "content_hash": content_hash,
                    "description": doc.page_content,
                    "metadata": {
                        "page_num": doc.metadata.get("page_num"),
                        "image_index": doc.metadata.get("image_index"),
                        "format": doc.metadata.get("image_format"),
                        "dimensions": doc.metadata.get("image_dimensions", {}),
                        "relevance_score": doc.metadata.get("relevance_score", 0)
                    }
                }
                
                # Add base64 encoded image if requested
                if self.config.search.include_content_in_results:
                    try:
                        image_bytes = image_data["data"]
                        image_base64 = base64.b64encode(image_bytes).decode('utf-8')
                        image_info["image_data"] = image_base64
                        image_info["image_size"] = len(image_bytes)
                    except Exception as e:
                        logger.error(f"Error encoding image {content_hash}: {e}")
                
                images.append(image_info)
                descriptions.append(doc.page_content)
            
            return {
                "images": images,
                "descriptions": descriptions,
                "combined_descriptions": "\n\n".join(descriptions)
            }
            
        except Exception as e:
            logger.error(f"Error assembling image context: {e}")
            return {"images": [], "descriptions": []}
    
    def _assemble_table_context(self, table_docs: List[Document]) -> Dict[str, Any]:
        """Assemble table context from documents"""
        try:
            if not table_docs:
                return {"tables": [], "combined_content": ""}
            
            # Sort by relevance score
            sorted_docs = sorted(
                table_docs,
                key=lambda x: x.metadata.get("weighted_score", 0),
                reverse=True
            )
            
            tables = []
            combined_content = []
            
            for doc in sorted_docs:
                content_hash = doc.metadata.get("content_hash")
                if not content_hash:
                    continue
                
                # Get table data from storage
                table_data = self.storage.retrieve_table(content_hash)
                if not table_data:
                    continue
                
                data = table_data.get("data", {})
                
                # Prepare table information
                table_info = {
                    "content_hash": content_hash,
                    "markdown": data.get("markdown", ""),
                    "content": doc.page_content,
                    "metadata": {
                        "page_num": doc.metadata.get("page_num"),
                        "table_index": doc.metadata.get("table_index"),
                        "num_rows": doc.metadata.get("num_rows"),
                        "num_cols": doc.metadata.get("num_cols"),
                        "extraction_method": doc.metadata.get("extraction_method"),
                        "relevance_score": doc.metadata.get("relevance_score", 0)
                    }
                }
                
                # Add structured data if requested
                if self.config.search.include_content_in_results:
                    table_info["structured_data"] = data.get("structured_data", {})
                    table_info["context"] = data.get("context", "")
                
                tables.append(table_info)
                combined_content.append(doc.page_content)
            
            return {
                "tables": tables,
                "combined_content": "\n\n".join(combined_content)
            }
            
        except Exception as e:
            logger.error(f"Error assembling table context: {e}")
            return {"tables": [], "combined_content": ""}
    
    def _create_spatial_context_map(self, results: List[Document]) -> Dict[str, Any]:
        """Create a map of spatial relationships between content elements"""
        try:
            # Group by document and page
            by_document_page = {}
            
            for doc in results:
                document_id = doc.metadata.get("document_id", "unknown")
                page_num = doc.metadata.get("page_num", 1)
                
                key = f"{document_id}_page_{page_num}"
                if key not in by_document_page:
                    by_document_page[key] = {
                        "document_id": document_id,
                        "page_num": page_num,
                        "content_elements": []
                    }
                
                element = {
                    "content_type": doc.metadata.get("content_type"),
                    "content_hash": doc.metadata.get("content_hash"),
                    "bbox": doc.metadata.get("bbox", {}),
                    "relevance_score": doc.metadata.get("relevance_score", 0)
                }
                
                by_document_page[key]["content_elements"].append(element)
            
            # Sort elements by relevance within each page
            for page_info in by_document_page.values():
                page_info["content_elements"].sort(
                    key=lambda x: x["relevance_score"], reverse=True
                )
            
            return {
                "pages": list(by_document_page.values()),
                "total_pages": len(by_document_page),
                "spatial_relationships_available": any(
                    elem.get("bbox") for page in by_document_page.values()
                    for elem in page["content_elements"]
                )
            }
            
        except Exception as e:
            logger.error(f"Error creating spatial context map: {e}")
            return {"pages": [], "total_pages": 0}
    
    def _create_empty_context(self, query: str) -> Dict[str, Any]:
        """Create empty context structure"""
        return {
            "query": query,
            "text_context": {"combined_text": "", "sources": [], "truncated": False},
            "image_context": {"images": [], "descriptions": []},
            "table_context": {"tables": [], "combined_content": ""},
            "spatial_context": {"pages": [], "total_pages": 0},
            "context_metadata": {
                "total_sources": 0,
                "content_types": [],
                "text_length": 0,
                "num_images": 0,
                "num_tables": 0,
                "multimodal": False,
                "empty": True
            }
        }
    
    def format_context_for_llm(self, assembled_context: Dict[str, Any]) -> str:
        """Format assembled context for LLM consumption"""
        try:
            parts = []
            
            # Add text context
            text_context = assembled_context.get("text_context", {})
            combined_text = text_context.get("combined_text", "")
            if combined_text:
                parts.append(f"Text Content:\n{combined_text}")
            
            # Add image descriptions
            image_context = assembled_context.get("image_context", {})
            descriptions = image_context.get("combined_descriptions", "")
            if descriptions:
                parts.append(f"Image Descriptions:\n{descriptions}")
            
            # Add table content
            table_context = assembled_context.get("table_context", {})
            table_content = table_context.get("combined_content", "")
            if table_content:
                parts.append(f"Table Content:\n{table_content}")
            
            # Add metadata summary
            metadata = assembled_context.get("context_metadata", {})
            if metadata.get("multimodal"):
                summary_parts = []
                if metadata.get("num_images", 0) > 0:
                    summary_parts.append(f"{metadata['num_images']} images")
                if metadata.get("num_tables", 0) > 0:
                    summary_parts.append(f"{metadata['num_tables']} tables")
                
                if summary_parts:
                    parts.append(f"Additional Context: This response includes {', '.join(summary_parts)}.")
            
            return "\n\n".join(parts)
            
        except Exception as e:
            logger.error(f"Error formatting context for LLM: {e}")
            return assembled_context.get("text_context", {}).get("combined_text", "")

# Global context assembler instance
_context_assembler = None

def get_context_assembler() -> ContextAssembler:
    """Get the global context assembler instance"""
    global _context_assembler
    if _context_assembler is None:
        _context_assembler = ContextAssembler()
    return _context_assembler

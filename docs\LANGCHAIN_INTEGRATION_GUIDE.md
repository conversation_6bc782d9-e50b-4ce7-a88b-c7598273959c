# LangChain Integration Guide

This guide describes the comprehensive LangChain integration that provides full compatibility with the existing multimodal RAG infrastructure while maintaining all performance optimizations.

## Overview

The LangChain integration provides:

- **Multimodal Document Loading**: Extract text, images, and tables from PDFs using existing PyMuPDF infrastructure
- **Cached Ollama Embeddings**: LangChain-compatible wrapper around existing cached embeddings
- **Adaptive Text Splitting**: Semantic and sentence-based splitting with multimodal awareness
- **Unified Vector Storage**: ChromaDB integration with category filtering and multimodal support
- **Adaptive Retrieval**: Query complexity-based retrieval with configurable k values
- **Performance Monitoring**: Comprehensive monitoring across all components

## Architecture

### Component Structure

```
app/services/langchain_integration/
├── __init__.py                      # Module exports
├── multimodal_document_loader.py    # LangChain document loader
├── ollama_embeddings_wrapper.py     # Cached Ollama embeddings
├── multimodal_text_splitter.py      # Adaptive text splitter
├── chroma_vectorstore_wrapper.py    # ChromaDB vector store
└── adaptive_retriever.py            # Adaptive retriever
```

### Integration Points

The LangChain components integrate with existing infrastructure:

- **Document Processing**: Uses `multimodal_document_processor.py`
- **Caching**: Uses `cache_service.py` with Redis (30-minute TTL)
- **Vector Storage**: Uses existing unified ChromaDB
- **Performance Monitoring**: Uses `performance_monitor.py`
- **Threading**: Uses existing ThreadPoolExecutor (max_workers=4)

## Components

### 1. MultimodalDocumentLoader

LangChain-compatible document loader for multimodal content.

```python
from app.services.langchain_integration.multimodal_document_loader import MultimodalDocumentLoader

# Initialize loader
loader = MultimodalDocumentLoader(
    file_path="document.pdf",
    category="RESEARCH",
    include_images=True,
    include_tables=True,
    include_multimodal_chunks=True
)

# Load documents
documents = loader.load()
```

**Features:**
- Extracts text, images, and tables using existing PyMuPDF processing
- Returns LangChain Document objects with rich metadata
- Supports lazy loading for memory efficiency
- Handles image captions and vision analysis
- Processes table markdown and context

### 2. OllamaEmbeddingsWrapper

LangChain-compatible wrapper for cached Ollama embeddings.

```python
from app.services.langchain_integration.ollama_embeddings_wrapper import get_default_langchain_embeddings

# Get embeddings instance
embeddings = get_default_langchain_embeddings()

# Embed documents (uses existing cache)
doc_embeddings = embeddings.embed_documents(["text1", "text2"])

# Embed query (uses existing cache)
query_embedding = embeddings.embed_query("search query")

# Get cache statistics
cache_stats = embeddings.get_cache_stats()
```

**Features:**
- Uses existing cached Ollama embeddings infrastructure
- Maintains 24-hour cache TTL (configurable)
- LangChain Embeddings interface compatibility
- Performance monitoring integration
- Connection testing and model info

### 3. MultimodalTextSplitter

Adaptive text splitter with multimodal content awareness.

```python
from app.services.langchain_integration.multimodal_text_splitter import get_multimodal_text_splitter

# Create splitter
splitter = get_multimodal_text_splitter(
    chunk_size=800,
    chunk_overlap=200,
    chunking_strategy="adaptive",  # "adaptive", "semantic", "sentence", "fixed"
    preserve_multimodal_relationships=True
)

# Split documents
chunks = splitter.split_documents(documents)
```

**Chunking Strategies:**
- **Adaptive**: Uses existing enhanced chunking service logic
- **Semantic**: LlamaIndex semantic splitting with Ollama embeddings
- **Sentence**: Sentence-aware splitting with paragraph preservation
- **Fixed**: Character-based splitting with word boundaries

### 4. ChromaVectorStoreWrapper

LangChain vector store wrapper for unified ChromaDB.

```python
from app.services.langchain_integration.chroma_vectorstore_wrapper import get_chroma_vectorstore

# Create vector store
vectorstore = get_chroma_vectorstore(
    category="RESEARCH",
    embeddings=embeddings
)

# Add documents
doc_ids = vectorstore.add_documents(documents, category="RESEARCH")

# Search
results = vectorstore.similarity_search("query", k=10, category="RESEARCH")

# Search with scores
results_with_scores = vectorstore.similarity_search_with_score("query", k=5)

# Maximum Marginal Relevance
mmr_results = vectorstore.max_marginal_relevance_search("query", k=5)
```

**Features:**
- Uses existing unified ChromaDB infrastructure
- Category-based filtering and organization
- Multimodal content support
- Performance monitoring integration
- Collection statistics and management

### 5. AdaptiveRetriever

LangChain retriever with adaptive k values based on query complexity.

```python
from app.services.langchain_integration.adaptive_retriever import get_adaptive_retriever

# Create retriever
retriever = get_adaptive_retriever(
    vectorstore=vectorstore,
    base_k=12,
    category="RESEARCH",
    enable_multimodal=True
)

# Retrieve documents (k automatically adjusted)
documents = retriever.get_relevant_documents("What is this research about?")
```

**Adaptive K Values:**
- **Simple queries** (≤3 words): k = base_k × 0.67 (typically 8)
- **Medium queries** (4-10 words): k = base_k (typically 12)
- **Complex queries** (>10 words): k = base_k × 1.33 (typically 16)

**Multimodal Features:**
- Prioritizes multimodal chunks for visual queries
- Adjusts retrieval for image/table references
- Post-processes results based on content type

## Usage Examples

### Basic LangChain Pipeline

```python
from app.services.langchain_integration import (
    MultimodalDocumentLoader,
    get_default_langchain_embeddings,
    get_multimodal_text_splitter,
    get_chroma_vectorstore,
    get_adaptive_retriever
)

# 1. Load documents
loader = MultimodalDocumentLoader("document.pdf", category="RESEARCH")
documents = loader.load()

# 2. Initialize embeddings
embeddings = get_default_langchain_embeddings()

# 3. Split documents
splitter = get_multimodal_text_splitter(chunking_strategy="adaptive")
chunks = splitter.split_documents(documents)

# 4. Create vector store
vectorstore = get_chroma_vectorstore(category="RESEARCH", embeddings=embeddings)
vectorstore.add_documents(chunks)

# 5. Create retriever
retriever = get_adaptive_retriever(vectorstore, enable_multimodal=True)

# 6. Query
results = retriever.get_relevant_documents("What are the main findings?")
```

### Unified Service Usage

```python
from app.services.unified_rag_service import get_unified_rag_service, RAGFramework

# Initialize unified service
service = get_unified_rag_service(default_framework=RAGFramework.BOTH)

# Process document with both frameworks
result = service.process_document("document.pdf", category="RESEARCH")

# Query with specific framework
langchain_result = service.query(
    "What is this about?",
    framework=RAGFramework.LANGCHAIN
)

llamaindex_result = service.query(
    "What is this about?",
    framework=RAGFramework.LLAMAINDEX
)
```

## Performance Optimizations

### Caching Strategy

- **Embedding Cache**: 24-hour TTL with Redis backend
- **Query Cache**: 30-minute TTL for query results
- **Shared Cache**: Both frameworks use the same cache infrastructure

### Parallel Processing

- **ThreadPoolExecutor**: max_workers=4 for document processing
- **Batch Processing**: Optimized for large document sets
- **Adaptive Batching**: Adjusts batch size based on content

### Memory Management

- **Lazy Loading**: Documents loaded on-demand
- **Streaming**: Large documents processed in chunks
- **Cleanup**: Automatic cleanup of temporary resources

## Configuration

### Environment Variables

```bash
# Ollama Configuration
OLLAMA_BASE_URL=http://localhost:11434
TEXT_EMBEDDING_MODEL=mxbai-embed-large:latest

# ChromaDB Configuration
UNIFIED_CHROMA_PATH=./data/unified_chroma

# Redis Configuration
REDIS_URL=redis://localhost:6379/1

# Performance Configuration
RAG_MAX_PARALLEL_WORKERS=4
RAG_ENABLE_ADAPTIVE_RETRIEVAL=true
```

### Chunking Configuration

```python
# Content-type specific configurations
CHUNKING_CONFIGS = {
    "technical": {
        "chunk_size": 1000,
        "chunk_overlap": 300,
        "use_semantic": True,
        "strategy": "semantic"
    },
    "narrative": {
        "chunk_size": 1200,
        "chunk_overlap": 250,
        "strategy": "sentence_aware"
    }
}
```

## Monitoring and Analytics

### Performance Metrics

- **Retrieval Time**: Time to retrieve documents
- **Embedding Time**: Time to generate embeddings
- **Cache Hit Rate**: Percentage of cache hits
- **Memory Usage**: Memory consumption tracking
- **Query Complexity**: Automatic complexity analysis

### Logging

```python
import logging

# Enable debug logging for detailed information
logging.getLogger('app.services.langchain_integration').setLevel(logging.DEBUG)
```

## Best Practices

### Document Processing

1. **Use appropriate categories** for better organization
2. **Enable multimodal processing** for rich content
3. **Configure chunking strategy** based on content type
4. **Monitor cache performance** for optimization

### Retrieval Optimization

1. **Use adaptive retrieval** for automatic k adjustment
2. **Enable MMR** for diverse results when needed
3. **Filter by category** for focused searches
4. **Monitor query complexity** for performance tuning

### Error Handling

1. **Implement fallback strategies** for component failures
2. **Monitor connection health** to external services
3. **Use graceful degradation** when features unavailable
4. **Log errors comprehensively** for debugging

## Troubleshooting

### Common Issues

1. **Ollama Connection**: Ensure Ollama server is running
2. **Redis Connection**: Verify Redis server availability
3. **ChromaDB Permissions**: Check file system permissions
4. **Memory Issues**: Monitor memory usage with large documents

### Debug Commands

```python
# Test embeddings connection
embeddings = get_default_langchain_embeddings()
success = embeddings.test_connection()

# Check vector store stats
vectorstore = get_chroma_vectorstore()
stats = vectorstore.get_collection_stats()

# Monitor cache performance
cache_stats = embeddings.get_cache_stats()
```

## Migration Guide

### From Existing RAG System

The LangChain integration is designed to work alongside the existing system:

1. **No data migration required** - uses existing ChromaDB
2. **Shared embeddings cache** - leverages existing cache
3. **Compatible configurations** - respects existing settings
4. **Gradual adoption** - can be introduced incrementally

### Framework Comparison

| Feature | LlamaIndex | LangChain | Unified Service |
|---------|------------|-----------|-----------------|
| Document Loading | ✅ | ✅ | ✅ |
| Semantic Chunking | ✅ | ✅ | ✅ |
| Cached Embeddings | ✅ | ✅ | ✅ |
| Adaptive Retrieval | ✅ | ✅ | ✅ |
| Multimodal Support | ✅ | ✅ | ✅ |
| Performance Monitoring | ✅ | ✅ | ✅ |

## Next Steps

1. **Run Examples**: Start with `examples/langchain_multimodal_example.py`
2. **Test Integration**: Use `examples/unified_rag_example.py`
3. **Monitor Performance**: Check cache hit rates and retrieval times
4. **Optimize Configuration**: Adjust parameters based on your use case
5. **Scale Gradually**: Introduce LangChain components incrementally

{"structured_data": {"rows": [[0, 1, 2], ["Alarm system", "Warning system", "Forecasting system"], ["Fully automated\nDetect ongoing process parameters\nShort lead times\nThresholds  serve as decision \ninstance\nAutomated intervention measures \nsuch as automated barriers on roads \nor interrupted power lines at railways", "Partly automated\nMonitor precursors\nExtended lead times\nFirst decision is \nbased on threshold, \nthe final one is made \nby experts\nOrganized \nintervention \nactions such as an \nevacuation", "Lowest degree of automation\nMonitor precursors\nExtended  lead times\nExperts conduct analysis i n \nregular intervals and not \nbased on thresholds\nForecast the danger level for \npredefined warning regions \nto enable preventive actions \nand preparation"]], "num_rows": 3, "num_cols": 3, "accuracy": 100.0}, "markdown": "| 0 | 1 | 2 |\n| --- | --- | --- |\n| Alarm system | Warning system | Forecasting system |\n| Fully automated\nDetect ongoing process parameters\nShort lead times\nThresholds  serve as decision \ninstance\nAutomated intervention measures \nsuch as automated barriers on roads \nor interrupted power lines at railways | Partly automated\nMonitor precursors\nExtended lead times\nFirst decision is \nbased on threshold, \nthe final one is made \nby experts\nOrganized \nintervention \nactions such as an \nevacuation | Lowest degree of automation\nMonitor precursors\nExtended  lead times\nExperts conduct analysis i n \nregular intervals and not \nbased on thresholds\nForecast the danger level for \npredefined warning regions \nto enable preventive actions \nand preparation |", "dataframe_info": {"shape": [2, 3], "columns": [0, 1, 2]}}
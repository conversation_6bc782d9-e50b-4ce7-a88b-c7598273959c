"""
Multimodal Embedding Service
Handles embedding generation for different content types with existing Ollama integration
"""

import logging
from typing import Dict, Any, List, Optional, Union
from langchain.schema import Document

from config.multimodal_config import get_multimodal_config, ContentType
from app.services.vector_db import CachedOllamaEmbeddings, get_vector_db, add_documents_with_category
from app.services.cache_service import cache_service
from app.utils.performance_monitor import performance_monitor

logger = logging.getLogger(__name__)

class MultimodalEmbeddingService:
    """Service for generating embeddings for multimodal content"""
    
    def __init__(self, config=None):
        self.config = config or get_multimodal_config()
        self.text_embeddings = None
        self._initialize_embedding_models()
        
    def _initialize_embedding_models(self):
        """Initialize embedding models"""
        try:
            # Use existing cached Ollama embeddings
            from app.services.vector_db import get_embedding_function
            self.text_embeddings = get_embedding_function()
            logger.info("Initialized multimodal embedding service with existing Ollama embeddings")
        except Exception as e:
            logger.error(f"Error initializing embedding models: {e}")
            self.text_embeddings = None
    
    @performance_monitor(track_memory=True, track_cpu=True, log_parameters=True, log_result_size=True)
    def embed_multimodal_documents(self, documents: List[Document], 
                                  category: Optional[str] = None) -> Dict[str, Any]:
        """
        Generate embeddings for multimodal documents and store in vector database
        
        Args:
            documents: List of LangChain documents with multimodal metadata
            category: Category for organizing content
            
        Returns:
            Dictionary with embedding results and statistics
        """
        if not self.text_embeddings:
            logger.error("Embedding models not initialized")
            return {"error": "Embedding models not available"}
        
        try:
            logger.info(f"Generating embeddings for {len(documents)} multimodal documents")
            
            # Group documents by content type
            documents_by_type = self._group_documents_by_content_type(documents)
            
            results = {
                "total_documents": len(documents),
                "documents_by_type": {k: len(v) for k, v in documents_by_type.items()},
                "embedding_results": {},
                "storage_results": {}
            }
            
            # Process each content type
            for content_type, type_documents in documents_by_type.items():
                if not type_documents:
                    continue
                
                logger.info(f"Processing {len(type_documents)} {content_type} documents")
                
                # Generate embeddings based on content type
                embedding_result = self._embed_documents_by_type(
                    type_documents, content_type, category
                )
                
                results["embedding_results"][content_type] = embedding_result
                
                # Store in vector database if separate collections are enabled
                if self.config.embedding.separate_collections:
                    storage_result = self._store_in_separate_collection(
                        type_documents, content_type, category
                    )
                    results["storage_results"][content_type] = storage_result
            
            # Store all documents in main collection if not using separate collections
            if not self.config.embedding.separate_collections:
                storage_result = self._store_in_main_collection(documents, category)
                results["storage_results"]["main"] = storage_result
            
            logger.info("Multimodal embedding generation completed")
            return results
            
        except Exception as e:
            logger.error(f"Error in multimodal embedding generation: {e}")
            return {"error": str(e)}
    
    def _group_documents_by_content_type(self, documents: List[Document]) -> Dict[str, List[Document]]:
        """Group documents by their content type"""
        grouped = {
            ContentType.TEXT.value: [],
            ContentType.IMAGE.value: [],
            ContentType.TABLE.value: [],
            ContentType.MIXED.value: []
        }
        
        for doc in documents:
            content_type = doc.metadata.get("content_type", ContentType.TEXT.value)
            if content_type in grouped:
                grouped[content_type].append(doc)
            else:
                # Default to text for unknown types
                grouped[ContentType.TEXT.value].append(doc)
        
        return grouped
    
    def _embed_documents_by_type(self, documents: List[Document], 
                                content_type: str, category: Optional[str]) -> Dict[str, Any]:
        """Generate embeddings for documents of a specific content type"""
        try:
            if content_type == ContentType.TEXT.value:
                return self._embed_text_documents(documents, category)
            elif content_type == ContentType.IMAGE.value:
                return self._embed_image_documents(documents, category)
            elif content_type == ContentType.TABLE.value:
                return self._embed_table_documents(documents, category)
            elif content_type == ContentType.MIXED.value:
                return self._embed_mixed_documents(documents, category)
            else:
                logger.warning(f"Unknown content type: {content_type}")
                return self._embed_text_documents(documents, category)
                
        except Exception as e:
            logger.error(f"Error embedding {content_type} documents: {e}")
            return {"error": str(e), "processed": 0}
    
    def _embed_text_documents(self, documents: List[Document], 
                             category: Optional[str]) -> Dict[str, Any]:
        """Generate embeddings for text documents"""
        try:
            # Use existing text embedding functionality
            processed = 0
            for doc in documents:
                # The embedding will be generated when stored in vector DB
                processed += 1
            
            return {
                "content_type": ContentType.TEXT.value,
                "processed": processed,
                "method": "ollama_text_embeddings"
            }
            
        except Exception as e:
            logger.error(f"Error embedding text documents: {e}")
            return {"error": str(e), "processed": 0}
    
    def _embed_image_documents(self, documents: List[Document], 
                              category: Optional[str]) -> Dict[str, Any]:
        """Generate embeddings for image description documents"""
        try:
            # Image descriptions are treated as text for embedding
            # The vision model has already generated the textual description
            processed = 0
            
            for doc in documents:
                # Enhance the document content with image metadata for better embeddings
                enhanced_content = self._enhance_image_content_for_embedding(doc)
                doc.page_content = enhanced_content
                processed += 1
            
            return {
                "content_type": ContentType.IMAGE.value,
                "processed": processed,
                "method": "text_embeddings_of_image_descriptions"
            }
            
        except Exception as e:
            logger.error(f"Error embedding image documents: {e}")
            return {"error": str(e), "processed": 0}
    
    def _embed_table_documents(self, documents: List[Document], 
                              category: Optional[str]) -> Dict[str, Any]:
        """Generate embeddings for table documents"""
        try:
            # Tables are embedded as text (markdown or structured text)
            processed = 0
            
            for doc in documents:
                # Enhance table content with metadata for better embeddings
                enhanced_content = self._enhance_table_content_for_embedding(doc)
                doc.page_content = enhanced_content
                processed += 1
            
            return {
                "content_type": ContentType.TABLE.value,
                "processed": processed,
                "method": "text_embeddings_of_table_content"
            }
            
        except Exception as e:
            logger.error(f"Error embedding table documents: {e}")
            return {"error": str(e), "processed": 0}
    
    def _embed_mixed_documents(self, documents: List[Document], 
                              category: Optional[str]) -> Dict[str, Any]:
        """Generate embeddings for mixed content documents"""
        try:
            # Mixed content is embedded as combined text
            processed = 0
            
            for doc in documents:
                # The content is already combined in the chunking service
                # Just ensure it's properly formatted
                processed += 1
            
            return {
                "content_type": ContentType.MIXED.value,
                "processed": processed,
                "method": "text_embeddings_of_combined_content"
            }
            
        except Exception as e:
            logger.error(f"Error embedding mixed documents: {e}")
            return {"error": str(e), "processed": 0}
    
    def _enhance_image_content_for_embedding(self, doc: Document) -> str:
        """Enhance image content with metadata for better embeddings"""
        try:
            content = doc.page_content
            metadata = doc.metadata
            
            # Add image metadata to improve embedding quality
            enhancements = []
            
            # Add image format and dimensions
            image_format = metadata.get("image_format", "")
            if image_format:
                enhancements.append(f"Image format: {image_format}")
            
            dimensions = metadata.get("image_dimensions", {})
            if dimensions:
                width = dimensions.get("width", "")
                height = dimensions.get("height", "")
                if width and height:
                    enhancements.append(f"Image size: {width}x{height}")
            
            # Add vision analysis results
            vision_analysis = metadata.get("vision_analysis", {})
            if vision_analysis:
                model = vision_analysis.get("model", "")
                if model:
                    enhancements.append(f"Analyzed by: {model}")
            
            if enhancements:
                enhanced_content = content + "\n\nImage metadata: " + "; ".join(enhancements)
                return enhanced_content
            
            return content
            
        except Exception as e:
            logger.error(f"Error enhancing image content: {e}")
            return doc.page_content
    
    def _enhance_table_content_for_embedding(self, doc: Document) -> str:
        """Enhance table content with metadata for better embeddings"""
        try:
            content = doc.page_content
            metadata = doc.metadata
            
            # Add table metadata to improve embedding quality
            enhancements = []
            
            # Add table dimensions
            num_rows = metadata.get("num_rows", "")
            num_cols = metadata.get("num_cols", "")
            if num_rows and num_cols:
                enhancements.append(f"Table size: {num_rows} rows x {num_cols} columns")
            
            # Add extraction method
            extraction_method = metadata.get("extraction_method", "")
            if extraction_method:
                enhancements.append(f"Extracted using: {extraction_method}")
            
            if enhancements:
                enhanced_content = content + "\n\nTable metadata: " + "; ".join(enhancements)
                return enhanced_content
            
            return content
            
        except Exception as e:
            logger.error(f"Error enhancing table content: {e}")
            return doc.page_content
    
    def _store_in_separate_collection(self, documents: List[Document], 
                                     content_type: str, category: Optional[str]) -> Dict[str, Any]:
        """Store documents in separate collection by content type"""
        try:
            # Create collection name with content type suffix
            collection_category = f"{category}_{content_type}" if category else content_type
            
            # Use existing vector DB functionality
            result = add_documents_with_category(documents, collection_category)
            
            return {
                "collection": collection_category,
                "documents_stored": len(documents),
                "success": True
            }
            
        except Exception as e:
            logger.error(f"Error storing {content_type} documents in separate collection: {e}")
            return {
                "collection": f"{category}_{content_type}" if category else content_type,
                "documents_stored": 0,
                "success": False,
                "error": str(e)
            }
    
    def _store_in_main_collection(self, documents: List[Document], 
                                 category: Optional[str]) -> Dict[str, Any]:
        """Store all documents in main collection"""
        try:
            # Use existing vector DB functionality
            result = add_documents_with_category(documents, category)
            
            return {
                "collection": category or "default",
                "documents_stored": len(documents),
                "success": True
            }
            
        except Exception as e:
            logger.error(f"Error storing documents in main collection: {e}")
            return {
                "collection": category or "default",
                "documents_stored": 0,
                "success": False,
                "error": str(e)
            }

# Global embedding service instance
_multimodal_embedding_service = None

def get_multimodal_embedding_service() -> MultimodalEmbeddingService:
    """Get the global multimodal embedding service instance"""
    global _multimodal_embedding_service
    if _multimodal_embedding_service is None:
        _multimodal_embedding_service = MultimodalEmbeddingService()
    return _multimodal_embedding_service

"""
Test suite for multimodal RAG pipeline
Comprehensive tests for multimodal document processing, chunking, embedding, and search
"""

import pytest
import os
import sys
import tempfile
import json
from unittest.mock import Mock, patch, MagicMock
from pathlib import Path

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from config.multimodal_config import MultimodalRAGConfig, get_multimodal_config
from app.services.multimodal_storage import MultimodalStorage
from app.services.multimodal_document_processor import MultimodalDocumentProcessor
from app.services.multimodal_chunking_service import MultimodalChunkingService
from app.services.multimodal_embedding_service import MultimodalEmbeddingService
from app.services.hybrid_search_service import HybridSearchService
from app.services.context_assembler import ContextAssembler

class TestMultimodalConfig:
    """Test multimodal configuration"""
    
    def test_default_config(self):
        """Test default configuration values"""
        config = MultimodalRAGConfig()
        
        assert config.enable_multimodal == True
        assert config.vision_model.model_name == "llama3.2-vision"
        assert config.image_processing.enable_image_extraction == True
        assert config.table_processing.enable_table_extraction == True
        assert config.chunking.enable_multimodal_chunking == True
        assert config.search.enable_hybrid_search == True
    
    def test_config_from_env(self):
        """Test configuration loading from environment variables"""
        with patch.dict(os.environ, {
            'MULTIMODAL_VISION_MODEL': 'test-vision-model',
            'MULTIMODAL_ENABLE_IMAGES': 'false',
            'MULTIMODAL_MAX_WORKERS': '8'
        }):
            from config.multimodal_config import load_multimodal_config_from_env
            config = load_multimodal_config_from_env()
            
            assert config.vision_model.model_name == 'test-vision-model'
            assert config.image_processing.enable_image_extraction == False
            assert config.max_workers == 8

class TestMultimodalStorage:
    """Test multimodal storage functionality"""
    
    @pytest.fixture
    def temp_storage(self):
        """Create temporary storage for testing"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config = MultimodalRAGConfig()
            config.storage.blob_storage_path = temp_dir
            storage = MultimodalStorage(config)
            yield storage
    
    def test_store_and_retrieve_image(self, temp_storage):
        """Test image storage and retrieval"""
        # Create test image data
        test_image_data = b"fake_image_data_for_testing"
        test_metadata = {
            "format": "png",
            "width": 100,
            "height": 100
        }
        
        # Store image
        content_hash = temp_storage.store_image(
            test_image_data, test_metadata, "test_doc", 1
        )
        
        assert content_hash is not None
        
        # Retrieve image
        retrieved = temp_storage.retrieve_image(content_hash)
        
        assert retrieved is not None
        assert retrieved["data"] == test_image_data
        assert retrieved["metadata"]["format"] == "png"
    
    def test_store_and_retrieve_table(self, temp_storage):
        """Test table storage and retrieval"""
        # Create test table data
        test_table_data = {
            "structured_data": {
                "rows": [["Header1", "Header2"], ["Data1", "Data2"]],
                "num_rows": 2,
                "num_cols": 2
            },
            "markdown": "| Header1 | Header2 |\n|---------|----------|\n| Data1 | Data2 |"
        }
        test_metadata = {
            "extraction_method": "test",
            "num_rows": 2,
            "num_cols": 2
        }
        
        # Store table
        content_hash = temp_storage.store_table(
            test_table_data, test_metadata, "test_doc", 1
        )
        
        assert content_hash is not None
        
        # Retrieve table
        retrieved = temp_storage.retrieve_table(content_hash)
        
        assert retrieved is not None
        assert retrieved["data"]["markdown"] == test_table_data["markdown"]
        assert retrieved["metadata"]["num_rows"] == 2

class TestMultimodalDocumentProcessor:
    """Test multimodal document processor"""
    
    @pytest.fixture
    def mock_processor(self):
        """Create mock processor for testing"""
        config = MultimodalRAGConfig()
        config.enable_multimodal = True
        processor = MultimodalDocumentProcessor(config)
        return processor
    
    def test_document_id_generation(self, mock_processor):
        """Test document ID generation"""
        test_path = "/test/path/document.pdf"
        doc_id = mock_processor._generate_document_id(test_path)
        
        assert doc_id is not None
        assert len(doc_id) == 32  # MD5 hash length
    
    def test_document_format_detection(self, mock_processor):
        """Test document format detection"""
        assert mock_processor._detect_document_format("test.pdf") == "pdf"
        assert mock_processor._detect_document_format("test.docx") == "docx"
        assert mock_processor._detect_document_format("test.doc") == "doc"
        assert mock_processor._detect_document_format("test.txt") == "unknown"
    
    @patch('app.services.multimodal_document_processor.extract_text_with_rag')
    @patch('app.services.multimodal_image_processor.MultimodalImageProcessor.extract_images_from_pdf')
    @patch('app.services.multimodal_table_processor.MultimodalTableProcessor.extract_tables_from_pdf')
    def test_process_pdf_document(self, mock_tables, mock_images, mock_text, mock_processor):
        """Test PDF document processing"""
        # Setup mocks
        mock_text.return_value = [{"page": 1, "text": "Test content"}]
        mock_images.return_value = [{"content_hash": "img123", "page_num": 1}]
        mock_tables.return_value = [{"content_hash": "table123", "page_num": 1}]
        
        # Test processing
        with tempfile.NamedTemporaryFile(suffix=".pdf") as temp_file:
            result = mock_processor.process_document(temp_file.name, "TEST")
            
            assert result["document_format"] == "pdf"
            assert len(result["text_content"]) == 1
            assert len(result["images"]) == 1
            assert len(result["tables"]) == 1

class TestMultimodalChunkingService:
    """Test multimodal chunking service"""
    
    @pytest.fixture
    def mock_chunking_service(self):
        """Create mock chunking service for testing"""
        config = MultimodalRAGConfig()
        service = MultimodalChunkingService(config)
        return service
    
    def test_multimodal_chunk_creation(self, mock_chunking_service):
        """Test multimodal chunk creation"""
        # Create test multimodal result
        test_result = {
            "document_id": "test123",
            "multimodal_chunks": [
                {
                    "page_num": 1,
                    "content_types": ["text", "image"],
                    "text_content": "Test page content",
                    "images": [{"content_hash": "img123", "caption": "Test image"}],
                    "tables": []
                }
            ]
        }
        
        # Mock storage
        with patch.object(mock_chunking_service, 'storage') as mock_storage:
            mock_storage.retrieve_image.return_value = {
                "metadata": {"caption": "Test image"}
            }
            
            documents = mock_chunking_service.chunk_multimodal_document(test_result)
            
            assert len(documents) > 0
            # Should have text and image documents
            content_types = [doc.metadata.get("content_type") for doc in documents]
            assert "text" in content_types
            assert "image" in content_types

class TestHybridSearchService:
    """Test hybrid search service"""
    
    @pytest.fixture
    def mock_search_service(self):
        """Create mock search service for testing"""
        config = MultimodalRAGConfig()
        service = HybridSearchService(config)
        return service
    
    @patch('app.services.hybrid_search_service.similarity_search_with_category_filter')
    def test_multimodal_search(self, mock_search, mock_search_service):
        """Test multimodal search functionality"""
        # Setup mock search results
        from langchain.schema import Document
        
        mock_results = [
            Document(
                page_content="Test text content",
                metadata={"content_type": "text", "relevance_score": 0.8}
            ),
            Document(
                page_content="Image description: Test image",
                metadata={"content_type": "image", "relevance_score": 0.7}
            )
        ]
        mock_search.return_value = mock_results
        
        # Perform search
        results = mock_search_service.search_multimodal("test query", "TEST", k=5)
        
        assert results is not None
        assert "merged_results" in results
        assert results["search_metadata"]["hybrid_search"] == True
    
    def test_relevance_score_calculation(self, mock_search_service):
        """Test relevance score calculation"""
        from langchain.schema import Document
        
        # Test image content with visual query
        image_doc = Document(
            page_content="Image description",
            metadata={"content_type": "image"}
        )
        score = mock_search_service._calculate_relevance_score(image_doc, "show me the image")
        assert score > 0.5  # Should get boost for visual query
        
        # Test table content with data query
        table_doc = Document(
            page_content="Table content",
            metadata={"content_type": "table"}
        )
        score = mock_search_service._calculate_relevance_score(table_doc, "show me the data")
        assert score > 0.5  # Should get boost for data query

class TestContextAssembler:
    """Test context assembler"""
    
    @pytest.fixture
    def mock_assembler(self):
        """Create mock context assembler for testing"""
        config = MultimodalRAGConfig()
        assembler = ContextAssembler(config)
        return assembler
    
    def test_context_assembly(self, mock_assembler):
        """Test multimodal context assembly"""
        from langchain.schema import Document
        
        # Create test search results
        search_results = {
            "merged_results": [
                Document(
                    page_content="Test text content",
                    metadata={
                        "content_type": "text",
                        "document_id": "test123",
                        "page_num": 1,
                        "weighted_score": 0.8
                    }
                ),
                Document(
                    page_content="Image description: Test image",
                    metadata={
                        "content_type": "image",
                        "content_hash": "img123",
                        "document_id": "test123",
                        "page_num": 1,
                        "weighted_score": 0.7
                    }
                )
            ]
        }
        
        # Mock storage
        with patch.object(mock_assembler, 'storage') as mock_storage:
            mock_storage.retrieve_image.return_value = {
                "data": b"fake_image_data",
                "metadata": {"caption": "Test image"}
            }
            
            context = mock_assembler.assemble_multimodal_context(
                search_results, "test query"
            )
            
            assert context is not None
            assert "text_context" in context
            assert "image_context" in context
            assert context["context_metadata"]["multimodal"] == True
    
    def test_context_formatting_for_llm(self, mock_assembler):
        """Test context formatting for LLM"""
        test_context = {
            "text_context": {"combined_text": "Test text content"},
            "image_context": {"combined_descriptions": "Test image description"},
            "table_context": {"combined_content": "Test table content"},
            "context_metadata": {"multimodal": True, "num_images": 1, "num_tables": 1}
        }
        
        formatted = mock_assembler.format_context_for_llm(test_context)
        
        assert "Text Content:" in formatted
        assert "Image Descriptions:" in formatted
        assert "Table Content:" in formatted
        assert "Additional Context:" in formatted

class TestIntegration:
    """Integration tests for the complete multimodal pipeline"""
    
    @pytest.fixture
    def temp_config(self):
        """Create temporary configuration for integration tests"""
        with tempfile.TemporaryDirectory() as temp_dir:
            config = MultimodalRAGConfig()
            config.storage.blob_storage_path = temp_dir
            config.enable_multimodal = True
            config.debug_mode = True
            yield config
    
    def test_end_to_end_pipeline(self, temp_config):
        """Test complete end-to-end multimodal pipeline"""
        # This test would require actual PDF files and vision models
        # For now, we'll test the pipeline structure
        
        # Initialize services
        processor = MultimodalDocumentProcessor(temp_config)
        chunking_service = MultimodalChunkingService(temp_config)
        embedding_service = MultimodalEmbeddingService(temp_config)
        search_service = HybridSearchService(temp_config)
        assembler = ContextAssembler(temp_config)
        
        # Verify all services are properly initialized
        assert processor is not None
        assert chunking_service is not None
        assert embedding_service is not None
        assert search_service is not None
        assert assembler is not None
        
        # Test configuration consistency
        assert processor.config.enable_multimodal == True
        assert chunking_service.multimodal_config.enable_multimodal == True
        assert search_service.config.enable_multimodal == True

if __name__ == "__main__":
    pytest.main([__file__, "-v"])

{"structured_data": {"rows": [[0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14], ["", "lution tolerance (1-10)\nPol\nTypes of macroinvertebrates based \non pollution-tolerance values\nGroup 3 taxa or pollution-tolerant organisms\n10\nMidge fly larva \nQuilted melania \n(Chironomidae)\n(Thiaridae)\nGroup 2 taxa or somewhat pollution-tolerant organisms\nPyralidae\nBlack fly larvae\nAsian clams\n(Simuliidae)\n(Corbiculidae)\nGroup 1 taxa or pollution-sensitive organisms\nCaddisfly larvae\nMayfly nymph\nWater-penny\n(Glossosotimadae)\n(Heptageniidae)\n(Psephenidae)\n0", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", ""], ["", "", "", "", "", "", "", "", "", "", "", "", "", "", ""]], "num_rows": 9, "num_cols": 15, "accuracy": 66.06569389285576}, "markdown": "| 0 | 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9 | 10 | 11 | 12 | 13 | 14 |\n| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |\n|  | lution tolerance (1-10)\nPol\nTypes of macroinvertebrates based \non pollution-tolerance values\nGroup 3 taxa or pollution-tolerant organisms\n10\nMidge fly larva \nQuilted melania \n(Chironomidae)\n(Thiaridae)\nGroup 2 taxa or somewhat pollution-tolerant organisms\nPyralidae\nBlack fly larvae\nAsian clams\n(Simuliidae)\n(Corbiculidae)\nGroup 1 taxa or pollution-sensitive organisms\nCaddisfly larvae\nMayfly nymph\nWater-penny\n(Glossosotimadae)\n(Heptageniidae)\n(Psephenidae)\n0 |  |  |  |  |  |  |  |  |  |  |  |  |  |\n|  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |\n|  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |\n|  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |\n|  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |\n|  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |\n|  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |\n|  |  |  |  |  |  |  |  |  |  |  |  |  |  |  |", "dataframe_info": {"shape": [8, 15], "columns": [0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14]}}
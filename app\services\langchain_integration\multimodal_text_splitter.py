"""
LangChain MultimodalTextSplitter

LangChain-compatible text splitter that integrates with the existing
enhanced chunking service, supporting semantic and sentence splitting
with multimodal content awareness.
"""

import logging
from typing import List, Dict, Any, Optional, Iterable

from langchain_core.documents import Document
from langchain_text_splitters.base import TextSplitter

from app.services.enhanced_chunking_service import get_enhanced_chunking_service
from app.services.content_type_detector import ContentTypeDetector
from app.utils.performance_monitor import performance_monitor

logger = logging.getLogger(__name__)


class MultimodalTextSplitter(TextSplitter):
    """
    LangChain-compatible text splitter with multimodal content awareness.
    
    This splitter integrates with the existing enhanced chunking service
    to provide semantic and sentence-based splitting while maintaining
    awareness of multimodal content relationships.
    """
    
    def __init__(
        self,
        chunk_size: int = 800,
        chunk_overlap: int = 200,
        content_type: Optional[str] = None,
        chunking_strategy: str = "adaptive",  # "adaptive", "semantic", "sentence", "fixed"
        preserve_multimodal_relationships: bool = True,
        enable_parallel_processing: bool = True,
        **kwargs
    ):
        """
        Initialize the multimodal text splitter.
        
        Args:
            chunk_size: Maximum size of each chunk
            chunk_overlap: Overlap between consecutive chunks
            content_type: Type of content for optimized chunking
            chunking_strategy: Strategy to use for chunking
            preserve_multimodal_relationships: Whether to preserve relationships with images/tables
            enable_parallel_processing: Whether to use parallel processing for large documents
            **kwargs: Additional arguments passed to parent class
        """
        super().__init__(
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap,
            **kwargs
        )
        
        self.content_type = content_type
        self.chunking_strategy = chunking_strategy
        self.preserve_multimodal_relationships = preserve_multimodal_relationships
        self.enable_parallel_processing = enable_parallel_processing
        
        # Initialize services
        self.chunking_service = get_enhanced_chunking_service()
        self.content_detector = ContentTypeDetector()
        
        logger.info(f"Initialized MultimodalTextSplitter with strategy: {chunking_strategy}")
    
    @performance_monitor(track_memory=True, track_cpu=True, log_parameters=True, log_result_size=True)
    def split_text(self, text: str) -> List[str]:
        """
        Split text into chunks using the configured strategy.
        
        Args:
            text: Text to split
            
        Returns:
            List of text chunks
        """
        try:
            # Create a temporary document for processing
            doc = Document(
                page_content=text,
                metadata={
                    "content_type": self.content_type or "general",
                    "chunking_strategy": self.chunking_strategy
                }
            )
            
            # Split the document
            split_docs = self.split_documents([doc])
            
            # Extract text content
            return [doc.page_content for doc in split_docs]
            
        except Exception as e:
            logger.error(f"Error splitting text: {e}")
            # Fallback to simple splitting
            return self._fallback_split_text(text)
    
    @performance_monitor(track_memory=True, track_cpu=True, log_parameters=True, log_result_size=True)
    def split_documents(self, documents: List[Document]) -> List[Document]:
        """
        Split documents using the enhanced chunking service.
        
        Args:
            documents: List of documents to split
            
        Returns:
            List of split documents with enhanced metadata
        """
        try:
            if not documents:
                return []
            
            logger.debug(f"Splitting {len(documents)} documents with strategy: {self.chunking_strategy}")
            
            # Detect content type if not specified
            content_type = self._determine_content_type(documents)
            
            # Apply chunking strategy
            if self.chunking_strategy == "adaptive":
                chunks = self._adaptive_split(documents, content_type)
            elif self.chunking_strategy == "semantic":
                chunks = self._semantic_split(documents, content_type)
            elif self.chunking_strategy == "sentence":
                chunks = self._sentence_split(documents, content_type)
            else:  # fixed
                chunks = self._fixed_split(documents, content_type)
            
            # Enhance metadata for multimodal awareness
            enhanced_chunks = self._enhance_chunk_metadata(chunks, documents)
            
            logger.debug(f"Split {len(documents)} documents into {len(enhanced_chunks)} chunks")
            return enhanced_chunks
            
        except Exception as e:
            logger.error(f"Error splitting documents: {e}")
            # Fallback to simple splitting
            return self._fallback_split_documents(documents)
    
    def _determine_content_type(self, documents: List[Document]) -> str:
        """Determine content type from documents."""
        if self.content_type:
            return self.content_type
        
        try:
            # Sample text from documents for content type detection
            sample_text = ""
            for doc in documents[:3]:  # Sample first 3 documents
                sample_text += doc.page_content[:500] + " "
            
            detected_type = self.content_detector.detect_content_type(sample_text)
            logger.debug(f"Detected content type: {detected_type}")
            return detected_type
            
        except Exception as e:
            logger.error(f"Error detecting content type: {e}")
            return "general"
    
    def _adaptive_split(self, documents: List[Document], content_type: str) -> List[Document]:
        """Apply adaptive chunking strategy."""
        try:
            if self.enable_parallel_processing and len(documents) > 10:
                return self.chunking_service.parallel_chunk_processing(documents, content_type)
            else:
                return self.chunking_service.adaptive_chunk(documents, content_type)
        except Exception as e:
            logger.error(f"Adaptive splitting failed: {e}")
            return self._fallback_split_documents(documents)
    
    def _semantic_split(self, documents: List[Document], content_type: str) -> List[Document]:
        """Apply semantic chunking strategy."""
        try:
            # Use semantic chunking from enhanced service
            chunks = []
            for doc in documents:
                doc_chunks = self.chunking_service.chunk_with_optimal_strategy(
                    doc.page_content,
                    content_type=content_type,
                    chunk_size=self.chunk_size,
                    chunk_overlap=self.chunk_overlap
                )
                
                # Update metadata
                for chunk in doc_chunks:
                    chunk.metadata.update(doc.metadata)
                    chunk.metadata["chunking_strategy"] = "semantic"
                
                chunks.extend(doc_chunks)
            
            return chunks
            
        except Exception as e:
            logger.error(f"Semantic splitting failed: {e}")
            return self._fallback_split_documents(documents)
    
    def _sentence_split(self, documents: List[Document], content_type: str) -> List[Document]:
        """Apply sentence-based chunking strategy."""
        try:
            # Get sentence splitter from enhanced service
            sentence_splitter = self.chunking_service.get_sentence_splitter(
                chunk_size=self.chunk_size,
                chunk_overlap=self.chunk_overlap
            )
            
            chunks = []
            for doc in documents:
                if hasattr(sentence_splitter, 'split_text'):
                    # LlamaIndex interface
                    text_chunks = sentence_splitter.split_text(doc.page_content)
                    for i, chunk_text in enumerate(text_chunks):
                        chunk = Document(
                            page_content=chunk_text,
                            metadata={
                                **doc.metadata,
                                "chunk_index": i,
                                "chunking_strategy": "sentence",
                                "content_type": content_type
                            }
                        )
                        chunks.append(chunk)
                else:
                    # LangChain interface
                    doc_chunks = sentence_splitter.split_documents([doc])
                    for chunk in doc_chunks:
                        chunk.metadata["chunking_strategy"] = "sentence"
                        chunk.metadata["content_type"] = content_type
                    chunks.extend(doc_chunks)
            
            return chunks
            
        except Exception as e:
            logger.error(f"Sentence splitting failed: {e}")
            return self._fallback_split_documents(documents)
    
    def _fixed_split(self, documents: List[Document], content_type: str) -> List[Document]:
        """Apply fixed-size chunking strategy."""
        try:
            # Use fallback splitter from enhanced service
            fallback_splitter = self.chunking_service._fallback_splitter
            chunks = fallback_splitter.split_documents(documents)
            
            # Update metadata
            for chunk in chunks:
                chunk.metadata["chunking_strategy"] = "fixed"
                chunk.metadata["content_type"] = content_type
            
            return chunks
            
        except Exception as e:
            logger.error(f"Fixed splitting failed: {e}")
            return self._fallback_split_documents(documents)
    
    def _enhance_chunk_metadata(self, chunks: List[Document], original_docs: List[Document]) -> List[Document]:
        """Enhance chunk metadata with multimodal awareness."""
        try:
            if not self.preserve_multimodal_relationships:
                return chunks
            
            enhanced_chunks = []
            for chunk in chunks:
                # Add multimodal metadata
                chunk.metadata.update({
                    "splitter_type": "multimodal",
                    "chunk_size_config": self.chunk_size,
                    "chunk_overlap_config": self.chunk_overlap,
                    "multimodal_aware": True
                })
                
                # Check for multimodal content indicators
                content = chunk.page_content.lower()
                if any(indicator in content for indicator in ["image", "figure", "fig.", "picture"]):
                    chunk.metadata["contains_image_references"] = True
                
                if any(indicator in content for indicator in ["table", "tab.", "chart", "graph"]):
                    chunk.metadata["contains_table_references"] = True
                
                enhanced_chunks.append(chunk)
            
            return enhanced_chunks
            
        except Exception as e:
            logger.error(f"Error enhancing metadata: {e}")
            return chunks
    
    def _fallback_split_text(self, text: str) -> List[str]:
        """Fallback text splitting method."""
        try:
            # Simple character-based splitting
            chunks = []
            start = 0
            while start < len(text):
                end = start + self.chunk_size
                if end < len(text):
                    # Try to break at word boundary
                    while end > start and text[end] not in [' ', '\n', '\t']:
                        end -= 1
                    if end == start:  # No word boundary found
                        end = start + self.chunk_size
                
                chunk = text[start:end].strip()
                if chunk:
                    chunks.append(chunk)
                
                start = end - self.chunk_overlap if self.chunk_overlap > 0 else end
            
            return chunks
            
        except Exception as e:
            logger.error(f"Fallback text splitting failed: {e}")
            return [text]  # Return original text as single chunk
    
    def _fallback_split_documents(self, documents: List[Document]) -> List[Document]:
        """Fallback document splitting method."""
        try:
            chunks = []
            for doc in documents:
                text_chunks = self._fallback_split_text(doc.page_content)
                for i, chunk_text in enumerate(text_chunks):
                    chunk = Document(
                        page_content=chunk_text,
                        metadata={
                            **doc.metadata,
                            "chunk_index": i,
                            "chunking_strategy": "fallback",
                            "splitter_type": "multimodal"
                        }
                    )
                    chunks.append(chunk)
            
            return chunks
            
        except Exception as e:
            logger.error(f"Fallback document splitting failed: {e}")
            return documents  # Return original documents
    
    def get_splitter_info(self) -> Dict[str, Any]:
        """Get information about the splitter configuration."""
        return {
            "splitter_type": "multimodal",
            "chunk_size": self.chunk_size,
            "chunk_overlap": self.chunk_overlap,
            "content_type": self.content_type,
            "chunking_strategy": self.chunking_strategy,
            "preserve_multimodal_relationships": self.preserve_multimodal_relationships,
            "enable_parallel_processing": self.enable_parallel_processing
        }


def get_multimodal_text_splitter(
    chunk_size: int = 800,
    chunk_overlap: int = 200,
    content_type: Optional[str] = None,
    chunking_strategy: str = "adaptive",
    **kwargs
) -> MultimodalTextSplitter:
    """
    Factory function to create a multimodal text splitter.
    
    Args:
        chunk_size: Maximum size of each chunk
        chunk_overlap: Overlap between consecutive chunks
        content_type: Type of content for optimized chunking
        chunking_strategy: Strategy to use for chunking
        **kwargs: Additional arguments
        
    Returns:
        MultimodalTextSplitter instance
    """
    return MultimodalTextSplitter(
        chunk_size=chunk_size,
        chunk_overlap=chunk_overlap,
        content_type=content_type,
        chunking_strategy=chunking_strategy,
        **kwargs
    )

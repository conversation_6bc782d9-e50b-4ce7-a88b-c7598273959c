"""
LangChain Integration Module

This module provides LangChain-compatible components that integrate with the existing
multimodal RAG infrastructure, including PyMuPDF processing, Ollama embeddings,
Redis caching, and ChromaDB vector storage.

Components:
- MultimodalDocumentLoader: LangChain document loader for multimodal content
- OllamaEmbeddingsWrapper: LangChain-compatible Ollama embeddings with caching
- MultimodalTextSplitter: LangChain text splitter using existing chunking logic
- ChromaVectorStoreWrapper: LangChain vector store wrapper for existing ChromaDB
- AdaptiveRetriever: LangChain retriever with adaptive k values
"""

try:
    from .multimodal_document_loader import MultimodalDocumentLoader
    from .ollama_embeddings_wrapper import OllamaEmbeddingsWrapper, get_default_langchain_embeddings
    from .multimodal_text_splitter import MultimodalTextSplitter, get_multimodal_text_splitter
    from .chroma_vectorstore_wrapper import ChromaVectorStoreWrapper, get_chroma_vectorstore
    from .adaptive_retriever import AdaptiveRetriever, get_adaptive_retriever

    LANGCHAIN_INTEGRATION_AVAILABLE = True
except ImportError as e:
    import logging
    logger = logging.getLogger(__name__)
    logger.warning(f"LangChain integration components not available: {e}")
    LANGCHAIN_INTEGRATION_AVAILABLE = False

if LANGCHAIN_INTEGRATION_AVAILABLE:
    __all__ = [
        "MultimodalDocumentLoader",
        "OllamaEmbeddingsWrapper",
        "get_default_langchain_embeddings",
        "MultimodalTextSplitter",
        "get_multimodal_text_splitter",
        "ChromaVectorStoreWrapper",
        "get_chroma_vectorstore",
        "AdaptiveRetriever",
        "get_adaptive_retriever",
        "LANGCHAIN_INTEGRATION_AVAILABLE"
    ]
else:
    __all__ = ["LANGCHAIN_INTEGRATION_AVAILABLE"]

#!/usr/bin/env python3
"""
Test script for Enhanced Chunking Service
Verifies that the new chunking system works correctly with existing setup.
"""

import sys
import os
import logging

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from langchain.schema import Document
from app.services.enhanced_chunking_service import EnhancedChunkingService
from app.services.content_type_detector import ContentTypeDetector
from config.chunking_config import get_chunking_config

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_content_type_detection():
    """Test content type detection"""
    print("\n=== Testing Content Type Detection ===")
    
    detector = ContentTypeDetector()
    
    # Test samples
    test_samples = {
        "technical": """
        This document describes the implementation of a REST API using Flask framework.
        The system architecture includes a database layer, authentication middleware,
        and various endpoints for CRUD operations. The configuration uses environment
        variables and supports both development and production deployments.
        """,
        
        "scientific": """
        This study investigates the correlation between temperature and plant growth.
        The methodology involved measuring 100 samples over a 6-month period.
        Statistical analysis revealed a significant correlation (p < 0.05) between
        temperature and growth rate. The results suggest that optimal growth occurs
        at temperatures between 20-25°C.
        """,
        
        "narrative": """
        Once upon a time, in a small village nestled between rolling hills,
        there lived a young girl named Sarah. She had always been curious about
        the mysterious forest that bordered her hometown. One day, she decided
        to explore its depths, not knowing that this decision would change her life forever.
        """,
        
        "general": """
        The company's quarterly report shows steady growth across all departments.
        Sales increased by 15% compared to the previous quarter, while customer
        satisfaction ratings remained consistently high. The marketing team
        launched several successful campaigns that contributed to brand awareness.
        """
    }
    
    for expected_type, sample_text in test_samples.items():
        detected_type = detector.detect_content_type(sample_text)
        print(f"Expected: {expected_type}, Detected: {detected_type}")
        
        # Analyze structure
        structure = detector.analyze_document_structure(sample_text)
        print(f"  Structure: {structure['total_paragraphs']} paragraphs, "
              f"avg length: {structure['avg_paragraph_length']:.1f}")

def test_chunking_configuration():
    """Test chunking configuration loading"""
    print("\n=== Testing Chunking Configuration ===")
    
    config = get_chunking_config()
    print(f"Default chunk size: {config.default_chunk_size}")
    print(f"Default overlap: {config.default_chunk_overlap}")
    print(f"Embedding model: {config.embedding_model}")
    print(f"Ollama URL: {config.ollama_base_url}")
    
    # Test content-type specific configs
    for content_type in ['technical', 'scientific', 'narrative', 'general']:
        type_config = config.get_config_for_content_type(content_type)
        print(f"{content_type}: chunk_size={type_config['chunk_size']}, "
              f"strategy={type_config.get('strategy', 'default')}")

def test_enhanced_chunking():
    """Test enhanced chunking service"""
    print("\n=== Testing Enhanced Chunking Service ===")
    
    # Create test documents
    test_documents = [
        Document(
            page_content="""
            This is a technical document about implementing microservices architecture.
            The system uses Docker containers for deployment and Kubernetes for orchestration.
            Each service has its own database and communicates via REST APIs.
            Authentication is handled by JWT tokens and authorization uses role-based access control.
            The monitoring stack includes Prometheus for metrics and Grafana for visualization.
            """,
            metadata={"source": "technical_doc.pdf", "page": 1}
        ),
        Document(
            page_content="""
            The research methodology involved a randomized controlled trial with 200 participants.
            Participants were divided into two groups: treatment (n=100) and control (n=100).
            The primary outcome was measured using standardized assessment tools.
            Statistical analysis was performed using SPSS version 28.
            Results showed a statistically significant difference between groups (p=0.003).
            """,
            metadata={"source": "research_paper.pdf", "page": 1}
        ),
        Document(
            page_content="""
            The old lighthouse stood majestically on the rocky cliff, its beacon cutting through
            the thick fog that rolled in from the sea. Captain Morrison had been the keeper
            for over thirty years, and he knew every sound the old structure made.
            Tonight, however, something was different. There was a strange humming coming
            from the lamp room that he had never heard before.
            """,
            metadata={"source": "story.txt", "page": 1}
        )
    ]
    
    try:
        # Initialize chunking service
        chunking_service = EnhancedChunkingService()
        print(f"Chunking service initialized successfully")
        
        # Test adaptive chunking
        chunks = chunking_service.adaptive_chunk(test_documents)
        print(f"Created {len(chunks)} chunks from {len(test_documents)} documents")
        
        # Display chunk information
        for i, chunk in enumerate(chunks):
            print(f"  Chunk {i+1}: {len(chunk.page_content)} chars, "
                  f"source: {chunk.metadata.get('source', 'unknown')}")
        
        # Test parallel processing if enabled
        if chunking_service.config.enable_parallel_processing:
            print("\nTesting parallel processing...")
            parallel_chunks = chunking_service.parallel_chunk_processing(test_documents)
            print(f"Parallel processing created {len(parallel_chunks)} chunks")
        
        return True
        
    except Exception as e:
        print(f"Enhanced chunking test failed: {e}")
        logger.exception("Detailed error:")
        return False

def test_fallback_compatibility():
    """Test fallback to LangChain when LlamaIndex is not available"""
    print("\n=== Testing Fallback Compatibility ===")
    
    # Create a simple document
    test_doc = Document(
        page_content="This is a test document for fallback compatibility testing. " * 50,
        metadata={"source": "test.txt"}
    )
    
    try:
        chunking_service = EnhancedChunkingService()
        chunks = chunking_service._fallback_chunk([test_doc])
        print(f"Fallback chunking created {len(chunks)} chunks")
        
        # Verify chunks have proper metadata
        for chunk in chunks:
            assert chunk.metadata["source"] == "test.txt"
        
        print("Fallback compatibility test passed")
        return True
        
    except Exception as e:
        print(f"Fallback compatibility test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("Enhanced Chunking Service Test Suite")
    print("=" * 50)
    
    tests = [
        ("Content Type Detection", test_content_type_detection),
        ("Chunking Configuration", test_chunking_configuration),
        ("Enhanced Chunking", test_enhanced_chunking),
        ("Fallback Compatibility", test_fallback_compatibility)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            print(f"\nRunning {test_name}...")
            result = test_func()
            results.append((test_name, result if result is not None else True))
        except Exception as e:
            print(f"Test {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n" + "=" * 50)
    print("Test Results Summary:")
    passed = 0
    for test_name, result in results:
        status = "PASS" if result else "FAIL"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nPassed: {passed}/{len(results)} tests")
    
    if passed == len(results):
        print("All tests passed! Enhanced chunking system is ready.")
    else:
        print("Some tests failed. Please check the implementation.")

if __name__ == "__main__":
    main()

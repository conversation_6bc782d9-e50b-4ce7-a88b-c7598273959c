"""
Multimodal Processing Logger Service
Provides real-time logging and feedback for multimodal document processing
"""

import json
import logging
import threading
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from pathlib import Path
import os

logger = logging.getLogger(__name__)

@dataclass
class ProcessingEvent:
    """Represents a processing event"""
    timestamp: str
    event_type: str  # 'start', 'progress', 'complete', 'error'
    document_name: str
    message: str
    details: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.details is None:
            self.details = {}

@dataclass
class ProcessingSession:
    """Represents a complete processing session"""
    session_id: str
    document_name: str
    start_time: str
    end_time: Optional[str] = None
    status: str = 'running'  # 'running', 'completed', 'failed'
    events: List[ProcessingEvent] = None
    results: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.events is None:
            self.events = []
        if self.results is None:
            self.results = {}

class MultimodalLogger:
    """Service for logging multimodal processing events and providing real-time feedback"""
    
    def __init__(self):
        self.sessions: Dict[str, ProcessingSession] = {}
        self.log_file = "data/multimodal_processing.log"
        self.sessions_file = "data/multimodal_sessions.json"
        self._lock = threading.Lock()
        
        # Ensure log directory exists
        os.makedirs(os.path.dirname(self.log_file), exist_ok=True)
        
        # Setup file logging
        self.setup_file_logging()
        
        # Load existing sessions
        self.load_sessions()
    
    def setup_file_logging(self):
        """Setup file logging for multimodal processing"""
        # Create a specific logger for multimodal processing
        self.processing_logger = logging.getLogger('multimodal_processing')
        self.processing_logger.setLevel(logging.INFO)
        
        # Remove existing handlers to avoid duplicates
        for handler in self.processing_logger.handlers[:]:
            self.processing_logger.removeHandler(handler)
        
        # Create file handler
        file_handler = logging.FileHandler(self.log_file)
        file_handler.setLevel(logging.INFO)
        
        # Create formatter
        formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        file_handler.setFormatter(formatter)
        
        # Add handler to logger
        self.processing_logger.addHandler(file_handler)
        
        # Prevent propagation to root logger
        self.processing_logger.propagate = False
    
    def start_session(self, document_name: str) -> str:
        """Start a new processing session"""
        session_id = f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{len(self.sessions)}"
        
        session = ProcessingSession(
            session_id=session_id,
            document_name=document_name,
            start_time=datetime.now().isoformat(),
            status='running'
        )
        
        with self._lock:
            self.sessions[session_id] = session
        
        # Log session start
        self.log_event(session_id, 'start', f"Started processing document: {document_name}")
        self.processing_logger.info(f"[{session_id}] Started processing: {document_name}")
        
        return session_id
    
    def log_event(self, session_id: str, event_type: str, message: str, details: Dict[str, Any] = None):
        """Log a processing event"""
        if session_id not in self.sessions:
            logger.warning(f"Session {session_id} not found")
            return
        
        event = ProcessingEvent(
            timestamp=datetime.now().isoformat(),
            event_type=event_type,
            document_name=self.sessions[session_id].document_name,
            message=message,
            details=details or {}
        )
        
        with self._lock:
            self.sessions[session_id].events.append(event)
        
        # Log to file
        self.processing_logger.info(f"[{session_id}] {event_type.upper()}: {message}")
        if details:
            self.processing_logger.info(f"[{session_id}] Details: {json.dumps(details)}")
        
        # Save sessions
        self.save_sessions()
    
    def log_progress(self, session_id: str, step: str, progress: float, details: Dict[str, Any] = None):
        """Log processing progress"""
        message = f"{step} - {progress:.1f}% complete"
        progress_details = {'step': step, 'progress': progress}
        if details:
            progress_details.update(details)
        
        self.log_event(session_id, 'progress', message, progress_details)
    
    def log_extraction_result(self, session_id: str, content_type: str, count: int, details: Dict[str, Any] = None):
        """Log extraction results"""
        message = f"Extracted {count} {content_type}(s)"
        extraction_details = {'content_type': content_type, 'count': count}
        if details:
            extraction_details.update(details)
        
        self.log_event(session_id, 'progress', message, extraction_details)
    
    def complete_session(self, session_id: str, results: Dict[str, Any] = None):
        """Complete a processing session"""
        if session_id not in self.sessions:
            logger.warning(f"Session {session_id} not found")
            return
        
        with self._lock:
            session = self.sessions[session_id]
            session.end_time = datetime.now().isoformat()
            session.status = 'completed'
            session.results = results or {}
        
        # Calculate processing time
        start_time = datetime.fromisoformat(session.start_time)
        end_time = datetime.fromisoformat(session.end_time)
        processing_time = (end_time - start_time).total_seconds()
        
        message = f"Completed processing in {processing_time:.2f} seconds"
        self.log_event(session_id, 'complete', message, {
            'processing_time': processing_time,
            'results': results or {}
        })
        
        self.processing_logger.info(f"[{session_id}] COMPLETED: {message}")
        self.save_sessions()
    
    def fail_session(self, session_id: str, error: str, details: Dict[str, Any] = None):
        """Mark a session as failed"""
        if session_id not in self.sessions:
            logger.warning(f"Session {session_id} not found")
            return
        
        with self._lock:
            session = self.sessions[session_id]
            session.end_time = datetime.now().isoformat()
            session.status = 'failed'
        
        error_details = {'error': error}
        if details:
            error_details.update(details)
        
        self.log_event(session_id, 'error', f"Processing failed: {error}", error_details)
        self.processing_logger.error(f"[{session_id}] FAILED: {error}")
        self.save_sessions()
    
    def get_session(self, session_id: str) -> Optional[ProcessingSession]:
        """Get a processing session"""
        return self.sessions.get(session_id)
    
    def get_recent_sessions(self, limit: int = 10) -> List[ProcessingSession]:
        """Get recent processing sessions"""
        sessions = list(self.sessions.values())
        sessions.sort(key=lambda s: s.start_time, reverse=True)
        return sessions[:limit]
    
    def get_session_events(self, session_id: str) -> List[ProcessingEvent]:
        """Get events for a specific session"""
        session = self.sessions.get(session_id)
        return session.events if session else []
    
    def get_processing_logs(self, lines: int = 100) -> List[str]:
        """Get recent processing logs"""
        try:
            if not os.path.exists(self.log_file):
                return []
            
            with open(self.log_file, 'r') as f:
                all_lines = f.readlines()
                return [line.strip() for line in all_lines[-lines:]]
        except Exception as e:
            logger.error(f"Error reading log file: {e}")
            return []
    
    def clear_old_sessions(self, days: int = 7):
        """Clear sessions older than specified days"""
        cutoff_time = datetime.now().timestamp() - (days * 24 * 60 * 60)
        
        with self._lock:
            sessions_to_remove = []
            for session_id, session in self.sessions.items():
                session_time = datetime.fromisoformat(session.start_time).timestamp()
                if session_time < cutoff_time:
                    sessions_to_remove.append(session_id)
            
            for session_id in sessions_to_remove:
                del self.sessions[session_id]
        
        if sessions_to_remove:
            self.save_sessions()
            logger.info(f"Cleared {len(sessions_to_remove)} old sessions")
    
    def save_sessions(self):
        """Save sessions to file"""
        try:
            sessions_data = {}
            for session_id, session in self.sessions.items():
                sessions_data[session_id] = asdict(session)
            
            with open(self.sessions_file, 'w') as f:
                json.dump(sessions_data, f, indent=2)
        except Exception as e:
            logger.error(f"Error saving sessions: {e}")
    
    def load_sessions(self):
        """Load sessions from file"""
        try:
            if os.path.exists(self.sessions_file):
                with open(self.sessions_file, 'r') as f:
                    sessions_data = json.load(f)
                
                for session_id, session_dict in sessions_data.items():
                    # Convert events back to ProcessingEvent objects
                    events = []
                    for event_dict in session_dict.get('events', []):
                        events.append(ProcessingEvent(**event_dict))
                    
                    session_dict['events'] = events
                    self.sessions[session_id] = ProcessingSession(**session_dict)
        except Exception as e:
            logger.error(f"Error loading sessions: {e}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get processing statistics"""
        total_sessions = len(self.sessions)
        completed_sessions = sum(1 for s in self.sessions.values() if s.status == 'completed')
        failed_sessions = sum(1 for s in self.sessions.values() if s.status == 'failed')
        running_sessions = sum(1 for s in self.sessions.values() if s.status == 'running')
        
        success_rate = (completed_sessions / total_sessions * 100) if total_sessions > 0 else 0
        
        # Calculate average processing time for completed sessions
        completed = [s for s in self.sessions.values() if s.status == 'completed' and s.end_time]
        avg_processing_time = 0
        if completed:
            total_time = 0
            for session in completed:
                start = datetime.fromisoformat(session.start_time)
                end = datetime.fromisoformat(session.end_time)
                total_time += (end - start).total_seconds()
            avg_processing_time = total_time / len(completed)
        
        return {
            'total_sessions': total_sessions,
            'completed_sessions': completed_sessions,
            'failed_sessions': failed_sessions,
            'running_sessions': running_sessions,
            'success_rate': success_rate,
            'average_processing_time': avg_processing_time
        }

# Global instance
multimodal_logger = MultimodalLogger()

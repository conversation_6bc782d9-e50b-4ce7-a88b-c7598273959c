"""
Test suite for RAG pipeline optimizations
Validates caching, parallel processing, adaptive retrieval, and semantic chunking
"""

import pytest
import time
import os
import sys
from unittest.mock import Mock, patch, MagicMock

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from app.services.cache_service import CacheService, QueryCache
from app.services.query_service import get_adaptive_k, filter_relevant_documents, score_document_relevance
from app.utils.rag_performance import RAGPerformanceMonitor, get_rag_monitor
from config.rag_optimizations import RAGOptimizationConfig, get_rag_config

class TestQueryResultCaching:
    """Test query result caching functionality"""
    
    def test_cache_service_initialization(self):
        """Test cache service initializes correctly"""
        cache = CacheService(use_redis=False)  # Use in-memory for testing
        assert cache is not None
        assert not cache._use_redis  # Should fallback to in-memory
    
    def test_cache_set_get(self):
        """Test basic cache set and get operations"""
        cache = CacheService(use_redis=False)
        test_key = "test_key"
        test_value = {"answer": "test answer", "sources": []}
        
        cache.set(test_key, test_value, ttl=60)
        retrieved_value = cache.get(test_key)
        
        assert retrieved_value == test_value
    
    def test_cache_expiration(self):
        """Test cache expiration functionality"""
        cache = CacheService(use_redis=False)
        test_key = "test_expiry"
        test_value = {"answer": "test answer"}
        
        cache.set(test_key, test_value, ttl=1)  # 1 second TTL
        assert cache.get(test_key) == test_value
        
        time.sleep(2)  # Wait for expiration
        assert cache.get(test_key) is None
    
    def test_query_cache_key_generation(self):
        """Test query cache key generation"""
        key1 = QueryCache.generate_query_cache_key("category1", "question1", "strict", "model1")
        key2 = QueryCache.generate_query_cache_key("category1", "question1", "strict", "model1")
        key3 = QueryCache.generate_query_cache_key("category1", "question2", "strict", "model1")
        
        assert key1 == key2  # Same parameters should generate same key
        assert key1 != key3  # Different parameters should generate different keys

class TestAdaptiveRetrieval:
    """Test adaptive retrieval functionality"""
    
    def test_simple_query_adaptive_k(self):
        """Test adaptive k for simple queries"""
        simple_query = "what is"
        base_k = 12
        adaptive_k = get_adaptive_k(simple_query, base_k)
        
        assert adaptive_k < base_k  # Should be reduced for simple queries
        assert adaptive_k >= 6  # Should not go below minimum
    
    def test_medium_query_adaptive_k(self):
        """Test adaptive k for medium complexity queries"""
        medium_query = "what are the environmental impacts of deforestation"
        base_k = 12
        adaptive_k = get_adaptive_k(medium_query, base_k)
        
        assert adaptive_k == base_k  # Should use base value for medium queries
    
    def test_complex_query_adaptive_k(self):
        """Test adaptive k for complex queries"""
        complex_query = "what are the detailed environmental and socioeconomic impacts of large-scale deforestation in tropical rainforests"
        base_k = 12
        adaptive_k = get_adaptive_k(complex_query, base_k)
        
        assert adaptive_k > base_k  # Should be increased for complex queries
        assert adaptive_k <= 20  # Should not exceed maximum

class TestParallelDocumentScoring:
    """Test parallel document scoring functionality"""
    
    def create_mock_documents(self, count=10):
        """Create mock documents for testing"""
        from langchain.schema import Document
        docs = []
        for i in range(count):
            doc = Document(
                page_content=f"This is test document {i} with some content about environmental research.",
                metadata={"source": f"test_doc_{i}.pdf", "page": i}
            )
            docs.append(doc)
        return docs
    
    def test_parallel_scoring_enabled(self):
        """Test that parallel scoring is used for large document sets"""
        docs = self.create_mock_documents(10)
        question = "environmental research"
        
        # Test with parallel processing enabled
        start_time = time.time()
        scored_docs = filter_relevant_documents(docs, question, use_parallel=True)
        parallel_time = time.time() - start_time
        
        assert len(scored_docs) <= len(docs)  # Should filter documents
        assert all(isinstance(item, tuple) and len(item) == 2 for item in scored_docs)  # Should return (doc, score) tuples
    
    def test_sequential_scoring_fallback(self):
        """Test that sequential scoring is used for small document sets"""
        docs = self.create_mock_documents(3)
        question = "environmental research"
        
        # Test with small document set (should use sequential)
        scored_docs = filter_relevant_documents(docs, question, use_parallel=True)
        
        assert len(scored_docs) <= len(docs)
        assert all(isinstance(item, tuple) and len(item) == 2 for item in scored_docs)

class TestRAGPerformanceMonitoring:
    """Test RAG performance monitoring functionality"""
    
    def test_performance_monitor_initialization(self):
        """Test performance monitor initializes correctly"""
        monitor = RAGPerformanceMonitor()
        assert monitor is not None
        assert len(monitor.metrics) == 0
        assert monitor.cache_stats['query_hits'] == 0
    
    def test_query_metric_recording(self):
        """Test recording query metrics"""
        monitor = RAGPerformanceMonitor()
        
        monitor.record_query_metric(
            category="test_category",
            execution_time=1.5,
            cache_hit=False,
            adaptive_k=10,
            base_k=12,
            document_count=5
        )
        
        assert len(monitor.metrics) == 1
        assert monitor.cache_stats['query_misses'] == 1
        assert monitor.cache_stats['query_hits'] == 0
    
    def test_cache_hit_rate_calculation(self):
        """Test cache hit rate calculation"""
        monitor = RAGPerformanceMonitor()
        
        # Record some hits and misses
        monitor.record_cache_hit('query')
        monitor.record_cache_hit('query')
        monitor.record_cache_miss('query')
        
        hit_rate = monitor.get_cache_hit_rate('query')
        assert hit_rate == 2/3  # 2 hits out of 3 total
    
    def test_parallel_processing_metrics(self):
        """Test parallel processing metrics recording"""
        monitor = RAGPerformanceMonitor()
        
        monitor.record_parallel_scoring(
            execution_time=0.5,
            workers=4,
            document_count=10,
            category="test_category"
        )
        
        stats = monitor.get_parallel_processing_performance()
        assert stats['total_operations'] == 1
        assert stats['avg_execution_time'] == 0.5
        assert stats['avg_workers'] == 4

class TestRAGConfiguration:
    """Test RAG optimization configuration"""
    
    def test_config_from_env(self):
        """Test configuration loading from environment variables"""
        # Set some test environment variables
        os.environ['RAG_ENABLE_QUERY_CACHING'] = 'true'
        os.environ['RAG_QUERY_CACHE_TTL'] = '3600'
        os.environ['RAG_MAX_PARALLEL_WORKERS'] = '8'
        
        config = RAGOptimizationConfig.from_env()
        
        assert config.enable_query_caching is True
        assert config.query_cache_ttl == 3600
        assert config.max_parallel_workers == 8
        
        # Clean up
        del os.environ['RAG_ENABLE_QUERY_CACHING']
        del os.environ['RAG_QUERY_CACHE_TTL']
        del os.environ['RAG_MAX_PARALLEL_WORKERS']
    
    def test_config_to_dict(self):
        """Test configuration serialization to dictionary"""
        config = RAGOptimizationConfig()
        config_dict = config.to_dict()
        
        assert 'query_caching' in config_dict
        assert 'embedding_caching' in config_dict
        assert 'parallel_processing' in config_dict
        assert 'adaptive_retrieval' in config_dict
        assert 'semantic_chunking' in config_dict

class TestIntegration:
    """Integration tests for RAG optimizations"""
    
    @patch('app.services.query_service.get_vector_db')
    @patch('app.services.query_service.similarity_search_with_category_filter')
    def test_end_to_end_optimization_flow(self, mock_search, mock_get_db):
        """Test the complete optimization flow"""
        # Mock vector database and search results
        mock_db = Mock()
        mock_get_db.return_value = mock_db
        
        # Mock search results
        from langchain.schema import Document
        mock_docs = [
            Document(page_content="Test content 1", metadata={"source": "test1.pdf"}),
            Document(page_content="Test content 2", metadata={"source": "test2.pdf"})
        ]
        mock_search.return_value = mock_docs
        
        # Test that the optimization components work together
        # This would require more complex mocking for a full integration test
        assert True  # Placeholder for now

if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])

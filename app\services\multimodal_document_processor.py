"""
Multimodal Document Processor
Main orchestrator for processing documents with text, images, and tables
"""

import os
import logging
import hashlib
from typing import Dict, Any, List, Optional, Tuple
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed
import time

from config.multimodal_config import get_multimodal_config, ContentType
from app.services.multimodal_storage import get_multimodal_storage
from app.services.multimodal_image_processor import get_multimodal_image_processor
from app.services.multimodal_table_processor import get_multimodal_table_processor
from app.services.pdf_processor import extract_text_with_rag, pdf_to_documents
from app.utils.performance_monitor import performance_monitor

logger = logging.getLogger(__name__)

class MultimodalDocumentProcessor:
    """Main processor for multimodal document processing"""
    
    def __init__(self, config=None):
        self.config = config or get_multimodal_config()
        self.storage = get_multimodal_storage()
        self.image_processor = get_multimodal_image_processor()
        self.table_processor = get_multimodal_table_processor()
        
    @performance_monitor(track_memory=True, track_cpu=True, log_parameters=True, log_result_size=True)
    def process_document(self, document_path: str, category: Optional[str] = None,
                        document_id: Optional[str] = None) -> Dict[str, Any]:
        """
        Process a document with multimodal content extraction
        
        Args:
            document_path: Path to the document file
            category: Category for organizing content
            document_id: Unique identifier for the document (auto-generated if not provided)
            
        Returns:
            Dictionary containing all extracted multimodal content
        """
        if not self.config.enable_multimodal:
            logger.info("Multimodal processing is disabled")
            return self._fallback_to_text_only(document_path, category)
        
        try:
            # Generate document ID if not provided
            if not document_id:
                document_id = self._generate_document_id(document_path)
            
            # Detect document format
            document_format = self._detect_document_format(document_path)
            if document_format not in self.config.supported_document_formats:
                logger.warning(f"Unsupported document format: {document_format}")
                return self._fallback_to_text_only(document_path, category)
            
            logger.info(f"Processing multimodal document: {document_path} (format: {document_format})")
            
            # Initialize result structure
            result = {
                "document_id": document_id,
                "document_path": document_path,
                "document_format": document_format,
                "category": category,
                "processing_timestamp": time.time(),
                "text_content": [],
                "images": [],
                "tables": [],
                "multimodal_chunks": [],
                "processing_stats": {}
            }
            
            # Process based on document format
            if document_format == "pdf":
                result = self._process_pdf_document(document_path, document_id, category, result)
            elif document_format in ["docx", "doc"]:
                result = self._process_docx_document(document_path, document_id, category, result)
            else:
                logger.error(f"Processing not implemented for format: {document_format}")
                return self._fallback_to_text_only(document_path, category)
            
            # Generate multimodal chunks
            result["multimodal_chunks"] = self._create_multimodal_chunks(result)
            
            # Calculate processing statistics
            result["processing_stats"] = self._calculate_processing_stats(result)
            
            logger.info(f"Multimodal processing completed for {document_path}")
            return result
            
        except Exception as e:
            logger.error(f"Error in multimodal document processing: {e}")
            return self._fallback_to_text_only(document_path, category)
    
    def _process_pdf_document(self, pdf_path: str, document_id: str, 
                             category: Optional[str], result: Dict[str, Any]) -> Dict[str, Any]:
        """Process PDF document with multimodal extraction"""
        try:
            # Extract text content using existing RAG pipeline
            logger.info("Extracting text content...")
            text_content = extract_text_with_rag(
                pdf_path,
                category=category,
                save_text=True,
                table_strategy=self.config.table_processing.table_strategy,
                ignore_images=False,  # We'll handle images separately
                debug=self.config.debug_mode
            )
            result["text_content"] = text_content
            
            # Process images and tables in parallel if enabled
            if self.config.enable_parallel_processing:
                result = self._process_pdf_parallel(pdf_path, document_id, category, result)
            else:
                result = self._process_pdf_sequential(pdf_path, document_id, category, result)
            
            return result
            
        except Exception as e:
            logger.error(f"Error processing PDF document: {e}")
            return result
    
    def _process_pdf_parallel(self, pdf_path: str, document_id: str,
                             category: Optional[str], result: Dict[str, Any]) -> Dict[str, Any]:
        """Process PDF with parallel image and table extraction"""
        try:
            with ThreadPoolExecutor(max_workers=self.config.max_workers) as executor:
                # Submit tasks
                futures = {}
                
                if self.config.image_processing.enable_image_extraction:
                    futures["images"] = executor.submit(
                        self.image_processor.extract_images_from_pdf,
                        pdf_path, document_id, category
                    )
                
                if self.config.table_processing.enable_table_extraction:
                    futures["tables"] = executor.submit(
                        self.table_processor.extract_tables_from_pdf,
                        pdf_path, document_id, category
                    )
                
                # Collect results
                for content_type, future in futures.items():
                    try:
                        result[content_type] = future.result(timeout=self.config.processing_timeout)
                        logger.info(f"Parallel {content_type} extraction completed")
                    except Exception as e:
                        logger.error(f"Error in parallel {content_type} extraction: {e}")
                        result[content_type] = []
            
            return result
            
        except Exception as e:
            logger.error(f"Error in parallel PDF processing: {e}")
            return self._process_pdf_sequential(pdf_path, document_id, category, result)
    
    def _process_pdf_sequential(self, pdf_path: str, document_id: str,
                               category: Optional[str], result: Dict[str, Any]) -> Dict[str, Any]:
        """Process PDF with sequential image and table extraction"""
        try:
            # Extract images
            if self.config.image_processing.enable_image_extraction:
                logger.info("Extracting images...")
                result["images"] = self.image_processor.extract_images_from_pdf(
                    pdf_path, document_id, category
                )
            
            # Extract tables
            if self.config.table_processing.enable_table_extraction:
                logger.info("Extracting tables...")
                result["tables"] = self.table_processor.extract_tables_from_pdf(
                    pdf_path, document_id, category
                )
            
            return result
            
        except Exception as e:
            logger.error(f"Error in sequential PDF processing: {e}")
            return result
    
    def _process_docx_document(self, docx_path: str, document_id: str,
                              category: Optional[str], result: Dict[str, Any]) -> Dict[str, Any]:
        """Process DOCX document with multimodal extraction"""
        try:
            # Import DOCX processor (to be implemented)
            # For now, return basic structure
            logger.warning("DOCX processing not yet implemented")
            result["text_content"] = []
            result["images"] = []
            result["tables"] = []
            
            return result
            
        except Exception as e:
            logger.error(f"Error processing DOCX document: {e}")
            return result
    
    def _create_multimodal_chunks(self, result: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Create multimodal chunks that combine text, images, and tables"""
        try:
            chunks = []
            
            # Group content by page number
            content_by_page = self._group_content_by_page(result)
            
            for page_num, page_content in content_by_page.items():
                # Create page-level chunk
                page_chunk = {
                    "chunk_type": "multimodal_page",
                    "page_num": page_num,
                    "content_types": [],
                    "text_content": page_content.get("text", ""),
                    "images": page_content.get("images", []),
                    "tables": page_content.get("tables", []),
                    "spatial_relationships": self._analyze_spatial_relationships(page_content)
                }
                
                # Determine content types present
                if page_chunk["text_content"]:
                    page_chunk["content_types"].append("text")
                if page_chunk["images"]:
                    page_chunk["content_types"].append("image")
                if page_chunk["tables"]:
                    page_chunk["content_types"].append("table")
                
                chunks.append(page_chunk)
            
            return chunks
            
        except Exception as e:
            logger.error(f"Error creating multimodal chunks: {e}")
            return []
    
    def _group_content_by_page(self, result: Dict[str, Any]) -> Dict[int, Dict[str, Any]]:
        """Group all content by page number"""
        content_by_page = {}
        
        try:
            # Group text content
            for text_item in result.get("text_content", []):
                page_num = text_item.get("page", 1)
                if page_num not in content_by_page:
                    content_by_page[page_num] = {"text": "", "images": [], "tables": []}
                content_by_page[page_num]["text"] += text_item.get("text", "") + "\n"
            
            # Group images
            for image in result.get("images", []):
                page_num = image.get("page_num", 1)
                if page_num not in content_by_page:
                    content_by_page[page_num] = {"text": "", "images": [], "tables": []}
                content_by_page[page_num]["images"].append(image)
            
            # Group tables
            for table in result.get("tables", []):
                page_num = table.get("page_num", 1)
                if page_num not in content_by_page:
                    content_by_page[page_num] = {"text": "", "images": [], "tables": []}
                content_by_page[page_num]["tables"].append(table)
            
            return content_by_page
            
        except Exception as e:
            logger.error(f"Error grouping content by page: {e}")
            return {}
    
    def _analyze_spatial_relationships(self, page_content: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze spatial relationships between content elements on a page"""
        try:
            relationships = {
                "image_text_proximity": [],
                "table_text_proximity": [],
                "image_table_proximity": []
            }
            
            # For now, return basic structure
            # In a more sophisticated implementation, we would analyze
            # bounding boxes to determine spatial relationships
            
            return relationships
            
        except Exception as e:
            logger.error(f"Error analyzing spatial relationships: {e}")
            return {}
    
    def _calculate_processing_stats(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate processing statistics"""
        try:
            stats = {
                "total_pages": len(set(
                    item.get("page", 1) for item in result.get("text_content", [])
                )),
                "total_images": len(result.get("images", [])),
                "total_tables": len(result.get("tables", [])),
                "total_chunks": len(result.get("multimodal_chunks", [])),
                "content_types_found": []
            }
            
            # Determine content types found
            if result.get("text_content"):
                stats["content_types_found"].append("text")
            if result.get("images"):
                stats["content_types_found"].append("image")
            if result.get("tables"):
                stats["content_types_found"].append("table")
            
            return stats
            
        except Exception as e:
            logger.error(f"Error calculating processing stats: {e}")
            return {}
    
    def _generate_document_id(self, document_path: str) -> str:
        """Generate a unique document ID"""
        try:
            # Use file path and modification time for uniqueness
            file_stat = os.stat(document_path)
            content = f"{document_path}_{file_stat.st_mtime}_{file_stat.st_size}"
            return hashlib.md5(content.encode()).hexdigest()
        except Exception as e:
            logger.error(f"Error generating document ID: {e}")
            return hashlib.md5(document_path.encode()).hexdigest()
    
    def _detect_document_format(self, document_path: str) -> str:
        """Detect document format from file extension"""
        try:
            extension = Path(document_path).suffix.lower()
            format_map = {
                ".pdf": "pdf",
                ".docx": "docx",
                ".doc": "doc"
            }
            return format_map.get(extension, "unknown")
        except Exception as e:
            logger.error(f"Error detecting document format: {e}")
            return "unknown"
    
    def _fallback_to_text_only(self, document_path: str, category: Optional[str]) -> Dict[str, Any]:
        """Fallback to text-only processing"""
        try:
            logger.info("Falling back to text-only processing")
            
            if document_path.lower().endswith('.pdf'):
                # Use existing PDF text extraction
                text_content = extract_text_with_rag(document_path, category=category)
                
                return {
                    "document_id": self._generate_document_id(document_path),
                    "document_path": document_path,
                    "document_format": "pdf",
                    "category": category,
                    "text_content": text_content,
                    "images": [],
                    "tables": [],
                    "multimodal_chunks": [],
                    "processing_stats": {"fallback_mode": True}
                }
            else:
                logger.error(f"Text-only fallback not implemented for {document_path}")
                return {}
                
        except Exception as e:
            logger.error(f"Error in text-only fallback: {e}")
            return {}

# Global processor instance
_multimodal_document_processor = None

def get_multimodal_document_processor() -> MultimodalDocumentProcessor:
    """Get the global multimodal document processor instance"""
    global _multimodal_document_processor
    if _multimodal_document_processor is None:
        _multimodal_document_processor = MultimodalDocumentProcessor()
    return _multimodal_document_processor

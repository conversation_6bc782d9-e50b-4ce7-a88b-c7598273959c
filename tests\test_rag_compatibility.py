"""
RAG Optimization Compatibility Test Suite
Verifies that core functionality is preserved after RAG optimizations
"""

import pytest
import os
import sys
import json
from unittest.mock import Mock, patch, MagicMock

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from app.services.query_service import query_category
from app.services.cache_service import QueryCache
from langchain.schema import Document

class TestPromptEngineeringCompatibility:
    """Test that prompt engineering features are preserved"""
    
    @patch('app.services.query_service.get_vector_db')
    @patch('app.services.query_service.similarity_search_with_category_filter')
    @patch('app.services.query_service.ChatOllama')
    def test_anti_hallucination_modes_preserved(self, mock_llm, mock_search, mock_get_db):
        """Test that all anti-hallucination modes still work"""
        # Mock setup
        mock_db = Mock()
        mock_get_db.return_value = mock_db
        
        mock_docs = [
            Document(
                page_content="Test content about environmental research",
                metadata={
                    "source": "20250101120000_test_document.pdf",
                    "original_filename": "test_document.pdf",
                    "citation_filename": "test_document.pdf",
                    "type": "pdf",
                    "category": "test_category",
                    "page": 1
                }
            )
        ]
        mock_search.return_value = mock_docs
        
        # Mock LLM response
        mock_llm_instance = Mock()
        mock_llm_instance.invoke = Mock(return_value="Test response with proper citations")
        mock_llm.return_value = mock_llm_instance
        
        # Test each anti-hallucination mode
        modes = ['strict', 'balanced', 'off']
        
        for mode in modes:
            with patch('app.services.query_service.ChatPromptTemplate') as mock_prompt:
                mock_chain = Mock()
                mock_chain.invoke = Mock(return_value="Test response")
                mock_prompt.from_template.return_value = mock_chain
                
                result = query_category(
                    category="test_category",
                    question="What is environmental research?",
                    anti_hallucination_mode=mode
                )
                
                # Verify the result structure is preserved
                assert 'answer' in result
                assert 'sources' in result
                assert 'metadata' in result
                
                # Verify prompt template was called (indicating mode-specific prompts are used)
                mock_prompt.from_template.assert_called()

class TestCitationSystemCompatibility:
    """Test that citation system features are preserved"""
    
    def test_gated_download_link_format(self):
        """Test that gated download links are properly formatted"""
        # Create a mock document with PDF metadata
        doc = Document(
            page_content="Test content",
            metadata={
                "source": "20250101120000_test_document.pdf",  # Timestamped filename
                "original_filename": "test_document.pdf",      # Clean filename
                "citation_filename": "test_document.pdf",      # Citation display name
                "type": "pdf",
                "category": "test_category",
                "page": 1
            }
        )
        
        # Simulate the source creation logic from query_service.py
        file_source = doc.metadata.get('source', '')
        source = {
            "source": doc.metadata.get("source", "Unknown"),
            "display_name": doc.metadata.get("original_filename", doc.metadata.get("source", "Unknown")),
            "page": doc.metadata.get("page", None),
            "type": doc.metadata.get("type", "Unknown"),
            "file_path": f"/download_gated/{file_source}" if doc.metadata.get("type") == "pdf" else None,
        }
        
        # Verify the citation format
        assert source["file_path"] == "/download_gated/20250101120000_test_document.pdf"  # Includes timestamp
        assert source["display_name"] == "test_document.pdf"  # Clean display name
        assert source["type"] == "pdf"
    
    def test_citation_filename_vs_file_path(self):
        """Test that citation filename and file path are handled correctly"""
        # Test data mimicking the context assembly in query_service.py
        doc_metadata = {
            "source": "20250706153939_canopy_vol45n1.pdf",
            "original_filename": "canopy_vol45n1.pdf",
            "citation_filename": "canopy_vol45n1.pdf",
            "type": "pdf"
        }
        
        source_name = doc_metadata.get("source", "")
        citation_filename = doc_metadata.get("citation_filename", doc_metadata.get("original_filename", ""))
        file_path = f"/download_gated/{source_name}"
        
        # Verify the expected format
        assert file_path == "/download_gated/20250706153939_canopy_vol45n1.pdf"  # Href with timestamp
        assert citation_filename == "canopy_vol45n1.pdf"  # Display without timestamp
        
        # Verify the context instruction format
        expected_context_part = f"File Path (USE THIS FOR CITATION LINKS): {file_path}"
        expected_citation_part = f"Citation Filename (USE THIS IN CITATIONS): {citation_filename}"
        
        assert "/download_gated/20250706153939_canopy_vol45n1.pdf" in expected_context_part
        assert "canopy_vol45n1.pdf" in expected_citation_part

class TestGatedDownloadIntegration:
    """Test that gated download integration is preserved"""
    
    @patch('app.services.vector_db.QueryCache')
    def test_cache_invalidation_on_document_add(self, mock_query_cache):
        """Test that cache is invalidated when new documents are added"""
        from app.services.vector_db import add_documents_with_category
        
        # Mock the cache invalidation
        mock_query_cache.invalidate_category_cache = Mock(return_value=5)
        
        # Mock documents
        mock_docs = [
            Document(
                page_content="Test content",
                metadata={"source": "test.pdf", "type": "pdf"}
            )
        ]
        
        # Mock the vector database operations
        with patch('app.services.vector_db.get_vector_db') as mock_get_db:
            mock_db = Mock()
            mock_db.add_documents = Mock()
            mock_get_db.return_value = mock_db
            
            # Call the function
            add_documents_with_category(mock_docs, "test_category")
            
            # Verify cache invalidation was called
            mock_query_cache.invalidate_category_cache.assert_called_once_with("test_category")
    
    def test_pdf_metadata_preservation(self):
        """Test that PDF metadata required for gated downloads is preserved"""
        # Simulate document metadata that should be preserved
        original_metadata = {
            "source": "20250101120000_research_paper.pdf",
            "original_filename": "research_paper.pdf",
            "citation_filename": "research_paper.pdf",
            "type": "pdf",
            "category": "research",
            "page": 1,
            "file_size": 1024000,
            "page_count": 10
        }
        
        # Create document
        doc = Document(
            page_content="Research content",
            metadata=original_metadata
        )
        
        # Verify all required metadata is present
        assert doc.metadata["source"] is not None
        assert doc.metadata["original_filename"] is not None
        assert doc.metadata["type"] == "pdf"
        assert doc.metadata["category"] is not None
        
        # Verify gated download path can be constructed
        gated_path = f"/download_gated/{doc.metadata['source']}"
        assert gated_path == "/download_gated/20250101120000_research_paper.pdf"

class TestOptimizationIntegration:
    """Test that optimizations work with existing functionality"""
    
    @patch('app.services.query_service.QueryCache')
    def test_caching_preserves_citation_format(self, mock_query_cache):
        """Test that cached results preserve citation formatting"""
        # Mock cached result with proper citation format
        cached_result = {
            "answer": "Test answer with <a href=\"/download_gated/20250101120000_test.pdf\">test.pdf</a>",
            "sources": [{
                "source": "20250101120000_test.pdf",
                "display_name": "test.pdf",
                "file_path": "/download_gated/20250101120000_test.pdf",
                "type": "pdf"
            }],
            "metadata": {
                "cached": False,
                "document_count": 1
            }
        }
        
        # Mock cache hit
        mock_query_cache.get_cached_query_result = Mock(return_value=cached_result)
        
        # Verify cached result maintains citation format
        result = mock_query_cache.get_cached_query_result("category", "question", "strict", "model")
        
        assert result is not None
        assert "/download_gated/20250101120000_test.pdf" in result["answer"]  # Timestamped href
        assert result["sources"][0]["display_name"] == "test.pdf"  # Clean display name
        assert result["sources"][0]["file_path"] == "/download_gated/20250101120000_test.pdf"
    
    def test_adaptive_retrieval_preserves_metadata(self):
        """Test that adaptive retrieval preserves document metadata"""
        from app.services.query_service import get_adaptive_k
        
        # Test different query complexities
        simple_query = "what is"
        medium_query = "what are the environmental impacts"
        complex_query = "what are the detailed environmental and socioeconomic impacts of deforestation"
        
        base_k = 12
        
        # Test adaptive k calculation
        simple_k = get_adaptive_k(simple_query, base_k)
        medium_k = get_adaptive_k(medium_query, base_k)
        complex_k = get_adaptive_k(complex_query, base_k)
        
        # Verify adaptive behavior
        assert simple_k < base_k  # Simple queries use fewer documents
        assert medium_k == base_k  # Medium queries use base value
        assert complex_k > base_k  # Complex queries use more documents
        
        # Verify bounds
        assert simple_k >= 6  # Minimum threshold
        assert complex_k <= 20  # Maximum threshold

if __name__ == "__main__":
    # Run compatibility tests
    pytest.main([__file__, "-v"])

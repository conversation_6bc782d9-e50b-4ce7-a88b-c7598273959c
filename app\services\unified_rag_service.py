"""
Unified RAG Service

A unified service that provides both LlamaIndex and LangChain interfaces
for multimodal RAG operations, sharing cached embeddings and vector storage
while maintaining all existing performance optimizations.
"""

import logging
from typing import List, Dict, Any, Optional, Union
from enum import Enum

from langchain_core.documents import Document as LangChainDocument

from app.services.langchain_integration.multimodal_document_loader import MultimodalDocumentLoader
from app.services.langchain_integration.ollama_embeddings_wrapper import get_default_langchain_embeddings
from app.services.langchain_integration.multimodal_text_splitter import get_multimodal_text_splitter
from app.services.langchain_integration.chroma_vectorstore_wrapper import get_chroma_vectorstore
from app.services.langchain_integration.adaptive_retriever import get_adaptive_retriever

from app.services.enhanced_rag_service import get_enhanced_rag_service
from app.services.multimodal_document_processor import get_multimodal_document_processor
from app.services.enhanced_chunking_service import get_enhanced_chunking_service
from app.utils.performance_monitor import performance_monitor

logger = logging.getLogger(__name__)


class RAGFramework(Enum):
    """Supported RAG frameworks."""
    LANGCHAIN = "langchain"
    LLAMAINDEX = "llamaindex"
    BOTH = "both"


class UnifiedRAGService:
    """
    Unified RAG service that provides both LlamaIndex and LangChain interfaces.
    
    This service allows users to choose between frameworks while sharing
    the same underlying infrastructure for document processing, embeddings,
    and vector storage.
    """
    
    def __init__(
        self,
        default_framework: RAGFramework = RAGFramework.BOTH,
        category: Optional[str] = None
    ):
        """
        Initialize the unified RAG service.
        
        Args:
            default_framework: Default framework to use
            category: Default category for operations
        """
        self.default_framework = default_framework
        self.category = category
        
        # Initialize shared components
        self._init_shared_components()
        
        # Initialize framework-specific components
        self._init_langchain_components()
        self._init_llamaindex_components()
        
        logger.info(f"Initialized UnifiedRAGService with framework: {default_framework.value}")
    
    def _init_shared_components(self):
        """Initialize shared components used by both frameworks."""
        try:
            # Shared document processor
            self.document_processor = get_multimodal_document_processor()
            
            # Shared embeddings (LangChain-compatible wrapper around existing Ollama embeddings)
            self.embeddings = get_default_langchain_embeddings()
            
            logger.info("Initialized shared components")
            
        except Exception as e:
            logger.error(f"Error initializing shared components: {e}")
            raise
    
    def _init_langchain_components(self):
        """Initialize LangChain-specific components."""
        try:
            # LangChain text splitter
            self.langchain_splitter = get_multimodal_text_splitter(
                chunking_strategy="adaptive",
                preserve_multimodal_relationships=True
            )
            
            # LangChain vector store
            self.langchain_vectorstore = get_chroma_vectorstore(
                category=self.category,
                embeddings=self.embeddings
            )
            
            # LangChain retriever
            self.langchain_retriever = get_adaptive_retriever(
                vectorstore=self.langchain_vectorstore,
                category=self.category,
                enable_multimodal=True
            )
            
            logger.info("Initialized LangChain components")
            
        except Exception as e:
            logger.error(f"Error initializing LangChain components: {e}")
            self.langchain_splitter = None
            self.langchain_vectorstore = None
            self.langchain_retriever = None
    
    def _init_llamaindex_components(self):
        """Initialize LlamaIndex-specific components."""
        try:
            # LlamaIndex services (existing)
            self.llamaindex_rag_service = get_enhanced_rag_service()
            self.llamaindex_chunking_service = get_enhanced_chunking_service()
            
            logger.info("Initialized LlamaIndex components")
            
        except Exception as e:
            logger.error(f"Error initializing LlamaIndex components: {e}")
            self.llamaindex_rag_service = None
            self.llamaindex_chunking_service = None
    
    @performance_monitor(track_memory=True, track_cpu=True, log_parameters=True, log_result_size=True)
    def process_document(
        self,
        document_path: str,
        category: Optional[str] = None,
        framework: Optional[RAGFramework] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Process a document using the specified framework(s).
        
        Args:
            document_path: Path to the document
            category: Category for the document
            framework: Framework to use (overrides default)
            **kwargs: Additional processing arguments
            
        Returns:
            Processing results with framework-specific outputs
        """
        try:
            framework = framework or self.default_framework
            category = category or self.category
            
            logger.info(f"Processing document with framework: {framework.value}")
            
            # Use shared document processor for multimodal extraction
            multimodal_result = self.document_processor.process_document(
                document_path=document_path,
                category=category,
                **kwargs
            )
            
            result = {
                "document_path": document_path,
                "category": category,
                "framework": framework.value,
                "multimodal_content": multimodal_result,
                "langchain_processing": None,
                "llamaindex_processing": None
            }
            
            # Process with LangChain if requested
            if framework in [RAGFramework.LANGCHAIN, RAGFramework.BOTH]:
                result["langchain_processing"] = self._process_with_langchain(
                    document_path, category, multimodal_result, **kwargs
                )
            
            # Process with LlamaIndex if requested
            if framework in [RAGFramework.LLAMAINDEX, RAGFramework.BOTH]:
                result["llamaindex_processing"] = self._process_with_llamaindex(
                    document_path, category, multimodal_result, **kwargs
                )
            
            logger.info(f"Successfully processed document with {framework.value}")
            return result
            
        except Exception as e:
            logger.error(f"Error processing document: {e}")
            raise
    
    def _process_with_langchain(
        self,
        document_path: str,
        category: str,
        multimodal_result: Dict[str, Any],
        **kwargs
    ) -> Dict[str, Any]:
        """Process document using LangChain components."""
        try:
            if not self.langchain_splitter or not self.langchain_vectorstore:
                logger.warning("LangChain components not available")
                return {"error": "LangChain components not initialized"}
            
            # Load document using LangChain loader
            loader = MultimodalDocumentLoader(
                file_path=document_path,
                category=category,
                include_images=True,
                include_tables=True,
                include_multimodal_chunks=True
            )
            
            documents = loader.load()
            
            # Split documents
            split_documents = self.langchain_splitter.split_documents(documents)
            
            # Add to vector store
            doc_ids = self.langchain_vectorstore.add_documents(
                split_documents,
                category=category
            )
            
            return {
                "framework": "langchain",
                "documents_loaded": len(documents),
                "chunks_created": len(split_documents),
                "document_ids": doc_ids,
                "vectorstore_stats": self.langchain_vectorstore.get_collection_stats(category)
            }
            
        except Exception as e:
            logger.error(f"Error in LangChain processing: {e}")
            return {"error": str(e)}
    
    def _process_with_llamaindex(
        self,
        document_path: str,
        category: str,
        multimodal_result: Dict[str, Any],
        **kwargs
    ) -> Dict[str, Any]:
        """Process document using LlamaIndex components."""
        try:
            if not self.llamaindex_rag_service:
                logger.warning("LlamaIndex components not available")
                return {"error": "LlamaIndex components not initialized"}
            
            # Use existing LlamaIndex processing
            result = self.llamaindex_rag_service.process_document(
                document_path,
                category,
                **kwargs
            )
            
            return {
                "framework": "llamaindex",
                "processing_result": result
            }
            
        except Exception as e:
            logger.error(f"Error in LlamaIndex processing: {e}")
            return {"error": str(e)}
    
    @performance_monitor(track_memory=True, track_cpu=True, log_parameters=True, log_result_size=True)
    def query(
        self,
        question: str,
        category: Optional[str] = None,
        framework: Optional[RAGFramework] = None,
        k: Optional[int] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Query the RAG system using the specified framework(s).
        
        Args:
            question: Question to ask
            category: Category to search in
            framework: Framework to use (overrides default)
            k: Number of documents to retrieve
            **kwargs: Additional query arguments
            
        Returns:
            Query results with framework-specific outputs
        """
        try:
            framework = framework or self.default_framework
            category = category or self.category
            
            logger.info(f"Querying with framework: {framework.value}")
            
            result = {
                "question": question,
                "category": category,
                "framework": framework.value,
                "langchain_result": None,
                "llamaindex_result": None
            }
            
            # Query with LangChain if requested
            if framework in [RAGFramework.LANGCHAIN, RAGFramework.BOTH]:
                result["langchain_result"] = self._query_with_langchain(
                    question, category, k, **kwargs
                )
            
            # Query with LlamaIndex if requested
            if framework in [RAGFramework.LLAMAINDEX, RAGFramework.BOTH]:
                result["llamaindex_result"] = self._query_with_llamaindex(
                    question, category, k, **kwargs
                )
            
            return result
            
        except Exception as e:
            logger.error(f"Error querying: {e}")
            raise
    
    def _query_with_langchain(
        self,
        question: str,
        category: str,
        k: Optional[int],
        **kwargs
    ) -> Dict[str, Any]:
        """Query using LangChain components."""
        try:
            if not self.langchain_retriever:
                logger.warning("LangChain retriever not available")
                return {"error": "LangChain retriever not initialized"}
            
            # Update retriever configuration
            if k is not None:
                self.langchain_retriever.update_config(base_k=k)
            if category:
                self.langchain_retriever.update_config(category=category)
            
            # Retrieve relevant documents
            documents = self.langchain_retriever.get_relevant_documents(question)
            
            # Format results
            return {
                "framework": "langchain",
                "documents_retrieved": len(documents),
                "documents": [
                    {
                        "content": doc.page_content[:200] + "..." if len(doc.page_content) > 200 else doc.page_content,
                        "metadata": doc.metadata,
                        "source": doc.metadata.get("source", "unknown")
                    }
                    for doc in documents
                ],
                "retriever_info": self.langchain_retriever.get_retriever_info()
            }
            
        except Exception as e:
            logger.error(f"Error in LangChain query: {e}")
            return {"error": str(e)}
    
    def _query_with_llamaindex(
        self,
        question: str,
        category: str,
        k: Optional[int],
        **kwargs
    ) -> Dict[str, Any]:
        """Query using LlamaIndex components."""
        try:
            if not self.llamaindex_rag_service:
                logger.warning("LlamaIndex service not available")
                return {"error": "LlamaIndex service not initialized"}
            
            # Use existing LlamaIndex query
            result = self.llamaindex_rag_service.answer_question(
                question=question,
                category=category,
                k=k,
                **kwargs
            )
            
            return {
                "framework": "llamaindex",
                "answer_result": result
            }
            
        except Exception as e:
            logger.error(f"Error in LlamaIndex query: {e}")
            return {"error": str(e)}
    
    def get_service_info(self) -> Dict[str, Any]:
        """Get information about the unified service."""
        return {
            "service_type": "unified_rag",
            "default_framework": self.default_framework.value,
            "category": self.category,
            "langchain_available": all([
                self.langchain_splitter,
                self.langchain_vectorstore,
                self.langchain_retriever
            ]),
            "llamaindex_available": all([
                self.llamaindex_rag_service,
                self.llamaindex_chunking_service
            ]),
            "shared_components": {
                "document_processor": self.document_processor is not None,
                "embeddings": self.embeddings is not None
            }
        }
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics from both frameworks."""
        stats = {}
        
        try:
            # LangChain cache stats
            if self.embeddings:
                stats["langchain_embeddings"] = self.embeddings.get_cache_stats()
            
            # LlamaIndex cache stats (if available)
            if self.llamaindex_rag_service:
                # Add LlamaIndex cache stats if available
                pass
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting cache stats: {e}")
            return {"error": str(e)}


# Global service instance
_unified_rag_service = None

def get_unified_rag_service(
    default_framework: RAGFramework = RAGFramework.BOTH,
    category: Optional[str] = None
) -> UnifiedRAGService:
    """
    Get the global unified RAG service instance.
    
    Args:
        default_framework: Default framework to use
        category: Default category for operations
        
    Returns:
        UnifiedRAGService instance
    """
    global _unified_rag_service
    if _unified_rag_service is None:
        _unified_rag_service = UnifiedRAGService(
            default_framework=default_framework,
            category=category
        )
    return _unified_rag_service

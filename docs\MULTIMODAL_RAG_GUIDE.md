# Multimodal RAG Pipeline Guide

This guide describes the enhanced RAG pipeline that supports multimodal content extraction and processing from documents, including images, tables, and text.

## Overview

The multimodal RAG extension builds on the existing LlamaIndex and Ollama-based RAG pipeline to add support for:

- **Image extraction and processing** with vision model captions
- **Table extraction and processing** with structure preservation
- **Hybrid search** across text, images, and tables
- **Multimodal context assembly** for comprehensive responses
- **Performance monitoring** for multimodal operations

## Architecture

### Core Components

1. **MultimodalDocumentProcessor** - Main orchestrator for document processing
2. **MultimodalImageProcessor** - Handles image extraction and vision analysis
3. **MultimodalTableProcessor** - Manages table extraction and formatting
4. **MultimodalChunkingService** - Creates multimodal chunks with context preservation
5. **MultimodalEmbeddingService** - Generates embeddings for different content types
6. **HybridSearchService** - Performs search across multiple modalities
7. **ContextAssembler** - Assembles final multimodal context for responses
8. **MultimodalStorage** - Manages BLOB storage for images and table metadata

### Integration with Existing Pipeline

The multimodal extension seamlessly integrates with existing components:

- **LlamaIndex SemanticSplitter/SentenceSplitter** - Used for text chunking
- **Ollama Embeddings** - Used for all content type embeddings
- **Redis Caching** - Maintains 30-minute TTL for query results
- **ThreadPoolExecutor** - Preserves max_workers=4 configuration
- **Adaptive Retrieval** - Maintains k values (simple: k=8, medium: k=12, complex: k=16)
- **Performance Monitoring** - Extended for multimodal operations

## Configuration

### Basic Configuration

```python
from config.multimodal_config import MultimodalRAGConfig

config = MultimodalRAGConfig()

# Enable multimodal features
config.enable_multimodal = True
config.image_processing.enable_image_extraction = True
config.table_processing.enable_table_extraction = True
config.chunking.enable_multimodal_chunking = True
config.search.enable_hybrid_search = True
```

### Vision Model Configuration

#### Local Ollama Vision Model (Recommended)

```python
# Default configuration uses local Ollama
config.vision_model.model_type = VisionModelType.OLLAMA_LOCAL
config.vision_model.model_name = "llama3.2-vision"
config.vision_model.base_url = "http://localhost:11434"
config.vision_model.fallback_models = ["minicpm-v", "llava"]
```

#### API-based Vision Models (Alternative)

```python
# OpenAI GPT-4V
config.vision_model.model_type = VisionModelType.OPENAI_API
config.vision_model.model_name = "gpt-4-vision-preview"
config.vision_model.api_key = "your-openai-api-key"

# Google Gemini Vision
config.vision_model.model_type = VisionModelType.GEMINI_API
config.vision_model.model_name = "gemini-pro-vision"
config.vision_model.api_key = "your-gemini-api-key"
```

### Environment Variables

```bash
# Vision model settings
MULTIMODAL_VISION_MODEL=llama3.2-vision
MULTIMODAL_VISION_BASE_URL=http://localhost:11434
MULTIMODAL_VISION_API_KEY=your-api-key

# Processing settings
MULTIMODAL_ENABLE_IMAGES=true
MULTIMODAL_ENABLE_TABLES=true
MULTIMODAL_MAX_WORKERS=4

# Storage settings
MULTIMODAL_BLOB_STORAGE_PATH=./data/multimodal_blobs
```

## Usage

### Basic Document Processing

```python
from app.services.multimodal_document_processor import get_multimodal_document_processor

# Process a document with multimodal extraction
processor = get_multimodal_document_processor()
result = processor.process_document(
    document_path="document.pdf",
    category="RESEARCH_PAPERS"
)

print(f"Extracted {len(result['images'])} images")
print(f"Extracted {len(result['tables'])} tables")
print(f"Created {len(result['multimodal_chunks'])} multimodal chunks")
```

### Multimodal Chunking

```python
from app.services.multimodal_chunking_service import get_multimodal_chunking_service

# Create multimodal chunks
chunking_service = get_multimodal_chunking_service()
documents = chunking_service.chunk_multimodal_document(result)

# Documents now contain text, image descriptions, and table content
for doc in documents:
    content_type = doc.metadata.get("content_type")
    print(f"Chunk type: {content_type}")
```

### Embedding Generation

```python
from app.services.multimodal_embedding_service import get_multimodal_embedding_service

# Generate embeddings for multimodal content
embedding_service = get_multimodal_embedding_service()
embedding_result = embedding_service.embed_multimodal_documents(
    documents, 
    category="RESEARCH_PAPERS"
)

print(f"Generated embeddings for {embedding_result['total_documents']} documents")
```

### Hybrid Search

```python
from app.services.hybrid_search_service import get_hybrid_search_service

# Perform hybrid search across all content types
search_service = get_hybrid_search_service()
results = search_service.search_multimodal(
    query="Show me charts about climate change",
    category="RESEARCH_PAPERS",
    k=10
)

print(f"Found {results['total_results']} results")
print(f"Results by type: {results['results_by_type']}")
```

### Context Assembly

```python
from app.services.context_assembler import get_context_assembler

# Assemble multimodal context for response
assembler = get_context_assembler()
context = assembler.assemble_multimodal_context(results, query)

# Format for LLM consumption
formatted_context = assembler.format_context_for_llm(context)
print(formatted_context)
```

## Supported Document Formats

### PDF Documents
- **Text extraction** using PyMuPDF RAG capabilities
- **Image extraction** with vision model analysis
- **Table extraction** using Camelot and Tabula
- **Spatial relationship preservation**

### DOCX Documents (Planned)
- **Text and formatting extraction**
- **Embedded image extraction**
- **Table structure preservation**
- **Cross-reference handling**

## Performance Optimization

### Parallel Processing

```python
# Enable parallel processing for large documents
config.enable_parallel_processing = True
config.max_workers = 4  # Maintains existing ThreadPoolExecutor settings
```

### Caching

```python
# Multimodal content uses existing Redis caching
config.embedding.embedding_cache_ttl = 86400  # 24 hours
config.storage.metadata_cache_ttl = 3600      # 1 hour
```

### Content Filtering

```python
# Configure image filtering
config.image_processing.filter_logos = True
config.image_processing.filter_sensitivity = 0.3
config.image_processing.min_image_size = 1024  # bytes

# Configure table filtering
config.table_processing.min_table_rows = 2
config.table_processing.min_table_cols = 2
```

## Performance Monitoring

### Basic Monitoring

```python
from app.utils.multimodal_performance_monitor import get_multimodal_performance_monitor

monitor = get_multimodal_performance_monitor()

# Get performance summary
summary = monitor.get_performance_summary("document_processing")
print(f"Average processing time: {summary['performance_metrics']['average_duration']:.2f}s")
print(f"Images processed: {summary['content_processing']['total_images']}")
print(f"Vision model calls: {summary['vision_model_performance']['total_calls']}")
```

### Export Metrics

```python
# Export detailed metrics to JSON
monitor.export_metrics("multimodal_performance.json")
```

## Error Handling and Fallbacks

### Vision Model Fallbacks

```python
# Automatic fallback when vision models are unavailable
config.vision_model.fallback_models = ["minicpm-v", "llava"]
config.vision_model.max_retries = 3
```

### Graceful Degradation

```python
# Fallback to text-only processing when multimodal fails
if not config.enable_multimodal:
    # Automatically falls back to existing text-only RAG pipeline
    pass
```

## Testing

### Unit Tests

```bash
# Run multimodal-specific tests
python -m pytest tests/test_multimodal_pipeline.py -v
```

### Integration Tests

```bash
# Run complete pipeline test
python examples/multimodal_rag_example.py
```

### Performance Tests

```bash
# Run performance benchmarks
python tests/test_multimodal_performance.py
```

## Dependencies

### Required Packages

```bash
# Install additional dependencies for multimodal features
pip install llama-index>=0.9.0
pip install llama-index-core
pip install llama-index-embeddings-ollama
pip install tabula-py==2.7.0
pip install python-docx==0.8.11
```

### Vision Model Setup

```bash
# Install Ollama vision model (local)
ollama pull llama3.2-vision

# Alternative models
ollama pull minicpm-v
ollama pull llava
```

## Troubleshooting

### Common Issues

1. **Vision model not available**
   - Check Ollama is running: `ollama list`
   - Install vision model: `ollama pull llama3.2-vision`

2. **Table extraction fails**
   - Ensure camelot-py is installed: `pip install camelot-py`
   - Check PDF has extractable tables (not scanned images)

3. **Memory issues with large documents**
   - Reduce `max_images_per_document` and `max_tables_per_document`
   - Enable image compression: `config.storage.compress_images = True`

4. **Slow processing**
   - Enable parallel processing: `config.enable_parallel_processing = True`
   - Increase worker count: `config.max_workers = 8`

### Debug Mode

```python
# Enable detailed logging
config.debug_mode = True
config.log_processing_steps = True

# Check processing statistics
print(result["processing_stats"])
```

## Best Practices

1. **Document Preparation**
   - Use high-quality PDFs with extractable text
   - Ensure images are clear and well-formatted
   - Use structured tables with clear borders

2. **Configuration Tuning**
   - Start with default settings and adjust based on performance
   - Monitor memory usage for large document batches
   - Use appropriate vision model for your use case

3. **Performance Optimization**
   - Enable caching for repeated operations
   - Use parallel processing for large document sets
   - Filter irrelevant content (logos, decorative images)

4. **Error Handling**
   - Always check processing results for errors
   - Implement fallback strategies for critical operations
   - Monitor performance metrics regularly

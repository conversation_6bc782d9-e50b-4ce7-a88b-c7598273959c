"""
Test suite to verify the unified configuration interface fix
Ensures that the template safely handles missing RAG optimization data
"""

import pytest
import os
import sys
from unittest.mock import Mock, patch

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from app.utils.config import get_query_config_data, get_embedding_config_data

class TestUnifiedConfigFix:
    """Test that the unified configuration interface handles missing data gracefully"""
    
    def test_query_config_loads_without_errors(self):
        """Test that query configuration loads without template errors"""
        try:
            config_data = get_query_config_data()
            assert isinstance(config_data, dict)
            print("✅ Query configuration loaded successfully")
            
            # Verify RAG optimizations are present
            assert 'rag_optimizations' in config_data
            rag_opts = config_data['rag_optimizations']
            
            # Verify all expected sections are present
            expected_sections = ['query_caching', 'parallel_processing', 'adaptive_retrieval', 'performance_monitoring', 'cache_invalidation']
            for section in expected_sections:
                assert section in rag_opts, f"Missing section: {section}"
                
            print("✅ All RAG optimization sections present in query config")
            
        except Exception as e:
            pytest.fail(f"Query configuration failed to load: {e}")
    
    def test_embedding_config_loads_without_errors(self):
        """Test that embedding configuration loads without template errors"""
        try:
            config_data = get_embedding_config_data()
            assert isinstance(config_data, dict)
            print("✅ Embedding configuration loaded successfully")
            
            # Verify RAG optimizations are present
            assert 'rag_optimizations' in config_data
            rag_opts = config_data['rag_optimizations']
            
            # Verify all expected sections are present
            expected_sections = ['embedding_caching', 'semantic_chunking']
            for section in expected_sections:
                assert section in rag_opts, f"Missing section: {section}"
                
            print("✅ All RAG optimization sections present in embedding config")
            
        except Exception as e:
            pytest.fail(f"Embedding configuration failed to load: {e}")
    
    def test_template_safe_access_patterns(self):
        """Test that templates use safe access patterns for RAG optimization data"""
        # Read the query config template
        with open('app/templates/query_config_partial.html', 'r') as f:
            query_template = f.read()
        
        # Check for safe access patterns
        unsafe_patterns = [
            'rag_optimizations.query_caching.enabled',
            'rag_optimizations.parallel_processing.enabled',
            'rag_optimizations.adaptive_retrieval.enabled'
        ]
        
        safe_patterns = [
            'rag_optimizations and rag_optimizations.query_caching and rag_optimizations.query_caching.enabled',
            'rag_optimizations and rag_optimizations.parallel_processing and rag_optimizations.parallel_processing.enabled',
            'rag_optimizations and rag_optimizations.adaptive_retrieval and rag_optimizations.adaptive_retrieval.enabled'
        ]
        
        # Verify unsafe patterns are not present
        for pattern in unsafe_patterns:
            if pattern in query_template:
                # Check if it's part of a safe pattern
                safe_found = any(safe_pattern in query_template for safe_pattern in safe_patterns if pattern in safe_pattern)
                if not safe_found:
                    pytest.fail(f"Unsafe template access pattern found: {pattern}")
        
        print("✅ Query template uses safe access patterns")
        
        # Read the embedding config template
        with open('app/templates/embedding_config_partial.html', 'r') as f:
            embedding_template = f.read()
        
        # Check for safe access patterns in embedding template
        embedding_unsafe_patterns = [
            'rag_optimizations.embedding_caching.enabled',
            'rag_optimizations.semantic_chunking.enabled'
        ]
        
        embedding_safe_patterns = [
            'rag_optimizations and rag_optimizations.embedding_caching and rag_optimizations.embedding_caching.enabled',
            'rag_optimizations and rag_optimizations.semantic_chunking and rag_optimizations.semantic_chunking.enabled'
        ]
        
        # Verify unsafe patterns are not present
        for pattern in embedding_unsafe_patterns:
            if pattern in embedding_template:
                # Check if it's part of a safe pattern
                safe_found = any(safe_pattern in embedding_template for safe_pattern in embedding_safe_patterns if pattern in safe_pattern)
                if not safe_found:
                    pytest.fail(f"Unsafe template access pattern found in embedding template: {pattern}")
        
        print("✅ Embedding template uses safe access patterns")
    
    def test_default_values_in_templates(self):
        """Test that templates provide appropriate default values"""
        # Read the query config template
        with open('app/templates/query_config_partial.html', 'r') as f:
            query_template = f.read()
        
        # Check for default values
        default_value_patterns = [
            'else 1800',  # Default TTL
            'else 4',     # Default threshold and workers
            'else 0.67',  # Default simple k ratio
            'else 1.33',  # Default complex k ratio
            'else 6',     # Default min k
            'else 20'     # Default max k
        ]
        
        for pattern in default_value_patterns:
            assert pattern in query_template, f"Missing default value pattern: {pattern}"
        
        print("✅ Query template provides appropriate default values")
        
        # Read the embedding config template
        with open('app/templates/embedding_config_partial.html', 'r') as f:
            embedding_template = f.read()
        
        # Check for default values in embedding template
        embedding_default_patterns = [
            'else 86400',  # Default embedding cache TTL
            'else 5000',   # Default semantic threshold length
            'else 1',      # Default buffer size
            'else 95'      # Default breakpoint threshold
        ]
        
        for pattern in embedding_default_patterns:
            assert pattern in embedding_template, f"Missing default value pattern in embedding template: {pattern}"
        
        print("✅ Embedding template provides appropriate default values")
    
    @patch('app.utils.config.get_rag_config')
    def test_handles_missing_rag_config_gracefully(self, mock_get_rag_config):
        """Test that configuration handles missing RAG config gracefully"""
        # Mock RAG config to return None or raise an exception
        mock_get_rag_config.side_effect = Exception("RAG config not available")
        
        try:
            # This should not raise an exception due to the safe template patterns
            config_data = get_query_config_data()
            
            # The function should still return a dict, but without rag_optimizations
            assert isinstance(config_data, dict)
            print("✅ Query config handles missing RAG config gracefully")
            
        except Exception as e:
            # If it does raise an exception, it should be handled gracefully
            print(f"⚠️  Query config raised exception with missing RAG config: {e}")
            # This is acceptable as long as the template handles it
    
    def test_configuration_data_structure(self):
        """Test that configuration data has the expected structure"""
        query_data = get_query_config_data()
        
        # Verify basic structure
        assert 'rag_optimizations' in query_data
        rag_opts = query_data['rag_optimizations']
        
        # Verify query caching structure
        assert 'query_caching' in rag_opts
        query_caching = rag_opts['query_caching']
        assert 'enabled' in query_caching
        assert 'ttl' in query_caching
        assert 'use_redis' in query_caching
        assert 'redis_url' in query_caching
        
        # Verify parallel processing structure
        assert 'parallel_processing' in rag_opts
        parallel_proc = rag_opts['parallel_processing']
        assert 'enabled' in parallel_proc
        assert 'threshold' in parallel_proc
        assert 'max_workers' in parallel_proc
        
        # Verify adaptive retrieval structure
        assert 'adaptive_retrieval' in rag_opts
        adaptive_ret = rag_opts['adaptive_retrieval']
        assert 'enabled' in adaptive_ret
        assert 'simple_k_ratio' in adaptive_ret
        assert 'complex_k_ratio' in adaptive_ret
        assert 'min_k' in adaptive_ret
        assert 'max_k' in adaptive_ret
        
        print("✅ Query configuration data structure is correct")
        
        # Test embedding configuration structure
        embedding_data = get_embedding_config_data()
        
        assert 'rag_optimizations' in embedding_data
        embedding_rag_opts = embedding_data['rag_optimizations']
        
        # Verify embedding caching structure
        assert 'embedding_caching' in embedding_rag_opts
        embedding_caching = embedding_rag_opts['embedding_caching']
        assert 'enabled' in embedding_caching
        assert 'ttl' in embedding_caching
        
        # Verify semantic chunking structure
        assert 'semantic_chunking' in embedding_rag_opts
        semantic_chunking = embedding_rag_opts['semantic_chunking']
        assert 'enabled' in semantic_chunking
        assert 'threshold_length' in semantic_chunking
        assert 'buffer_size' in semantic_chunking
        assert 'breakpoint_threshold' in semantic_chunking
        assert 'fallback_to_sentence' in semantic_chunking
        
        print("✅ Embedding configuration data structure is correct")

class TestTemplateRendering:
    """Test that templates render correctly with the configuration data"""
    
    def test_query_template_renders_with_data(self):
        """Test that query template renders without errors"""
        from flask import Flask, render_template_string
        from app.utils.config import get_query_config_data
        
        app = Flask(__name__)
        
        with app.app_context():
            config_data = get_query_config_data()
            
            # Test a simple template snippet that uses RAG optimization data
            template_snippet = """
            {% if rag_optimizations and rag_optimizations.query_caching and rag_optimizations.query_caching.enabled %}
                <input type="checkbox" checked>
            {% else %}
                <input type="checkbox">
            {% endif %}
            """
            
            try:
                rendered = render_template_string(template_snippet, **config_data)
                assert '<input type="checkbox"' in rendered
                print("✅ Query template snippet renders correctly")
            except Exception as e:
                pytest.fail(f"Template rendering failed: {e}")
    
    def test_embedding_template_renders_with_data(self):
        """Test that embedding template renders without errors"""
        from flask import Flask, render_template_string
        from app.utils.config import get_embedding_config_data
        
        app = Flask(__name__)
        
        with app.app_context():
            config_data = get_embedding_config_data()
            
            # Test a simple template snippet that uses RAG optimization data
            template_snippet = """
            {% if rag_optimizations and rag_optimizations.embedding_caching and rag_optimizations.embedding_caching.enabled %}
                <input type="checkbox" checked>
            {% else %}
                <input type="checkbox">
            {% endif %}
            """
            
            try:
                rendered = render_template_string(template_snippet, **config_data)
                assert '<input type="checkbox"' in rendered
                print("✅ Embedding template snippet renders correctly")
            except Exception as e:
                pytest.fail(f"Template rendering failed: {e}")

if __name__ == "__main__":
    # Run the fix verification tests
    pytest.main([__file__, "-v"])

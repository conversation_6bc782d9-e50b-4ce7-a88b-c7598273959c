"""
LangChain MultimodalDocumentLoader

Custom LangChain document loader that integrates with the existing multimodal
document processing infrastructure to extract text, images, and tables from PDFs.
"""

import logging
import os
from typing import List, Dict, Any, Optional, Iterator
from pathlib import Path

from langchain_core.document_loaders import BaseLoader
from langchain_core.documents import Document

from app.services.multimodal_document_processor import get_multimodal_document_processor
from app.utils.performance_monitor import performance_monitor

logger = logging.getLogger(__name__)


class MultimodalDocumentLoader(BaseLoader):
    """
    LangChain document loader for multimodal content extraction.
    
    This loader uses the existing multimodal document processor to extract
    text, images, and tables from PDF documents and returns them as
    LangChain Document objects with proper metadata.
    """
    
    def __init__(
        self,
        file_path: str,
        category: Optional[str] = None,
        document_id: Optional[str] = None,
        include_images: bool = True,
        include_tables: bool = True,
        include_multimodal_chunks: bool = True
    ):
        """
        Initialize the multimodal document loader.
        
        Args:
            file_path: Path to the document file
            category: Category for organizing content
            document_id: Unique identifier for the document
            include_images: Whether to include image content in documents
            include_tables: Whether to include table content in documents
            include_multimodal_chunks: Whether to include multimodal page chunks
        """
        self.file_path = file_path
        self.category = category
        self.document_id = document_id
        self.include_images = include_images
        self.include_tables = include_tables
        self.include_multimodal_chunks = include_multimodal_chunks
        
        # Initialize the multimodal processor
        self.processor = get_multimodal_document_processor()
        
        # Validate file exists
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"Document file not found: {file_path}")
    
    @performance_monitor(track_memory=True, track_cpu=True, log_parameters=True, log_result_size=True)
    def load(self) -> List[Document]:
        """
        Load and process the document, returning LangChain Document objects.
        
        Returns:
            List of LangChain Document objects containing text, image, and table content
        """
        try:
            logger.info(f"Loading multimodal document: {self.file_path}")
            
            # Process document using existing multimodal processor
            result = self.processor.process_document(
                document_path=self.file_path,
                category=self.category,
                document_id=self.document_id
            )
            
            if not result:
                logger.warning(f"No content extracted from document: {self.file_path}")
                return []
            
            # Convert to LangChain documents
            documents = self._convert_to_langchain_documents(result)
            
            logger.info(f"Created {len(documents)} LangChain documents from {self.file_path}")
            return documents
            
        except Exception as e:
            logger.error(f"Error loading document {self.file_path}: {e}")
            return []
    
    def lazy_load(self) -> Iterator[Document]:
        """
        Lazy load implementation that yields documents one by one.
        
        Yields:
            LangChain Document objects
        """
        documents = self.load()
        for doc in documents:
            yield doc
    
    def _convert_to_langchain_documents(self, result: Dict[str, Any]) -> List[Document]:
        """
        Convert multimodal processing result to LangChain Document objects.
        
        Args:
            result: Result from multimodal document processor
            
        Returns:
            List of LangChain Document objects
        """
        documents = []
        
        try:
            # Base metadata for all documents
            base_metadata = {
                "source": self.file_path,
                "document_id": result.get("document_id"),
                "document_format": result.get("document_format"),
                "category": result.get("category"),
                "processing_timestamp": result.get("processing_timestamp")
            }
            
            # Process text content
            text_content = result.get("text_content", [])
            for i, text_item in enumerate(text_content):
                if isinstance(text_item, dict):
                    page_content = text_item.get("text", "")
                    page_num = text_item.get("page_num", i + 1)
                else:
                    page_content = str(text_item)
                    page_num = i + 1
                
                if page_content.strip():
                    metadata = {
                        **base_metadata,
                        "content_type": "text",
                        "page_num": page_num,
                        "chunk_index": i
                    }
                    
                    documents.append(Document(
                        page_content=page_content,
                        metadata=metadata
                    ))
            
            # Process images if enabled
            if self.include_images:
                images = result.get("images", [])
                for image_info in images:
                    documents.extend(self._create_image_documents(image_info, base_metadata))
            
            # Process tables if enabled
            if self.include_tables:
                tables = result.get("tables", [])
                for table_info in tables:
                    documents.extend(self._create_table_documents(table_info, base_metadata))
            
            # Process multimodal chunks if enabled
            if self.include_multimodal_chunks:
                multimodal_chunks = result.get("multimodal_chunks", [])
                for chunk in multimodal_chunks:
                    documents.extend(self._create_multimodal_chunk_documents(chunk, base_metadata))
            
            return documents
            
        except Exception as e:
            logger.error(f"Error converting to LangChain documents: {e}")
            return []
    
    def _create_image_documents(self, image_info: Dict[str, Any], base_metadata: Dict[str, Any]) -> List[Document]:
        """Create LangChain documents for image content."""
        documents = []
        
        try:
            # Create document for image caption if available
            caption = image_info.get("caption")
            if caption:
                metadata = {
                    **base_metadata,
                    "content_type": "image_caption",
                    "page_num": image_info.get("page_num"),
                    "image_index": image_info.get("image_index"),
                    "content_hash": image_info.get("content_hash"),
                    "image_format": image_info.get("format"),
                    "image_dimensions": image_info.get("dimensions"),
                    "bbox": image_info.get("bbox")
                }
                
                documents.append(Document(
                    page_content=f"Image caption: {caption}",
                    metadata=metadata
                ))
            
            # Create document for vision analysis if available
            vision_analysis = image_info.get("vision_analysis")
            if vision_analysis and isinstance(vision_analysis, dict):
                analysis_text = vision_analysis.get("description", "")
                if analysis_text:
                    metadata = {
                        **base_metadata,
                        "content_type": "image_analysis",
                        "page_num": image_info.get("page_num"),
                        "image_index": image_info.get("image_index"),
                        "content_hash": image_info.get("content_hash"),
                        "vision_model": vision_analysis.get("model"),
                        "confidence": vision_analysis.get("confidence")
                    }
                    
                    documents.append(Document(
                        page_content=f"Image analysis: {analysis_text}",
                        metadata=metadata
                    ))
            
            return documents
            
        except Exception as e:
            logger.error(f"Error creating image documents: {e}")
            return []
    
    def _create_table_documents(self, table_info: Dict[str, Any], base_metadata: Dict[str, Any]) -> List[Document]:
        """Create LangChain documents for table content."""
        documents = []
        
        try:
            # Create document for table markdown content
            markdown_content = table_info.get("markdown")
            if markdown_content:
                metadata = {
                    **base_metadata,
                    "content_type": "table_markdown",
                    "page_num": table_info.get("page_num"),
                    "table_index": table_info.get("table_index"),
                    "content_hash": table_info.get("content_hash"),
                    "num_rows": table_info.get("num_rows"),
                    "num_cols": table_info.get("num_cols"),
                    "extraction_method": table_info.get("extraction_method"),
                    "bbox": table_info.get("bbox")
                }
                
                documents.append(Document(
                    page_content=markdown_content,
                    metadata=metadata
                ))
            
            # Create document for table context if available
            context = table_info.get("context")
            if context:
                metadata = {
                    **base_metadata,
                    "content_type": "table_context",
                    "page_num": table_info.get("page_num"),
                    "table_index": table_info.get("table_index"),
                    "content_hash": table_info.get("content_hash")
                }
                
                documents.append(Document(
                    page_content=f"Table context: {context}",
                    metadata=metadata
                ))
            
            return documents
            
        except Exception as e:
            logger.error(f"Error creating table documents: {e}")
            return []
    
    def _create_multimodal_chunk_documents(self, chunk: Dict[str, Any], base_metadata: Dict[str, Any]) -> List[Document]:
        """Create LangChain documents for multimodal chunks."""
        documents = []
        
        try:
            # Combine all content types in the chunk
            content_parts = []
            
            # Add text content
            text_content = chunk.get("text_content", "")
            if text_content.strip():
                content_parts.append(f"Text: {text_content}")
            
            # Add image descriptions
            images = chunk.get("images", [])
            for i, image in enumerate(images):
                caption = image.get("caption", "")
                if caption:
                    content_parts.append(f"Image {i+1}: {caption}")
            
            # Add table content
            tables = chunk.get("tables", [])
            for i, table in enumerate(tables):
                markdown = table.get("markdown", "")
                if markdown:
                    content_parts.append(f"Table {i+1}:\n{markdown}")
            
            if content_parts:
                combined_content = "\n\n".join(content_parts)
                
                metadata = {
                    **base_metadata,
                    "content_type": "multimodal_chunk",
                    "chunk_type": chunk.get("chunk_type"),
                    "page_num": chunk.get("page_num"),
                    "content_types": chunk.get("content_types", []),
                    "num_images": len(images),
                    "num_tables": len(tables),
                    "spatial_relationships": chunk.get("spatial_relationships")
                }
                
                documents.append(Document(
                    page_content=combined_content,
                    metadata=metadata
                ))
            
            return documents
            
        except Exception as e:
            logger.error(f"Error creating multimodal chunk documents: {e}")
            return []

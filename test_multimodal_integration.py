#!/usr/bin/env python3
"""
Test script to verify multimodal processing integration
"""

import os
import sys
import time
import logging

# Add the app directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'app'))

def test_multimodal_status_service():
    """Test the multimodal status service functionality"""
    print("Testing multimodal status service...")
    
    try:
        from app.services.multimodal_status_service import multimodal_status_service
        
        # Get initial metrics
        initial_metrics = multimodal_status_service.get_metrics()
        print(f"Initial metrics: {initial_metrics}")
        
        # Record a test processing completion
        multimodal_status_service.record_processing_complete(
            document_name="test_document.pdf",
            processing_time=2.5,
            images_extracted=3,
            tables_extracted=1,
            text_chunks=15,
            success=True
        )
        
        # Get updated metrics
        updated_metrics = multimodal_status_service.get_metrics()
        print(f"Updated metrics: {updated_metrics}")
        
        # Verify the metrics were updated
        assert updated_metrics['total_documents'] == initial_metrics['total_documents'] + 1
        assert updated_metrics['total_text_chunks'] == initial_metrics['total_text_chunks'] + 15
        assert updated_metrics['total_images_extracted'] == initial_metrics['total_images_extracted'] + 3
        assert updated_metrics['total_tables_extracted'] == initial_metrics['total_tables_extracted'] + 1
        
        print("✅ Multimodal status service test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Multimodal status service test failed: {e}")
        return False

def test_status_api():
    """Test the status API endpoints"""
    print("\nTesting status API endpoints...")
    
    try:
        import requests
        
        # Test the metrics endpoint
        response = requests.get('http://localhost:5000/api/multimodal/metrics')
        if response.status_code == 200:
            metrics = response.json()
            print(f"API metrics response: {metrics}")
            print("✅ Metrics API test passed!")
        else:
            print(f"❌ Metrics API test failed: {response.status_code}")
            return False
            
        # Test the status endpoint
        response = requests.get('http://localhost:5000/api/multimodal/status')
        if response.status_code == 200:
            status = response.json()
            print(f"API status response keys: {list(status.keys())}")
            print("✅ Status API test passed!")
        else:
            print(f"❌ Status API test failed: {response.status_code}")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Status API test failed: {e}")
        print("Note: Make sure the Flask app is running on localhost:5000")
        return False

def main():
    """Run all tests"""
    print("🧪 Testing Multimodal Processing Integration\n")
    
    # Test 1: Status service functionality
    test1_passed = test_multimodal_status_service()
    
    # Test 2: API endpoints (optional, requires running server)
    test2_passed = test_status_api()
    
    print(f"\n📊 Test Results:")
    print(f"Status Service: {'✅ PASSED' if test1_passed else '❌ FAILED'}")
    print(f"API Endpoints: {'✅ PASSED' if test2_passed else '❌ FAILED'}")
    
    if test1_passed:
        print("\n🎉 Core integration is working! The multimodal status service can now track processing metrics.")
        print("\n📋 Next steps:")
        print("1. Upload a PDF through the dashboard")
        print("2. Check the multimodal dashboard for updated metrics")
        print("3. Verify that text chunks, images, and tables are properly counted")
    else:
        print("\n⚠️  Core integration has issues. Please check the error messages above.")

if __name__ == "__main__":
    main()

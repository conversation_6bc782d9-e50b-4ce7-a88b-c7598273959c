"""
RAG Pipeline Optimization Configuration
Centralized configuration for all RAG pipeline optimizations
"""

import os
from dataclasses import dataclass
from typing import Dict, Any, Optional

@dataclass
class RAGOptimizationConfig:
    """Configuration for RAG pipeline optimizations"""
    
    # Query Result Caching
    enable_query_caching: bool = True
    query_cache_ttl: int = 1800  # 30 minutes
    use_redis_cache: bool = True
    redis_url: str = "redis://localhost:6379/1"
    
    # Embedding Caching
    enable_embedding_caching: bool = True
    embedding_cache_ttl: int = 86400  # 24 hours
    
    # Parallel Document Scoring
    enable_parallel_scoring: bool = True
    parallel_scoring_threshold: int = 4  # Minimum docs to use parallel processing
    max_parallel_workers: int = 4
    
    # Adaptive Retrieval
    enable_adaptive_retrieval: bool = True
    simple_query_k_ratio: float = 0.67  # k multiplier for simple queries (≤3 words)
    complex_query_k_ratio: float = 1.33  # k multiplier for complex queries (>10 words)
    min_adaptive_k: int = 6
    max_adaptive_k: int = 20
    
    # Semantic Chunking
    prefer_semantic_chunking: bool = True
    semantic_threshold_length: int = 5000  # Min chars for semantic chunking
    semantic_buffer_size: int = 1
    semantic_breakpoint_threshold: int = 95
    fallback_to_sentence_chunking: bool = True
    
    # Performance Monitoring
    enable_performance_monitoring: bool = True
    log_cache_performance: bool = True
    log_adaptive_retrieval: bool = True
    log_parallel_processing: bool = True
    
    # Cache Invalidation
    auto_invalidate_on_new_docs: bool = True
    invalidation_pattern: str = "*query:{category}:*"
    
    @classmethod
    def from_env(cls) -> 'RAGOptimizationConfig':
        """Create configuration from environment variables"""
        return cls(
            # Query Caching
            enable_query_caching=os.getenv('RAG_ENABLE_QUERY_CACHING', 'true').lower() == 'true',
            query_cache_ttl=int(os.getenv('RAG_QUERY_CACHE_TTL', '1800')),
            use_redis_cache=os.getenv('RAG_USE_REDIS_CACHE', 'true').lower() == 'true',
            redis_url=os.getenv('RAG_REDIS_URL', 'redis://localhost:6379/1'),
            
            # Embedding Caching
            enable_embedding_caching=os.getenv('RAG_ENABLE_EMBEDDING_CACHING', 'true').lower() == 'true',
            embedding_cache_ttl=int(os.getenv('RAG_EMBEDDING_CACHE_TTL', '86400')),
            
            # Parallel Processing
            enable_parallel_scoring=os.getenv('RAG_ENABLE_PARALLEL_SCORING', 'true').lower() == 'true',
            parallel_scoring_threshold=int(os.getenv('RAG_PARALLEL_THRESHOLD', '4')),
            max_parallel_workers=int(os.getenv('RAG_MAX_PARALLEL_WORKERS', '4')),
            
            # Adaptive Retrieval
            enable_adaptive_retrieval=os.getenv('RAG_ENABLE_ADAPTIVE_RETRIEVAL', 'true').lower() == 'true',
            simple_query_k_ratio=float(os.getenv('RAG_SIMPLE_QUERY_K_RATIO', '0.67')),
            complex_query_k_ratio=float(os.getenv('RAG_COMPLEX_QUERY_K_RATIO', '1.33')),
            min_adaptive_k=int(os.getenv('RAG_MIN_ADAPTIVE_K', '6')),
            max_adaptive_k=int(os.getenv('RAG_MAX_ADAPTIVE_K', '20')),
            
            # Semantic Chunking
            prefer_semantic_chunking=os.getenv('RAG_PREFER_SEMANTIC_CHUNKING', 'true').lower() == 'true',
            semantic_threshold_length=int(os.getenv('RAG_SEMANTIC_THRESHOLD_LENGTH', '5000')),
            semantic_buffer_size=int(os.getenv('RAG_SEMANTIC_BUFFER_SIZE', '1')),
            semantic_breakpoint_threshold=int(os.getenv('RAG_SEMANTIC_BREAKPOINT_THRESHOLD', '95')),
            fallback_to_sentence_chunking=os.getenv('RAG_FALLBACK_TO_SENTENCE', 'true').lower() == 'true',
            
            # Performance Monitoring
            enable_performance_monitoring=os.getenv('RAG_ENABLE_PERFORMANCE_MONITORING', 'true').lower() == 'true',
            log_cache_performance=os.getenv('RAG_LOG_CACHE_PERFORMANCE', 'true').lower() == 'true',
            log_adaptive_retrieval=os.getenv('RAG_LOG_ADAPTIVE_RETRIEVAL', 'true').lower() == 'true',
            log_parallel_processing=os.getenv('RAG_LOG_PARALLEL_PROCESSING', 'true').lower() == 'true',
            
            # Cache Invalidation
            auto_invalidate_on_new_docs=os.getenv('RAG_AUTO_INVALIDATE_ON_NEW_DOCS', 'true').lower() == 'true',
            invalidation_pattern=os.getenv('RAG_INVALIDATION_PATTERN', '*query:{category}:*')
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary"""
        return {
            'query_caching': {
                'enabled': self.enable_query_caching,
                'ttl': self.query_cache_ttl,
                'use_redis': self.use_redis_cache,
                'redis_url': self.redis_url
            },
            'embedding_caching': {
                'enabled': self.enable_embedding_caching,
                'ttl': self.embedding_cache_ttl
            },
            'parallel_processing': {
                'enabled': self.enable_parallel_scoring,
                'threshold': self.parallel_scoring_threshold,
                'max_workers': self.max_parallel_workers
            },
            'adaptive_retrieval': {
                'enabled': self.enable_adaptive_retrieval,
                'simple_k_ratio': self.simple_query_k_ratio,
                'complex_k_ratio': self.complex_query_k_ratio,
                'min_k': self.min_adaptive_k,
                'max_k': self.max_adaptive_k
            },
            'semantic_chunking': {
                'enabled': self.prefer_semantic_chunking,
                'threshold_length': self.semantic_threshold_length,
                'buffer_size': self.semantic_buffer_size,
                'breakpoint_threshold': self.semantic_breakpoint_threshold,
                'fallback_to_sentence': self.fallback_to_sentence_chunking
            },
            'performance_monitoring': {
                'enabled': self.enable_performance_monitoring,
                'log_cache': self.log_cache_performance,
                'log_adaptive': self.log_adaptive_retrieval,
                'log_parallel': self.log_parallel_processing
            },
            'cache_invalidation': {
                'auto_invalidate': self.auto_invalidate_on_new_docs,
                'pattern': self.invalidation_pattern
            }
        }

# Global configuration instance
_rag_config: Optional[RAGOptimizationConfig] = None

def get_rag_config() -> RAGOptimizationConfig:
    """Get the global RAG optimization configuration"""
    global _rag_config
    if _rag_config is None:
        _rag_config = RAGOptimizationConfig.from_env()
    return _rag_config

def reload_rag_config():
    """Reload configuration from environment variables"""
    global _rag_config
    _rag_config = RAGOptimizationConfig.from_env()
    return _rag_config
